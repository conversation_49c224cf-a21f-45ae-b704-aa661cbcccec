import moment from 'moment';

export function today(): string {
  const date = new Date();
  return (
    date.getFullYear().toString() +
    '-' +
    ('0' + (date.getMonth() + 1).toString()).substr(-2) +
    '-' +
    ('0' + date.getDate().toString()).substr(-2)
  );
}

export function diffDay(date1: string | Date, date2?: string | Date): number {
  const seconds = diffSeconds(date1, date2);
  return Math.ceil(seconds / 36 / 24 / 10) / 10;
}

export function diffDay_Seconds(seconds: any): number {
  return Math.ceil(seconds / 36 / 24 / 10) / 10;
}

export function diffHours(date1: string | Date, date2?: string | Date): number {
  const seconds = diffSeconds(date1, date2);
  return Math.ceil(seconds / 36) / 100;
}

export function diffHours_Seconds(seconds: any): number {
  return Math.ceil(seconds / 36) / 100;
}

export function diffMinutes(date1: string | Date, date2?: string | Date): number {
  const seconds = diffSeconds(date1, date2);
  return Math.ceil(seconds / 0.6) / 100;
}

export function diffMinutes_Seconds(seconds: any): number {
  return Math.ceil(seconds / 0.6) / 100;
}

export function diffSeconds(date1: string | Date, date2?: string | Date): number {
  const seconds = moment(date1).diff(date2 || moment(), 'seconds');
  return Math.abs(seconds);
}

class DateHelper {
  get currentTime(): string {
    const date = new Date();
    const seperator1 = '-';
    const seperator2 = ':';
    let month: any = date.getMonth() + 1;
    let strDate: any = date.getDate();
    let hours: any = date.getHours();
    let minutes: any = date.getMinutes();
    let seconds: any = date.getSeconds();
    if (month >= 1 && month <= 9) {
      month = '0' + month;
    }
    if (strDate >= 0 && strDate <= 9) {
      strDate = '0' + strDate;
    }
    if (hours >= 0 && hours <= 9) {
      hours = '0' + hours;
    }
    if (minutes >= 0 && minutes <= 9) {
      minutes = '0' + minutes;
    }
    if (seconds >= 0 && seconds <= 9) {
      seconds = '0' + seconds;
    }
    const currentdate =
      date.getFullYear() + seperator1 + month + seperator1 + strDate + ' ' + hours + seperator2 + minutes + seperator2 + seconds;
    return currentdate;
  }
  dateFormat(date: string | Date, format?: string): string {
    return moment(date).format(format ? format : 'YYYY-MM-DD');
  }
  momentAdd(date: string | Date, count: any, type: string, format?: string): string {
    return moment(date).add(count, type).format(format ? format : 'YYYY-MM-DD');
  }
  momentFormat(data: string | string[]) {
    let result = null;
    if (Array.isArray(data)) {
      result = data.map((m: string, i: number) => moment(m));
    } else if (data) {
      result = moment(data);
    }
    return result;
  }
  calculationEnddate(startDate: string, endDate: string, holiday: any[]) {
    if (startDate && endDate) {
      const newStartDate = this.dateFormat(startDate);
      const newEndDate = this.dateFormat(endDate);
      let startTime = this.dateFormat(startDate, 'HH:mm:ss');
      let endTime = this.dateFormat(endDate, 'HH:mm:ss');
      let realStartDate = this.dateFormat(startDate);
      let realEndDate = this.dateFormat(endDate);
      // 有效节假日
      const newHoliday = (holiday || []).filter((f: any) => this.dateFormat(f.holidayDate) >= newStartDate
        && this.dateFormat(f.holidayDate) <= newEndDate);
      if (newHoliday && newHoliday.length > 0) {
        // 判断开始与结束是否包含再里面
        if (newStartDate === this.dateFormat(newHoliday[0].holidayDate)) {
          realStartDate = moment(newStartDate).add(1, 'd').format('YYYY-MM-DD');
          startTime = '00:00:00';
        }
        if (newEndDate === this.dateFormat(newHoliday[newHoliday.length - 1].holidayDate)) {
          realEndDate = moment(newEndDate).subtract(1, 'd').format('YYYY-MM-DD');
          endTime = '59:59:59';
        }
        const startIndex = newHoliday.findIndex((f: any) => this.dateFormat(f.holidayDate) === newStartDate);
        if (startIndex > -1) {
          newHoliday.splice(startIndex, 1);
        }
        const endIndex = newHoliday.findIndex((f: any) => this.dateFormat(f.holidayDate) === newEndDate);
        if (endIndex > -1) {
          newHoliday.splice(endIndex, 1);
        }
        if (newHoliday.length > 0) {
          realEndDate = moment(realEndDate).subtract(newHoliday.length, 'd').format('YYYY-MM-DD');
        }
        if (realStartDate <= realEndDate) {
          return {
            startDate: `${realStartDate} ${startTime}`,
            endDate: `${realEndDate} ${endTime}`
          };
        }
        return {
          startDate: newStartDate,
          endDate: newStartDate
        };
      }
    }
    return {
      startDate: startDate,
      endDate: endDate
    };
  }
}

export const dateHelper = new DateHelper();
