import { AES, enc, mode, pad } from 'crypto-js';

class EncryptHelper {
  encryptByEnAES(data: string, key: string = 'nPwWc9Ci7bbTKNTdBf2vPvfgN3NAjptd', iv: string = 'Nti2ip8yibzHeoBx'): string {
    const tmpAES = AES.encrypt(data, enc.Utf8.parse(key), {
      iv: enc.Utf8.parse(iv),
      mode: mode.CBC,
      padding: pad.Pkcs7
    });
    return tmpAES.toString();
  }

  base64(data: string) {
    return enc.Base64.stringify(enc.Utf8.parse(data));
  }
}

export const encryptHelper = new EncryptHelper();
