import { Subject, Subscription } from 'rxjs';
import { VanToast } from 'vant/types/toast';
import { Toast } from 'vant';

class GlobalLoadingHelper {
  private loadingModal: VanToast | undefined;

  create() {
    if (!this.loadingModal) {
      this.loadingModal = Toast.loading({
        duration: 0,
        overlay: true,
        message: '加载中...',
        forbidClick: true,
        loadingType: 'spinner',
      });
    }
  }

  destroy() {
    if (this.loadingModal) {
      this.loadingModal.clear();
      this.loadingModal = undefined;
    }
  }
}

export const globalLoadingHelper = new GlobalLoadingHelper();
