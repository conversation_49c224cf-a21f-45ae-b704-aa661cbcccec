class GuidHelper {
  generate(): string {
    return `${this.s4()}${this.s4()}-${this.s4()}-${this.s4()}-${this.s4()}-${this.s4()}${this.s4()}${this.s4()}`;
  }

  isGuidFormat(guid: string): boolean {
    const reg = new RegExp(/^[0-9a-zA-Z]{8}-[0-9a-zA-Z]{4}-[0-9a-zA-Z]{4}-[0-9a-zA-Z]{4}-[0-9a-zA-Z]{12}$/);
    return reg.test(guid);
  }

  empty(): string {
    return `00000000-0000-0000-0000-000000000000`;
  }
  private s4(): string {
    return Math.floor((1 + Math.random()) * 0x10000)
      .toString(16)
      .substring(1);
  }
}

export const guidHelper = new GuidHelper();
