import { AxiosError } from 'axios';
import { Observable, Subject } from 'rxjs';
import { map } from 'rxjs/operators';
import { Toast } from 'vant';
import { Settings } from '../defines';

class HttpErrorHandler {
  private error$ = new Subject<any>();

  throw(error: AxiosError<any>, uuid?: string) {
    let errorMessage;
    let donotAlert = false;
    if (error.response) {
      errorMessage = error.response.data;
      if (error.response.status === 401) {
        donotAlert = true;
      } else if (error.response.status === 308) {
        donotAlert = true;
      } else if (error.response.status === 440) {
        errorMessage = '登录超时，请重新登录。';
        const loginUrl = error.response.data;
        Toast.fail({
          message: errorMessage,
          overlay: true,
          forbidClick: true
        });
        if (loginUrl.startsWith('http://') || loginUrl.startsWith('https://')) {
          window.location.href = loginUrl;
        } else {
          window.location.href = `/${Settings.AppKey}/` + loginUrl;
        }
      }
    } else {
      errorMessage = error.message;
    }
    if (!donotAlert && error.message !== 'Request aborted') {
      Toast.fail({
        message: errorMessage,
        overlay: true,
        forbidClick: true
      });
    }
    (error as any).uuid = uuid;
    this.error$.next(error);
  }

  handle(uuid?: string): Observable<AxiosError<any>> {
    return this.error$.pipe(
      map(error => {
        if (!error || error.uuid !== uuid) {
          return undefined;
        } else {
          return error;
        }
      })
    );
  }
}

export const httpErrorHandler = new HttpErrorHandler();
