import { httpHelper } from './http-helper';
import { Route } from 'vue-router';

import { authService } from '@/MobileStandard/services/auth';
import { menuService } from '@/MobileStandard/services/menu';

export function routerGuard(to: Route, from: Route, next: (option?: any) => void) {
  authService.getAuthState().subscribe(
    () => {
      // 没有配置低代码菜单默认跳转至todo页面
      if (to.path === '/home' && !(menuService.modules && menuService.modules.length > 0)) {
        next({ path: '/todo', replace: true });
      } else {
        next();
      }
    },
    error => {
      if (error && error.response) {
        // 服务端会对 sso 接入进行直接跳转，所以此处可以使用 Router 进行跳转
        if (error.response.status === 401) {
          next(error.response.data);
        } else if (error.response.status === 308) {
          const url = error.response.data as string;
          if (url.indexOf('api-login') > 0) {
            const uid = to.query['uid'];
            const token = to.query['token'];
            const timestamp = to.query['timestamp'];
            if (uid && token && timestamp) {
              const param = {
                uid: uid,
                token: token,
                timestamp: timestamp
              };
              httpHelper.post('/auth/mobile-state', param).subscribe(() => {
                next();
              });
            }
          } else {
            window.location.href = url;
          }
        }
      }
    }
  );
}
