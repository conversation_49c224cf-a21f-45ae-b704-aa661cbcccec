import lodash from 'lodash';
import { Subject } from 'rxjs';

class SubjectHelper {
    subs$: any = {};
    setSubject(key: string) {
        if (!lodash.has(this.subs$, key)) {
            this.subs$[key] = new Subject<any>();
        }
    }
    getSubject(key: string) {
        if (!lodash.has(this.subs$, key)) {
            this.subs$[key] = new Subject<any>();
        }
        return this.subs$[key];
    }

    subjectUnsubscribe(key: string) {
        if (this.subs$[key]) {
            this.subs$[key].unsubscribe();
        }
    }
    subjectUnsubscribeAll() {
        lodash.forEach(lodash.keys(this.subs$), (s: string) => {
            this.subs$[s].unsubscribe();
        });
    }
    hasObservers(s: string) {
        return this.subs$[s] && this.subs$[s].observers && this.subs$[s].observers.length > 0;
    }
}

export const subjectHelper = new SubjectHelper();
