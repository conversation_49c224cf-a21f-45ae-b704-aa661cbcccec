/**  水印添加方法  */

const setWatermark = (str1: any, str2: any) => {
  const id = '1.23452384164.123412415';

  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id) as HTMLInputElement);
  }

  const can = document.createElement('canvas');
  // 设置canvas画布大小
  can.width = 200;
  can.height = 160;

  const cans = can.getContext('2d')  as any;
  // const cans: CanvasRenderingContext2D | null;
  cans.rotate(-25 * Math.PI / 180); // 水印旋转角度
  cans.font = '12px Vedana';
  cans.fillStyle = '#666666';
  cans.textAlign = 'center';
  cans.textBaseline = 'Middle';
  cans.fillText(str1, can.width / 3, can.height); // 水印在画布的位置x，y轴
  cans.fillText(str2, can.width / 3, can.height + 18);

  const div = document.createElement('div');
  div.id = id;
  div.style.pointerEvents = 'none';
  div.style.top = '40px';
  div.style.left = '0px';
  div.style.opacity = '0.15';
  div.style.position = 'fixed';
  div.style.zIndex = '100000';
  div.style.width = document.documentElement.clientWidth + 'px';
  div.style.height = document.documentElement.clientHeight  + 'px';
  div.style.background = 'url(' + can.toDataURL('image/png') + ') left top repeat';
  document.body.appendChild(div);
  return id;
};

// 添加水印方法
export function setWaterMark(str1: any, str2: any) {
  let id = setWatermark(str1, str2);
  if (document.getElementById(id) === null) {
    id = setWatermark(str1, str2);
  }
}

// 移除水印方法
export function removeWatermark() {
  const id = '1.23452384164.123412415';
  if (document.getElementById(id) !== null) {
    document.body.removeChild(document.getElementById(id) as HTMLInputElement);
  }
}
