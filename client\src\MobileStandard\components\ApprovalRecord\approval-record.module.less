.sheet{
    height: 100%;
    max-height: 100% !important;
    .top{
        width: 100%;
        position: absolute;
        top: 0px;
        .title{
            text-align: center;
            vertical-align: middle;
            line-height: 44px;
            height: 44px;
            font-size: 18px;
            font-weight: 500;
            .closeIcon{
                position: absolute !important;
                left: 20px;
                top: 10px;
            }
        }
    }
    .main_content{
        position: absolute;
        width: 100%;
        top: 50px;
        bottom: 0px;
        padding: 10px 0;
        overflow: auto;
    }
}
.step{
    &::after {
        border: none !important;
    }
}
.stepContent{
    background-color: #ffffff;
    // border-radius: 4px;
    border: 1px solid #ecedef;
    // font-size: 12px;
}
.paralleStepContent{
    background-color: #ffffff;
    border: 1px solid #ecedef;
    margin-bottom: 5px;
    padding-bottom: 5px;
    padding-right: 5px;
}
.newNodeContent{
    border: 1px solid #009a3e;
}
.arrowLeft{

}
.stepTitle{
    min-height: 30px;
    background-color: #f6f7f9;
    // border-radius: 4px;
    line-height: 30px;
    padding-left: 5px;
    padding-right: 5px;
    display: flex;
    .stepName{
        flex: 1;
        color: #45575E;
        word-break: break-all;
    }
    .stepOperate{
        color: #009a3e;
        span{
            margin-left: 5px;
        }
    }
}
.extend_operate{
    width: 100%;
    text-align: right;
    padding-top: 5px;
    span{
        color: #009a3e;
        margin-left: 5px;
    }
}
.newNodeTitle{
    background-color: #A9CAFF;
}
.stepUser{
    // min-height: 30px;
    padding: 5px;
    border-radius: 4px;
    width: inherit;
    .userTagContent{
        // margin: 10px 0;
        position: relative;
    }
    .userTagContent_horizontal{
        display: inline-block;
    }
    .resolverName{
        display: block;
        // margin-bottom: 10px;
        color: #999999;
    }
    .userContent{
        padding-top: 5px;
    }
}
.addUserBackground{
    border-radius: 50%;
    height: 25px;
    width: 25px;
    background-color: #ffffff;
    box-shadow: 1px 1px 10px 0px #f0f2f5;
    text-align: center;
    position: absolute;
    top: 10px;
    right: 16px;
}
.stepNumber{
    height: 12px;
    width: 12px;
    border: 1px solid #ffffff;
    color: #FFFFFF;
    text-align: center;
    // font-size: 14px;
    line-height: 12px;
}
.startStep{
    border-radius: 50%;
    background-color: #DDDEE0;
}
.addStep{
    padding-top: 5px;
    // font-size: 12px;
    .addIcon{
        border-radius: 50%;
        height: 20px;
        width: 15px;
        background-color: #ffffff;
        box-shadow: 1px 1px 10px 0px #f0f2f5;
        text-align: center;
        position: absolute;
        left: -17px;
        // bottom: 8px;
        // line-height: 25px;
        z-index: 1;
        i{
            font-size: 12px;
        }
    }
    .addRemark{
        color: #009a3e;
    }
}
.stepState{
    padding: 2px 5px;
    border-radius: 4px;
}
.stepFirstRow{
    // padding-top: 10px;
    // padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
    span{
        color: #333333;
    }
}
.stepSecondRow{
    // padding-bottom: 10px;
    display: flex;
    justify-content: space-between;
}
.stepThirdRow{
    // padding-bottom: 10px;
    color: #333333;
    white-space: pre-wrap;
}

@process-start: #2998fe;
@process-ready: #2998fe;
@process-processing: #f5b25e;
@process-approved: #41ba42;
@process-refused: #9c9c9c;
@process-canceled: #9c9c9c;
@process-rejected: #9c9c9c;
@process-todo: #9c9c9c;
@process-parallel: #8B50DF;
@process-suspended: #9c9c9c;
.tagColorIcon(@tag-color) {
    color: @tag-color;
}
.tagColor(@tag-color) {
    color: @tag-color !important;
    background-color: tint(@tag-color, 80%);
}
.startIcon {
    .tagColorIcon(@process-start);
}
.readyIcon {
    .tagColorIcon(@process-ready);
}
.processingIcon {
    .tagColorIcon(@process-processing);
}
.approvedIcon {
    .tagColorIcon(@process-approved);
}
.refusedIcon {
    .tagColorIcon(@process-refused);
}
.canceledIcon {
    .tagColorIcon(@process-canceled);
}
.rejectedIcon {
    .tagColorIcon(@process-rejected);
}
.todoIcon {
    .tagColorIcon(@process-todo);
}
.parallelIcon {
    .tagColorIcon(@process-parallel);
}
.suspendedIcon {
    .tagColorIcon(@process-suspended);
}
.start {
    .tagColor(@process-start);
}
.ready {
    .tagColor(@process-ready);
}
.processing {
    .tagColor(@process-processing);
}
.approved {
    .tagColor(@process-approved);
}
.refused {
    .tagColor(@process-refused);
}
.canceled {
    .tagColor(@process-canceled);
}
.rejected {
    .tagColor(@process-rejected);
}
.todo {
    .tagColor(@process-todo);
}
.suspended {
    .tagColor(@process-suspended);
}
