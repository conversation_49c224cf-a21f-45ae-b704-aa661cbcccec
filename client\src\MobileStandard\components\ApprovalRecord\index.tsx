import { Component, Vue, Emit, Prop, Watch } from 'vue-property-decorator';
import styles from './approval-record.module.less';
import { InstanceParamMixin } from '@/MobileStandard/mixins';
import { commonService } from '@/MobileStandard/services/common';
import { ApproveState } from '@/MobileStandard/services/common/common.types';

@Component({
    mixins: [InstanceParamMixin],
})
export class ApprovalRecord extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop() formData!: any;
    private approvalRecordItems: any = [];
    @Watch('visible')
    visibleChange(newVal: boolean) {
        if (newVal) {
            this.approvalRecordItems = [];
            this.onLoadData();
        }
    }
    @Emit('close')
    onClose(visible: boolean) { }
    private getStateIcon(step: any) {
        let iconName = 'checked';
        switch (step.state) {
            case ApproveState.start:
            case ApproveState.approved:
            case ApproveState.canceled:
            case ApproveState.refused:
            case ApproveState.rejected:
                iconName = 'checked';
                break;
            default:
                iconName = 'clock';
                break;
        }
        return <div class={styles.stepNumber}>
            <van-icon
                name={iconName}
                class={styles[step.isParallel && step.paralleSteps && step.paralleSteps.length > 0 ?
                    'parallelIcon' : (step.state + 'Icon')]}
                size='12'></van-icon>
        </div>;
    }
    private getComment(step: any) {
        const comment: any = [];
        step.commentTextArray.map((m: any) => {
            const c = <span>{m}</span>;
            return comment.push(c);
        });
        return comment;
    }
    private getState(step: any) {
        return <span class={styles.stepState + ' ' + styles[step.state]}>{step.stateName}</span>;
    }
    private isShowResolveUser(record: any) {
        return record.state !== 'todo' && record.detailStatus !== 'handover'
            && record.userId && record.resolveUserId && record.userId !== record.resolveUserId;
    }
    private onLoadData() {
        commonService.getApproval(this.number, this.formData).subscribe(s => {
            this.approvalRecordItems = s.data;
        });
    }
    private getApprovalStep(m: any, i: number) {
        m.state = m.status || 'todo';
        m.stepType = m.nodeType;
        return <van-step class={styles.step}>
            {
                m.isParallel && m.paralleSteps && m.paralleSteps.length > 0 ? <div>
                    {
                        m.paralleSteps.map((p: any, pi: number) => {
                            return <div class={styles.paralleStepContent}>
                                <van-steps direction='vertical' active={-1}>
                                    {
                                        p.map((pp: any, ppi: number) => {
                                            return this.getApprovalStep(pp, ppi);
                                        })
                                    }
                                </van-steps>
                            </div>;
                        })
                    }
                </div> :
                    <div class={styles.stepContent}>
                        <div class={styles.stepTitle}>
                            <div class={styles.arrowLeft}></div>
                            <span class={styles.stepName}>{m.name}</span>
                        </div>
                        {
                            m.stepType !== 'end' ? <div class={styles.stepUser}>
                                {
                                    m.tasks.map((a: any, ai: number) => {
                                        a.state = a.status || 'todo';
                                        a.resolveTime = a.finishTime;
                                        a.stayDuration = (Math.floor((Number(a.elapsedTime) / 3600) * 100) / 100).toString();
                                        a.commentTextArray = a.comments || [];
                                        a.stateName = a.detailStatusName || a.statusName;
                                        return <div class={(ai > 0 ? (styles.userContent + ' van-hairline--top') : '')}>
                                            <div class={styles.stepFirstRow}>
                                                <span>{
                                                    this.isShowResolveUser(a) ?
                                                        `${a.resolveUserName}${this.$l.getLocale('fields.agent')}${a.userName}${this.$l.getLocale('fields.approve')}`
                                                        : a.userName ? a.userName : a.status === 'todo' ?
                                                            '' : this.$l.getLocale('fields.nothing')}</span>
                                                {!a.state || a.state === 'todo' ? '' : this.getState(a)}
                                            </div>
                                            {
                                                a.activityResolverName ?
                                                    <div class={styles.stepFirstRow}>
                                                        <span>
                                                            {`(${a.activityResolverName})`}
                                                        </span>
                                                    </div> :
                                                    ''
                                            }
                                            <div class={styles.stepFirstRow}>
                                                <span>
                                                    {
                                                        a.detailStatus === 'extrainsert' || a.detailStatus === 'extraappend' ?
                                                            `  加签给  ${a.toUser.map((u: any) => u.userName).join(',')}` : ''
                                                    }
                                                    {
                                                        a.detailStatus === 'handover' ?
                                                            `  转交给  ${a.toUser.map((u: any) => u.userName).join(',')}` : ''
                                                    }
                                                    {
                                                        a.detailStatus === 'rejectactivity' || a.detailStatus === 'rejectactivitydirect' ?
                                                            `  退回到  ${a.toActivityName}` : ''
                                                    }
                                                </span>
                                            </div>
                                            <div class={styles.stepSecondRow}>
                                                <span>{a.resolveTime}</span>
                                                {
                                                    (a.state !== ApproveState.approved
                                                        && a.state !== ApproveState.rejected)
                                                        || a.stayDuration === undefined ?
                                                        '' : <span>
                                                            {this.$l.getLocale('fields.approvalDuration')}
                                                            {a.stayDuration}{this.$l.getLocale('fields.hour')}
                                                        </span>
                                                }
                                            </div>
                                            <div class={styles.stepThirdRow}>
                                                {this.getComment(a)}
                                            </div>
                                        </div>;
                                    })
                                }
                            </div> : ''
                        }
                    </div>
            }
            <template slot='inactive-icon'>
                {
                    this.getStateIcon(m.tasks && m.tasks.length > 0 ? m.tasks[m.tasks.length - 1] : m)
                }
            </template>
        </van-step>;
    }
    created() {
    }
    render() {
        return <van-action-sheet v-model={this.visible}
            round={false}
            class={styles.sheet}
            on-click-overlay={this.onClose}>
            <div class={styles.top}>
                <div class={styles.title + ' van-hairline--bottom'}>
                    <van-icon name='arrow-down' size='20'
                        class={styles.closeIcon}
                        on-click={this.onClose}></van-icon>
                    {this.$l.getLocale('fields.history')}
                </div>
            </div>
            <div class={styles.main_content}>
                <van-steps direction='vertical' active={-1}>
                    {
                        this.approvalRecordItems.map((m: any, i: number) => {
                            return this.getApprovalStep(m, i);
                        })
                    }
                </van-steps>
            </div>
        </van-action-sheet>;
    }
}
