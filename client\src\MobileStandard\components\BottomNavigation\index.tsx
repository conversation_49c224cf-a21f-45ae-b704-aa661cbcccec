import { Component, Vue, Emit, Prop, Watch } from 'vue-property-decorator';
import styles from './bottom-navigation.module.less';

@Component
export class BottomNavigation extends Vue {
    @Prop({ default: 'map' }) currentPage!: string;
    private page = 'map';
    created() {
        this.page = this.currentPage;
    }
    render() {
        return (
            <van-tabbar class={styles.tabbar}
                v-model={this.page}
                inactive-color='#CCCCCC'
                active-color='#009a3e'>
                {/* <van-tabbar-item
                    name='workbench'
                    icon-prefix='icon'
                    icon={this.page === 'workbench' ? 'tabbar_icon1_on' : 'tabbar_icon12'}
                    to='/workbench'>
                    工作台
                    </van-tabbar-item> */}
                <van-tabbar-item
                    name='todo'
                    icon-prefix='icon'
                    icon={this.page === 'todo' ? 'tabbar_icon2_on' : 'tabbar_icon2'}
                    to='/todo'>
                    {
                        this.$l.getLocale('fields.todoCenter')
                    }
                    </van-tabbar-item>
                <van-tabbar-item
                    name='map'
                    icon-prefix='icon'
                    icon={this.page === 'map' ? 'tabbar_icon3_on' : 'tabbar_icon3'}
                    to='/map'>
                     {
                        this.$l.getLocale('fields.processStart')
                    }
                    </van-tabbar-item>
                {/* <van-tabbar-item
                    name='my'
                    icon-prefix='icon'
                    icon={this.page === 'my' ? 'tabbar_icon4_on' : 'tabbar_icon4'}
                    to='/my'>
                    我的
                    </van-tabbar-item> */}
            </van-tabbar>
        );
    }
}
