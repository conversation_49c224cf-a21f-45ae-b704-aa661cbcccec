.sheet{
    height: 100%;
    max-height: 100% !important;
    background-color: #F0F2F5 !important;
    .top{
        width: 100%;
        position: absolute;
        top: 0px;
        background-color: #ffffff;
        .title{
            text-align: center;
            vertical-align: middle;
            line-height: 44px;
            height: 44px;
            font-size: 18px;
            font-weight: 500;
            .closeIcon{
                position: absolute !important;
                left: 20px;
                top: 10px;
            }
        }
    }
    .main_content{
        position: absolute;
        width: 100%;
        top: 50px;
        bottom: 0px;
        padding: 5px 0;
        overflow: auto;
        .example{
            margin: 0 10px 10px 10px;
            border-radius: 4px;
            background-color: #FFFFFF;
            padding: 5px;
            position: relative;
            display: flex;
            align-items: center;
            .content{
                margin-left: 10px;
                flex: 1;
            }
            .row{
                height: 33px;
                line-height: 33px;
                font-size: 15px;
                font-weight: 400;
                color: #999999;
                display: flex;
            }
        }
    }
}
