import { Component, Vue, Emit, Prop, Watch } from 'vue-property-decorator';
import styles from './cc-record.module.less';
import { InstanceParamMixin } from '@/MobileStandard/mixins';
import { ccRecordService } from './service';

@Component({
    mixins: [InstanceParamMixin],
})
export class CcRecord extends Vue {
    @Prop({ default: false }) visible!: boolean;
    private ccRecordItems: any = [];
    private current = 0;
    private loading = false;
    private finished = false;
    @Watch('visible')
    visibleChange(newVal: boolean) {
        if (newVal) {
            this.ccRecordItems = [];
            this.current = 0;
            this.loading = false;
            this.finished = false;
            this.onLoadData();
        }
    }
    @Emit('close')
    onClose(visible: boolean) { }
    private onLoadData() {
        this.current += 1;
        const params = {
            'user-id': this.authId,
            'business-number': this.number,
            'page-index': this.current,
            'page-size': 10
        };
        ccRecordService.getCCHistory(params).subscribe((s: any) => {
            this.ccRecordItems = [...this.ccRecordItems, ...s.items];
            this.loading = false;
            if (s.items.length === 0 || s.items.length < 10) {
                this.finished = true;
            }
        });
    }
    private getTag(isRead: boolean) {
        return isRead ? <van-tag color='blue'>{this.$l.getLocale('fields.read')}</van-tag> :
            <van-tag color='red'>{this.$l.getLocale('fields.unread')}</van-tag>;
    }
    created() {
    }
    render() {
        return <van-action-sheet v-model={this.visible}
            round={false}
            class={styles.sheet}
            on-click-overlay={this.onClose}>
            <div class={styles.top}>
                <div class={styles.title + ' van-hairline--bottom'}>
                    <van-icon name='arrow-down' size='20'
                        class={styles.closeIcon}
                        on-click={this.onClose}></van-icon>
                    {this.$l.getLocale('fields.ccRecord')}
                </div>
            </div>
            <div class={styles.main_content}>
                {
                    this.ccRecordItems.length > 0 ? <van-list v-model={this.loading}
                        finished={this.finished}
                        on-load={this.onLoadData}
                        finished-text={this.$l.getLocale('fields.noMore')}
                        offset={0}
                        immediate-check={false}
                        class='van-clearfix'>
                        {
                            this.ccRecordItems.map((d: any) => {
                                return <div class={styles.example}>
                                    <div class={styles.content}>
                                        <div class={styles.row}>
                                            <div style='width:50%'>{this.$l.getLocale('fields.ccPerson')}:
                                                {(d.operatorType === 0 || d.operatorType === null) ? this.$l.getLocale('fields.systemCC')
                                                    : d.operatorName}</div>
                                            <div style='width:50%'>{this.$l.getLocale('fields.toReadPerson')}: {d.userName}</div>
                                        </div>
                                        <div class={styles.row}>
                                            <div>{this.$l.getLocale('fields.ccTime')}: {d.arriveTime}</div>
                                        </div>
                                        <div class={styles.row}>
                                            <div>{this.$l.getLocale('fields.status')}: {this.getTag(d.isRead)}</div>
                                        </div>
                                    </div>
                                </div>;
                            })
                        }
                    </van-list> : <van-empty description={this.$l.getLocale('fields.emptyData')} />
                }
            </div>
        </van-action-sheet>;
    }
}
