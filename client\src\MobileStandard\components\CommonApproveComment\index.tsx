import { Component, Vue, Emit, Prop, Watch } from 'vue-property-decorator';
import { commonApproveCommentService } from './service';
import styles from './common-approve-comment.module.less';

@Component
export class CommonApproveComment extends Vue {
    private options: any = [];
    @Emit('change')
    onChange(value: string) {
    }
    created() {
        commonApproveCommentService.get().subscribe(data => this.options = data);
    }
    render() {
        return (
            <div class={styles.comment}>
                <div class={styles.title}>{this.$l.getLocale('fields.commonComment')}</div>
                <div class={styles.content}>
                    {this.options.map((tag: any, i: number) => {
                        return <van-button
                            size='small'
                            type='default'
                            on-click={() => this.onChange(tag.content)}>
                            {tag.subject}
                        </van-button>;
                    })}
                </div>
            </div>
        );
    }
}
