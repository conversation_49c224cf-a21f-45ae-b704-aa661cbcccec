import { Observable, of, zip } from 'rxjs';
import { httpHelper } from '@/MobileStandard/common/utils';
import { CommonApprovalCommentDto } from './types';

class CommonApproveCommentService {
    get(): Observable<CommonApprovalCommentDto[]> {
        return httpHelper.get('/api/process/v1/common-approval-comment');
      }
}

export const commonApproveCommentService = new CommonApproveCommentService();
