export function BMapLoader(ak: string) {
    const win: any = window;
    return new Promise((resolve, reject) => {
        if (win.BMapGL) {
            resolve(win.BMapGL);
        } else {
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.src = `https://api.map.baidu.com/api?type=webgl&v=1.0&ak=${ak}&callback=initBMap`;
            script.async = true;
            script.onerror = reject;
            document.head.appendChild(script);
        }
        win.initBMap = () => {
            resolve(win.BMapGL);
        };
    });
}
