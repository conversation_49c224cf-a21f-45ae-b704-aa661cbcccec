import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator';
import lodash from 'lodash';
import { ControlAnchor, StatusCodes } from './type';
import { BMapLoader } from './BMapLoader';
import { formInfoService } from '../DetailPage/FormInfo/service';
import { guidHelper } from '@/MobileStandard/common/utils';
@Component
export class CustMap extends Vue {
    @Prop() value!: any;
    @Prop() config!: any;
    private id = guidHelper.generate();
    private localCata = { lng: '', lat: '', address: '' };
    private ak = '';
    private map: any = null;
    private control: any = null;
    private refreshMap = lodash.debounce(this.getLocation, 500);
    @Watch('config.disabled')
    disabled(newVal: any) {
        this.refreshLoacalControl(newVal, this.config.editable);
    }
    @Watch('config.editable')
    dislplayMode(newVal: any) {
        this.refreshLoacalControl(this.config.disabled, newVal);
    }
    @Watch('value')
    valueChange(newVal: any) {
        if (this.map && newVal !== JSON.stringify(this.localCata)) {
            if (newVal) {
                BMapLoader(this.ak).then((bmap: any) => {
                    const valObject = JSON.parse(newVal);
                    this.addMarker(bmap, this.map, new bmap.Point(valObject.lng, valObject.lat));
                    this.localCata.lng = valObject.lng;
                    this.localCata.lat = valObject.lat;
                    this.localCata.address = valObject.address;
                });

            } else if (!newVal) {
                BMapLoader(this.ak).then((bmap: any) => {
                    // 空值时设置为默认位置当前城市
                    const myCity = new bmap.LocalCity();
                    myCity.get((result: any) => {
                        const point = new bmap.Point(result.center.lng, result.center.lat);
                        this.map.centerAndZoom(point, 13);
                        this.getAddress(bmap, point);
                        this.initVal(result.center.lng, result.center.lat, this.localCata.address);
                    });
                });
            }
        }
    }
    @Watch('config.display', { immediate: true })
    display(newVal: any) {
        if (newVal) {
            this.initMap();
        }
    }
    @Emit('change')
    onValueChange(value: any) {
    }
    private refreshLoacalControl(disabled: any, editable: any) {
        if (this.map) {
            if ((disabled || !editable) && this.control) {
                BMapLoader(this.ak).then((bmap: any) => {
                    this.map.removeControl(this.control);
                });
            } else if (!disabled && !this.control) {
                BMapLoader(this.ak).then((bmap: any) => {
                    this.setLocalControl(bmap, this.map);
                });
            }
        }
    }
    private onChange(val: any) {
        this.onValueChange(val);
    }
    private initVal(localLng: string, localLat: string, localAddress: string) {
        const val = { lng: localLng, lat: localLat, address: localAddress };
        this.localCata.lng = val.lng;
        this.localCata.lat = val.lat;
        this.localCata.address = val.address;
        this.onChange(JSON.stringify(val));
    }
    private initMap() {
        this.$nextTick(() => {
            BMapLoader(this.ak).then((bmap: any) => {
                if (document.getElementById(this.id)) {
                    const map = new bmap.Map(this.id);
                    this.map = map;
                    map.enableScrollWheelZoom(true);
                    // 默认加载当前城市地图
                    const myCity = new bmap.LocalCity();
                    myCity.get((result: any) => {
                        const point = new bmap.Point(result.center.lng, result.center.lat);
                        map.centerAndZoom(point, 13);
                        if (this.config.editable) {
                            this.initVal(result.center.lng, result.center.lat, this.localCata.address);
                            this.getLocation(bmap, map);
                        }
                        if (this.config.editable && !this.config.disabled) {
                            this.setLocalControl(bmap, map);
                        }
                        if (this.value) {
                            this.valueChange(this.value);
                        }
                    });
                }
            });
        });
    }
    private setLocalControl(bmap: any, map: any) {
        const control = new bmap.Control();
        control.defaultAnchor = ControlAnchor.BMAP_ANCHOR_TOP_RIGHT;
        control.defaultOffset = new bmap.Size(20, 20);
        control.initialize = () => {
            const div = document.createElement('div');
            div.style.width = '39px';
            div.style.height = '39px';
            div.style.borderRadius = '4px';
            div.style.background = 'rgb(255, 255, 255)';
            div.style.position = 'absolute';
            div.style.zIndex = '10';
            div.style.boxShadow = 'rgb(107 120 137 / 20%) 0px 2px 6px 0px, rgb(91 98 107 / 8%) 0px 4px 10px 0px';
            div.style.cursor = 'pointer';
            const local = document.createElement('div');
            local.style.position = 'absolute';
            local.style.left = '50%';
            local.style.top = '50%';
            local.style.marginLeft = '-10px';
            local.style.marginTop = '-10px';
            local.style.width = '20px';
            local.style.height = '20px';
            let background = 'url("https://webmap0.bdimg.com/res/litemapapi/v1d1/images/loc_new.png?20190314")';
            background += ' 0px 0px / 48px 116px';
            local.style.background = background;
            div.appendChild(local);
            div.onclick = () => {
                this.refreshMap(bmap, map);
            };
            div.onmousedown = () => {
                div.style.background = 'rgb(155, 155, 155)';
            };
            div.onmouseup = () => {
                div.style.background = 'rgb(255, 255, 255)';
            };
            map.getContainer().appendChild(div);
            return div;
        };
        map.addControl(control);
        this.control = control;
    }
    private getLocation(bmap: any, map: any) {
        const geolocation = new bmap.Geolocation();
        geolocation.getCurrentPosition((position: any) => {
            if (geolocation.getStatus() === StatusCodes.BMAP_STATUS_SUCCESS) {
                console.log(position);
                this.addMarker(bmap, map, position.point);
                if (position.address) {
                    this.localCata.address = `${position.address.province}${position.address.city}${position.address.district}${position.address.street}${position.address.street_number}`;
                } else {
                    this.getAddress(bmap, position.point);
                }
                this.initVal(position.point.lng, position.point.lat, this.localCata.address);
            } else {
                this.$toast({
                    type: 'fail',
                    forbidClick: true,
                    message: '定位失败'
                });
            }
        }, {
            enableHighAccuracy: true,
            SDKLocation: true
        });
    }
    private addMarker(bmap: any, map: any, point: any) {
        map.clearOverlays();
        map.addOverlay(new bmap.Marker(point));
        map.panTo(point);
    }
    private getAddress(bmap: any, point: any) {
        this.localCata.address = '';
        const geoc = new bmap.Geocoder();
        geoc.getLocation(point, (result: any) => {
            console.log(result);
            if (result) {
                this.localCata.address = result.address;
            }
        });
    }
    created(): void {
        this.ak = formInfoService.getConfig('baiduak');
    }
    render() {
        return (
            <div style='width:100%;'>
                <span>{this.localCata.address}</span>
                <div id={this.id}
                    style={`width:100%;height:${this.config.height ? this.config.height : 300}px;`}
                ></div>
            </div>
        );
    }
}
