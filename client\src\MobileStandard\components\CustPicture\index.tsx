import { Settings } from '@/MobileStandard/common/defines';
import { guidHelper, httpHelper } from '@/MobileStandard/common/utils';
import lodash from 'lodash';
import { Component, Vue, Emit, Prop, Watch } from 'vue-property-decorator';

@Component
export class CustPicture extends Vue {
    @Prop() deletable!: boolean;
    @Prop() showUpload!: boolean;
    @Prop() value!: any;
    @Prop() formUploadApi!: any;
    @Prop() formDownloadApi!: any;
    @Emit('change')
    handleChange(files: any) { }
    private pictureBeforeRead(file: any) {
        if (!lodash.startsWith(file.type, 'image/')) {
            this.$toast({ type: 'fail', overlay: true, forbidClick: true, message: '文件格式错误' });
            return false;
        }
        return true;
    }
    private pictureAfterRead(file: any) {
        if (lodash.isArray(file)) {
            lodash.forEach(file, (f: any) => {
                f.file['id'] = guidHelper.generate();
                f.status = 'uploading';
                f.message = '上传中...';
                this.pictureUpload(f);
            });
        } else {
            file.file['id'] = guidHelper.generate();
            file.status = 'uploading';
            file.message = '上传中...';
            this.pictureUpload(file);
        }
    }
    private pictureUpload(file: any) {
        const formData = new FormData();
        formData.append('file', file.file);
        httpHelper.post(this.formUploadApi, formData, {}, { loading: false, }).subscribe((res: any) => {
            if (res) {
                file.message = '';
                file.status = '';
                const list = this.value ? JSON.parse(this.value) : [];
                const newFile = {
                    uid: file.file.id,
                    name: file.file.name,
                    filePath: res.filePath,
                    idocId: res.idocId,
                    status: 'done'
                };
                list.push(newFile);
                this.handleChange(list.length > 0 ? JSON.stringify(list) : '');
            } else {
                file.status = 'failed';
                file.message = '上传失败';
            }
        });
    }
    private pictureValue(files: any) {
        if (files) {
            return this.getFileUrl(lodash.isArray(files) ? files : JSON.parse(files));
        }
        return [];
    }
    private getFileUrl(list: any) {
        return lodash.map(list, (m: any) => {
            return lodash.has(lodash.keys(m), 'url') ? { ...m, isImage: true } :
                { ...m, url: this.previewUrl(m), isImage: true };
        });
    }
    private previewUrl(file: any) {
        const idocId = file.response ? file.response.idocId : file.idocId;
        const filePath = file.response ? file.response.filePath : file.filePath;
        const pars = `?IdocId=${idocId}&FilePath=${filePath}`;
        return `/${Settings.UrlKey}${this.formDownloadApi}${pars}`;
    }
    private pictureDelete(pa1: any, pa2: any) {
        if (this.value) {
            const list = JSON.parse(this.value);
            list.splice(pa2.index, 1);
            this.handleChange(list.length > 0 ? JSON.stringify(list) : '');
        }
    }
    created() {
    }
    render() {
        return (
            <van-uploader
                preview-image={true}
                preview-full-image={false}
                fileList={this.pictureValue(this.value)}
                deletable={this.deletable}
                show-upload={this.showUpload}
                before-read={(e: any) => this.pictureBeforeRead(e)}
                after-read={(e: any) => this.pictureAfterRead(e)}
                on-delete={(pa1: any, pa2: any) => this.pictureDelete(pa1, pa2)}
            ></van-uploader>
        );
    }
}
