import lodash from 'lodash';
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';

@Component
export class IndexMixins extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop({ default: false }) multiple!: boolean;
    @Prop({ default: false }) onlyCheckedEndNode!: boolean;
    @Prop({ default: false }) search!: boolean;
    @Prop() treeChildrenField!: string;
    @Prop() treeParentField!: string;
    @Prop() treeTitleField!: string;
    @Prop() treeKeyField!: string;
    @Prop() treeData!: any;
    @Prop() value!: any;
    searchText = '';
    searchObj: any = [];
    parentId = null;
    copyVisible = false;
    selectObjs: any = [];
    breadcrumb: any = [{ label: this.$l.getLocale('fields.topLevel'), value: '' }];
    showSelectObjs = false;
    @Watch('visible')
    visibleChange(newVal: boolean) {
        this.copyVisible = this.visible;
        if (newVal) {
            this.parentId = null;
            this.searchText = '';
            this.selectObjs = [];
            this.breadcrumb = [{ label: this.$l.getLocale('fields.topLevel'), value: '' }];
            this.searchObj = this.treeSelectOptions(null);
            if (this.value) {
                lodash.forEach(this.value.split(';'), (v: any) => {
                    const it = lodash.find(this.treeData, (t: any) => t[this.treeKeyField] === v);
                    if (it) {
                        this.selectObjs.push({
                            value: v,
                            label: it[this.treeTitleField]
                        });
                    }
                });
                this.setObjChecked();
            }
        }
    }
    @Emit('close')
    treeSelectClose() { }
    @Emit('ok')
    treeSelectOk(value: any, label: any) { }
    onSearch() {
        this.breadcrumb = [{ label: this.$l.getLocale('fields.topLevel'), value: '' }];
        if (this.searchText) {
            this.searchObj = lodash.map(lodash.filter(this.treeData || [], (dd: any) => {
                return lodash.lowerCase(dd[this.treeTitleField]).indexOf(lodash.lowerCase(this.searchText)) > -1
                    || lodash.lowerCase(dd[this.treeKeyField]).indexOf(lodash.lowerCase(this.searchText)) > -1;
            }), (d: any) => {
                let disabled = false;
                if (this.onlyCheckedEndNode && lodash.filter(this.treeData, (td: any) => {
                    if (d[this.treeParentField]) {
                        return td[this.treeChildrenField] === d[this.treeParentField];
                    } else {
                        return !td[this.treeChildrenField];
                    }
                }).length > 0) {
                    disabled = true;
                }
                const arrs = [d[this.treeTitleField]];
                this.recursionParent(d[this.treeChildrenField], arrs);
                return {
                    label: arrs.length === 1 ? d[this.treeTitleField] : `${d[this.treeTitleField]}(${lodash.join(lodash.reverse(arrs), '-')})`,
                    value: d[this.treeKeyField],
                    checked: false,
                    childCount: 0,
                    disabled: disabled
                };
            });
        } else {
            this.parentId = null;
            this.onLoadData();
        }
    }
    onLoadData() {
        this.searchObj = this.treeSelectOptions(this.parentId);
        this.setObjChecked();
    }
    treeSelectOptions(parentId: any) {
        const cascader = lodash.map(lodash.filter(this.treeData || [], (dd: any) => {
            if (parentId) {
                return dd[this.treeChildrenField] === parentId;
            } else {
                return !dd[this.treeChildrenField];
            }
        }), (d: any) => {
            const resultNode: any = {
                label: d[this.treeTitleField],
                value: d[this.treeKeyField],
                id: d[this.treeParentField],
                checked: false,
                childCount: lodash.filter(this.treeData, (td: any) => {
                    if (d[this.treeParentField]) {
                        return td[this.treeChildrenField] === d[this.treeParentField];
                    } else {
                        return !td[this.treeChildrenField];
                    }
                }).length
            };
            resultNode['disabled'] = this.onlyCheckedEndNode && resultNode['childCount'] > 0 ? true : false;
            return resultNode;
        });

        return cascader;
    }
    onBreadcrumbClick(item: any, index: number) {
        this.breadcrumb.splice(index + 1, this.breadcrumb.length - index);
        this.parentId = item.value;
        this.onLoadData();
    }
    onNextOrg(m: any) {
        this.parentId = m.id;
        this.breadcrumb.push({ label: m.label, value: m.value });
        this.onLoadData();
    }
    onSelectObjChange(checked: boolean, item: any) {
        if (this.multiple) {
            const selectObjIndex = this.selectObjs.findIndex((f: any) => f.value === item.value);
            if (checked && selectObjIndex === -1) {
                this.selectObjs.push({
                    value: item.value,
                    label: item.label
                });
            } else if (!checked && selectObjIndex > -1) {
                this.selectObjs.splice(selectObjIndex, 1);
            }
        } else {
            if (checked) {
                this.selectObjs = [];
                this.selectObjs.push({
                    value: item.value,
                    label: item.label
                });
            } else {
                const selectObjIndex = this.selectObjs.findIndex((f: any) => f.value === item.value);
                if (selectObjIndex > -1) {
                    this.selectObjs.splice(selectObjIndex, 1);
                }
            }
        }
        this.setObjChecked();
    }
    setObjChecked() {
        this.searchObj.map((m: any) => {
            m['checked'] = false;
            if (this.selectObjs.findIndex((f: any) => f.value === m.value) > -1) {
                m['checked'] = true;
            }
        });
    }
    onSelectObjClick() {
        this.showSelectObjs = true;
    }
    onSelectObjClose() {
        this.setObjChecked();
        this.showSelectObjs = false;
    }
    onObjRemove(index: number) {
        this.selectObjs.splice(index, 1);
        this.setObjChecked();
    }
    onSaveClick() {
        if (this.multiple) {
            this.treeSelectOk(this.selectObjs, lodash.map(this.selectObjs, (s: any) => s.label));
        } else {
            if (this.selectObjs.length > 0) {
                this.treeSelectOk(this.selectObjs[0].value, [this.selectObjs[0].label]);
            } else {
                this.treeSelectOk('', []);
            }
        }
    }
    onCancelClick() {
        this.treeSelectClose();
    }
    recursionParent(current: any, arrs: any) {
        const parent = lodash.find(this.treeData || [], (dd: any) => dd[this.treeParentField] === current);
        if (parent) {
            arrs.push(parent[this.treeTitleField]);
            this.recursionParent(parent[this.treeChildrenField], arrs);
        }
    }
    created() {
    }
}
