import { mixins } from 'vue-class-component';
import { Component } from 'vue-property-decorator';
import { IndexMixins } from './IndexMixins';
import styles from './tree-select.module.less';

@Component
export class CustTreeSelect extends mixins(IndexMixins) {
    render() {
        return (
            <van-action-sheet v-model={this.copyVisible}
                round={false}
                class={styles.sheet}
                on-click-overlay={this.treeSelectClose}>
                <div class={styles.top}>
                    <form action='/'>
                        <van-search
                            v-model={this.searchText}
                            shape='round'
                            placeholder={this.$l.getLocale('placeholders.searchKw')}
                            on-search={this.onSearch}
                            on-clear={this.onLoadData}
                            class='van-hairline--bottom'
                        />
                    </form>
                    <div class={styles.breadcrumb}>
                        {
                            this.breadcrumb.map((m: any, i: number) => {
                                return <div class={styles.breadcrumbItem +
                                    (i < this.breadcrumb.length - 1 ? ' ' + styles.breadcrumbColor : '')}
                                    on-click={() => this.onBreadcrumbClick(m, i)}>
                                    {
                                        m.label
                                    }
                                    {
                                        i < this.breadcrumb.length - 1 ? ' >' : ''
                                    }
                                </div>;
                            })
                        }
                    </div>
                </div>
                <div class={styles.content}>
                    <van-cell-group>
                        {
                            this.searchObj.map((m: any, i: number) => {
                                return <van-cell title={m.label}>
                                    <template slot='title'>
                                        <van-checkbox
                                            name={m.value}
                                            disabled={m.disabled}
                                            v-model={m.checked}
                                            on-change={(checked: boolean) => this.onSelectObjChange(checked, m)}
                                        >
                                            {m.label}
                                        </van-checkbox>
                                    </template>
                                    <template slot='right-icon'>
                                        {
                                            m.childCount > 0 ? <div class={styles.clusterContent} on-click={() => this.onNextOrg(m)}>
                                                <van-icon name='cluster-o' class={styles.clusterIcon} />
                                                <span>{this.$l.getLocale('fields.lowerLevel')}</span>
                                            </div> : ''
                                        }
                                    </template>
                                </van-cell>;
                            })
                        }
                    </van-cell-group>
                </div>
                <div class={styles.buttons}>
                    <div class={styles.checked} on-click={this.onSelectObjClick}>
                        <span>{this.$l.getLocale('fields.selected')}:{this.selectObjs.length}</span>
                        {
                            this.selectObjs.length > 0 ?
                                <van-icon name='arrow-up' /> : ''
                        }
                    </div>
                    <van-button type='default' color='#ffffff'
                        block
                        class={styles.button + ' ' + styles.cancel}
                        on-click={this.onCancelClick}>{this.$l.getLocale('buttons.cancel')}</van-button>
                    <van-button type='default' color='#0e9266'
                        block
                        class={styles.button}
                        on-click={this.onSaveClick}>{this.$l.getLocale('buttons.ok')}</van-button>
                </div>
                <van-action-sheet
                    v-model={this.showSelectObjs}
                    round={false}
                    class={styles.selectSheet}>
                    <div class={styles.top}>
                        <div class={styles.activityInfo}>
                            <van-icon name='arrow-down' size='20'
                                class={styles.activityInfoClose}
                                on-click={this.onSelectObjClose}></van-icon>
                            {this.$l.getLocale('fields.selected')}
                        </div>
                        <van-cell-group border={false}
                            class={styles.selectContent}>
                            {
                                this.selectObjs.map((m: any, i: number) => {
                                    return <van-cell
                                        title={m.label}
                                        icon='contact'
                                    >
                                        <template slot='right-icon'>
                                            <i class={`iconfont icon-btn_deletelist ${styles.remove}`}
                                                on-click={() => this.onObjRemove(i)} />
                                        </template>
                                    </van-cell>;
                                })
                            }
                        </van-cell-group>
                    </div>
                </van-action-sheet>
            </van-action-sheet>
        );
    }
}
