.sheet{
    height: 100%;
    max-height: 88% !important;
}
.selectSheet{
    height: 70%;
    max-height: 70% !important;
}
.top{
    width: 100%;
    height: 100%;
    :global(.van-search) {
        padding: 10px 5px;
    }
    :global(.van-search__content) {
        :global(.van-icon-search) {
            font-size: 12px;
        }

        :global(.van-field__control) {
            font-size: 12px;
            color: #999999;
            font-weight: 400px;
        }
    }
}
.activityInfo{
    text-align: center;
    vertical-align: middle;
    line-height: 44px;
    height: 44px;
}
.breadcrumb{
    display: flex;
    white-space: nowrap;
    background-color: #fff;
    line-height: 40px;
    height: 40px;
    font-size: 12px;
    color: #999999;
    font-weight: 400;
    width: 100%;
    overflow: auto;
    .breadcrumbItem{
        padding-left: 5px;
        padding-right: 5px;
        cursor: pointer;
    }
    .breadcrumbColor{
        color: #2165D9;
        font-weight: bold;
    }
    :first-child{
        padding-left: 12px;
    }
    :last-child{
        padding-right: 12px;
        cursor: none;
    }
}
.content{
    width: 100%;
    overflow: auto;
    overflow-x: hidden;
    position: absolute;
    top: 94px;
    bottom: 60px;
}
.selectContent{
    width: 100%;
    overflow: auto;
    position: fixed;
    height: 100%;
}
.buttons{
    background-color: #fff;
    border-top: 1px solid #ebedf0;
    display: flex;
    width: 100%;
    line-height: 60px;
    // height: 60px;
    color: #2165D9;
    position: absolute;
    bottom: 0px;
    border-radius: 4px;
    padding: 10px 0px 15px;
}
.checked{
    display: inline-block;
    margin-left: 10px;
    flex: 1px;
}
.button{
    width: 100px !important;
    margin-right: 10px !important;
    margin-top: 5px !important;
}
.cancel{
    color: #2165D9 !important;
    box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.09);
}
.clusterContent{
    display: flex;
    color: #2165D9;
    cursor: pointer;
    border-left: 1px solid #ebedf0;
}
.clusterIcon{
    line-height: inherit !important;
    padding-left: 5px;
    padding-right: 5px;
}
.remove{
    color: #ee0a24;
    width: 20px;
    text-align: center;
}
