.sheet{
    height: 100%;
    max-height: 100% !important;
}
.selectSheet{
    height: 100%;
    max-height: 100% !important;
}
.top{
    width: 100%;
    position: absolute;
    top: 0px;
}
.activityInfo{
    text-align: center;
    vertical-align: middle;
    line-height: 44px;
    height: 44px;
}
.activityInfoClose{
    position: absolute !important;
    left: 20px;
    top: 10px;
}
.breadcrumb{
    display: flex;
    white-space: nowrap;
    background-color: #fff;
    line-height: 40px;
    height: 40px;
    font-size: 14px;
    color: #999999;
    font-weight: 400;
    padding-left: 12px;
    padding-right: 12px;
    width: 100%;
    overflow: auto;
}
.breadcrumbItem{
    padding-left: 5px;
    padding-right: 5px;
}
.breadcrumbColor{
    color: #0091ff;
}
.content{
    width: 100%;
    overflow: auto;
    overflow-x: hidden;
    position: absolute;
    top: 84px;
    bottom: 45px;
}
.selectContent{
    width: 100%;
    overflow: auto;
    position: absolute;
    top: 44px;
}
.buttons{
    background-color: #fff;
    border-top: 1px solid #ebedf0;
    display: flex;
    width: 100%;
    line-height: 45px;
    height: 45px;
    color: #0091ff;
    position: absolute;
    bottom: 0px;
}
.checked{
    display: inline-block;
    margin-left: 10px;
    flex: 1px;
}
.button{
    width: 100px !important;
    margin-right: 10px !important;
    margin-top: 5px !important;
}
.clusterContent{
    display: flex;
    color: #0091ff;
    cursor: pointer;
    border-left: 1px solid #ebedf0;
}
.clusterIcon{
    line-height: inherit !important;
    padding-left: 5px;
    padding-right: 5px;
}
.remove{
    color: #ee0a24;
}
