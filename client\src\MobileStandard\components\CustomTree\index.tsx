import { httpHelper } from '@/MobileStandard/common/utils';
import { Component, Vue, Emit, Prop, Watch } from 'vue-property-decorator';
import styles from './custom-tree.module.less';

@Component
export class CustomTree extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop() default!: any;
    @Prop() treeApiPath!: string;
    @Prop() treeSourceParentKey!: string;
    @Prop() treeSourceLabelKey!: string;
    @Prop() treeSourceValueKey!: string;
    @Prop() treeHasChild!: string;
    private breadcrumb: any = [{ label: this.$l.getLocale('fields.topLevel'), value: '' }];
    private searchTree: any = [];
    private selectTree: any = [];
    private showSelectTree = false;
    private parentId = '';
    @Watch('visible')
    visibleChange(newVal: boolean) {
        if (newVal) {
            this.parentId = '';
            this.searchTree = [];
            this.selectTree = [];
            this.breadcrumb = [{ label: this.$l.getLocale('fields.topLevel'), value: '' }];
            if (this.default && this.default.length > 0) {
                this.selectTree = this.default;
            }
            this.onLoadData();
        }
    }
    @Emit('close')
    customTreeClose() { }
    @Emit('ok')
    customTreeOk(items: any) { }
    private onLoadData() {
        const search: any = {};
        search[this.treeSourceParentKey] = this.parentId;
        const hasChild = this.treeHasChild
            ? this.treeHasChild.split(';')[
            this.treeHasChild.split(';').length - 1
            ]
            : '';
        const labelKey = this.treeSourceLabelKey
            ? this.treeSourceLabelKey.split(';')[
            this.treeSourceLabelKey.split(';').length - 1
            ]
            : '';
        const valueKey = this.treeSourceValueKey
            ? this.treeSourceValueKey.split(';')[
            this.treeSourceValueKey.split(';').length - 1
            ]
            : '';
        httpHelper.post('/api/api-centre' + this.treeApiPath, search, {}, { loading: false }).subscribe((res: any) => {
            if (res && res.Data) {
                this.searchTree = res.Data.map((r: any) => {
                    if (labelKey) {
                        r['label'] = r[labelKey];
                    } else {
                        r['label'] = '';
                    }
                    if (valueKey) {
                        r['value'] = r[valueKey];
                    } else {
                        r['value'] = '';
                    }
                    if (hasChild && r[hasChild] > 0) {
                        r['hasChild'] = true;
                    } else {
                        r['hasChild'] = false;
                    }
                    r['checked'] = false;
                    return r;
                });
            }
            this.setOrgChecked();
        });
    }
    private onBreadcrumbClick(item: any, index: number) {
        this.breadcrumb.splice(index + 1, this.breadcrumb.length - index);
        this.parentId = item.value;
        this.onLoadData();
    }
    private onNextOrg(item: any) {
        this.parentId = item.value;
        this.breadcrumb.push({ label: item.label, value: item.value });
        this.onLoadData();
    }
    private onSelectTreeChange(checked: boolean, item: any) {
        if (checked) {
            this.selectTree.splice(0, 1, {
                label: item.label,
                value: item.value
            });
        } else {
            const ind = this.selectTree.findIndex((f: any) => f.value === item.value);
            if (ind > -1) {
                this.selectTree = [];
            }
        }
    }
    private onSelectTreeClick() {
        this.showSelectTree = true;
    }
    private onSelectOrgClose() {
        this.showSelectTree = false;
    }
    private onTreeRemove(index: number) {
        this.selectTree = [];
        this.setOrgChecked();
    }
    private onSaveClick() {
        this.customTreeOk(this.selectTree);
    }
    private setOrgChecked() {
        this.searchTree.map((m: any) => {
            m['checked'] = false;
            if (this.selectTree.findIndex((f: any) => f.value === m.value) > -1) {
                m['checked'] = true;
            }
        });
    }
    created() {
    }
    render() {
        return (
            <van-action-sheet v-model={this.visible}
                round={false}
                class={styles.sheet}
                on-click-overlay={this.customTreeClose}>
                <div class={styles.top}>
                    <div class={styles.activityInfo}>
                        <van-icon name='arrow-down' size='20'
                            class={styles.activityInfoClose}
                            on-click={() => this.customTreeClose}></van-icon>
                        {this.$l.getLocale('fields.selectTree')}
                    </div>
                    <div class={styles.breadcrumb}>
                        {
                            this.breadcrumb.map((m: any, i: number) => {
                                return <div class={styles.breadcrumbItem +
                                    (i < this.breadcrumb.length - 1 ? ' ' + styles.breadcrumbColor : '')}
                                    on-click={() => this.onBreadcrumbClick(m, i)}>
                                    {
                                        m.label
                                    }
                                    {
                                        i < this.breadcrumb.length - 1 ? '>' : ''
                                    }
                                </div>;
                            })
                        }
                    </div>
                </div>
                <div class={styles.content}>
                    <van-cell-group>
                        {
                            this.searchTree.map((m: any, i: number) => {
                                return <van-cell title={m.label}>
                                    <template slot='title'>
                                        <van-checkbox
                                            name={m.value}
                                            v-model={m.checked}
                                            on-change={(checked: boolean) => this.onSelectTreeChange(checked, m)}
                                        >
                                            {`${m.label}`}
                                        </van-checkbox>
                                    </template>
                                    <template slot='right-icon'>
                                        {
                                            m.hasChild ? <div class={styles.clusterContent} on-click={() => this.onNextOrg(m)}>
                                                <van-icon name='cluster-o' class={styles.clusterIcon} />
                                                <span>{this.$l.getLocale('fields.lowerLevel')}</span>
                                            </div> : ''
                                        }
                                    </template>
                                </van-cell>;
                            })
                        }
                    </van-cell-group>
                </div>
                <div class={styles.buttons}>
                    <div class={styles.checked} on-click={this.onSelectTreeClick}>
                        <span>{this.$l.getLocale('fields.selected')}:{this.selectTree.length}</span>
                        {
                            this.selectTree.length > 0 ?
                                <van-icon name='arrow-up' /> : ''
                        }
                    </div>
                    <van-button type='default' size='small' color='#3896fa'
                        block
                        class={styles.button}
                        on-click={this.onSaveClick}>{this.$l.getLocale('buttons.ok')}</van-button>
                </div>
                <van-action-sheet
                    v-model={this.showSelectTree}
                    round={false}
                    class={styles.selectSheet}>
                    <div class={styles.top}>
                        <div class={styles.activityInfo}>
                            <van-icon name='arrow-down' size='20'
                                class={styles.activityInfoClose}
                                on-click={this.onSelectOrgClose}></van-icon>
                            {this.$l.getLocale('fields.selected')}
                        </div>
                        <van-cell-group border={false}
                            class={styles.selectContent}>
                            {
                                this.selectTree.map((m: any, i: number) => {
                                    return <van-cell
                                        title={m.label}
                                        icon='contact'
                                    >
                                        <template slot='right-icon'>
                                            <span
                                                class={styles.remove}
                                                on-click={() => this.onTreeRemove(i)}>
                                                {this.$l.getLocale('buttons.remove')}
                                            </span>
                                        </template>
                                    </van-cell>;
                                })
                            }
                        </van-cell-group>
                    </div>
                </van-action-sheet>
            </van-action-sheet>
        );
    }
}
