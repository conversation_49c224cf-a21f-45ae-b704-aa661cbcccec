import { dateHelper } from '@/MobileStandard/common/utils/date-helper';
import { Component, Vue, Emit, Prop, Watch } from 'vue-property-decorator';
import styles from './date-time-picker.module.less';

@Component
export class CustomDateTimePicker extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop({ default: 'date' }) datePickerModal!: string;
    @Prop({ default: 'YYYY-MM-DD' }) datePickerFormat!: string;
    @Prop() defaultValue!: any;
    private defaultDate: any = null;
    private showCalendar = false;
    private showTimer = false;
    private beginDate = '';
    private endDate = '';
    private saveDisabled = false;
    private defaultTimer: any = [];
    private copyDefaultTimer: any = [];
    private columns: any = [];
    private minDate: any = null;
    private maxDate: any = null;
    @Watch('visible')
    visibleChange(newVal: boolean) {
        if (newVal) {
            this.setDefaultDate();
        }
        this.showCalendar = newVal;
    }
    @Emit('close')
    datePickerClose() { }
    @Emit('ok')
    datePickerOk(value: any) { }
    private setDefaultDate() {
        this.saveDisabled = true;
        this.beginDate = '';
        this.endDate = '';
        const timeFormat = this.datePickerFormat === 'YYYY-MM-DD HH:mm' ?
            'HH:mm' : 'HH:mm:ss';
        const tody = new Date();
        if (this.datePickerModal === 'date') {
            this.beginDate = dateHelper.dateFormat(tody);
            this.defaultTimer = [dateHelper.dateFormat(tody, timeFormat)];
        } else {
            this.beginDate = dateHelper.dateFormat(tody);
            const add = new Date(tody.setDate(tody.getDate() + 1));
            this.endDate = dateHelper.dateFormat(add);
            this.defaultTimer = [dateHelper.dateFormat(tody, timeFormat),
            dateHelper.dateFormat(add, timeFormat)];
        }
        if (this.defaultValue) {
            if (this.datePickerModal === 'date') {
                this.beginDate = dateHelper.dateFormat(this.defaultValue);
                this.defaultTimer = [dateHelper.dateFormat(this.defaultValue, timeFormat)];
                this.defaultDate = new Date(this.beginDate);
            } else {
                this.defaultTimer = this.datePickerFormat === 'YYYY-MM-DD HH:mm' ? ['00:00', '00:00'] : ['00:00:00', '00:00:00'];
                const spValue = this.defaultValue.split('~');
                if (spValue[0]) {
                    this.beginDate = dateHelper.dateFormat(spValue[0]);
                    this.defaultTimer[0] = dateHelper.dateFormat(spValue[0], timeFormat);
                }
                if (spValue[1]) {
                    this.endDate = dateHelper.dateFormat(spValue[1]);
                    this.defaultTimer[1] = dateHelper.dateFormat(spValue[1], timeFormat);
                }
                this.defaultDate = [new Date(this.beginDate), new Date(this.endDate)];
            }
        }
        this.copyDefaultTimer = JSON.parse(JSON.stringify(this.defaultTimer));
    }
    private onSelect(date: any) {
        if (this.datePickerModal === 'date') {
            this.defaultTimer = this.datePickerFormat === 'YYYY-MM-DD HH:mm' ? ['00:00'] : ['00:00:00'];
            this.beginDate = dateHelper.dateFormat(Array.isArray(date) ? date[0] : date);
        } else {
            this.saveDisabled = false;
            this.defaultTimer = this.datePickerFormat === 'YYYY-MM-DD HH:mm' ? ['00:00', '00:00'] : ['00:00:00', '00:00:00'];
            if (Array.isArray(date)) {
                this.saveDisabled = !(!date[0] || !date[1]);
                this.beginDate = date[0] ? dateHelper.dateFormat(date[0]) : '';
                this.endDate = date[1] ? dateHelper.dateFormat(date[1]) : '';
            } else {
                this.saveDisabled = true;
                this.beginDate = this.endDate = dateHelper.dateFormat(date);
            }
        }
        this.copyDefaultTimer = JSON.parse(JSON.stringify(this.defaultTimer));
    }
    private onTimerClick() {
        let sIndex = 0;
        let fIndex = 0;
        let mIndex = 0;
        const time = this.copyDefaultTimer[0];
        if (time) {
            const times = time.split(':');
            sIndex = Number(times[0]);
            fIndex = Number(times[1]);
            if (!this.datePickerFormat || this.datePickerFormat === 'YYYY-MM-DD HH:mm:ss') {
                mIndex = Number(times[2]);
            }
        }
        this.setColumns(sIndex, fIndex, mIndex);
        this.showTimer = true;
    }
    private onSaveClick() {
        let returnDate1 = this.beginDate;
        let returnDate2 = this.endDate;
        if (this.datePickerFormat === 'YYYY-MM-DD HH:mm:ss' || this.datePickerFormat === 'YYYY-MM-DD HH:mm') {
            returnDate1 += ' ' + this.defaultTimer[0];
            returnDate2 += ' ' + this.defaultTimer[1];
        }
        if (this.datePickerModal === 'date') {
            this.datePickerOk(returnDate1);
        } else {
            this.datePickerOk([returnDate1, returnDate2]);
        }
    }
    private setColumns(sIndex: number, fIndex: number, mIndex: number) {
        this.columns = [];
        const dCol = [];
        const sCol = [];
        const fCol = [];
        const mCol = [];
        for (let i = 0; i < 60; i++) {
            const ti = (Array(2).join('0') + i).slice(-2);
            if (i < 24) {
                sCol.push(ti);
            }
            fCol.push(ti);
            mCol.push(ti);
        }
        dCol.push(dateHelper.dateFormat(this.beginDate));
        if (this.datePickerModal === 'daterange') {
            dCol.push(dateHelper.dateFormat(this.endDate));
        }
        this.columns.push({ values: dCol, defaultIndex: 0 });
        this.columns.push({ values: sCol, defaultIndex: sIndex });
        this.columns.push({ values: fCol, defaultIndex: fIndex });
        if (!this.datePickerFormat || this.datePickerFormat === 'YYYY-MM-DD HH:mm:ss') {
            this.columns.push({ values: mCol, defaultIndex: mIndex });
        }
    }
    private onTimerConfirm() {
        this.defaultTimer = this.copyDefaultTimer;
        this.showTimer = false;
    }
    private onTimerChange(picker: any, timer: any, timerIndex: number) {
        if (timerIndex === 0) {
            const index = picker.getColumnIndex(0);
            let sIndex = 0;
            let fIndex = 0;
            let mIndex = 0;
            const times = this.copyDefaultTimer[index].split(':');
            sIndex = times[0];
            fIndex = times[1];
            picker.setColumnIndex(1, Number(sIndex));
            picker.setColumnIndex(2, Number(fIndex));
            if (!this.datePickerFormat || this.datePickerFormat === 'YYYY-MM-DD HH:mm:ss') {
                mIndex = times[2];
                picker.setColumnIndex(3, Number(mIndex));
            }
        } else {
            const index = picker.getColumnIndex(0);
            this.copyDefaultTimer[index] = `${timer[1]}:${timer[2]}`;
            if (!this.datePickerFormat || this.datePickerFormat === 'YYYY-MM-DD HH:mm:ss') {
                this.copyDefaultTimer[index] = `${timer[1]}:${timer[2]}:${timer[3]}`;
            }
        }
    }
    created() {
        const now = new Date();
        this.defaultDate = now;
        this.minDate = new Date(now.getFullYear(), now.getMonth() - 6, now.getDate());
        this.maxDate = new Date(now.getFullYear(), now.getMonth() + 6, now.getDate());
    }
    render() {
        return (
            <div>
                <van-calendar
                    v-model={this.showCalendar}
                    default-date={this.defaultDate}
                    type={this.datePickerModal === 'date' ? 'single' : 'range'}
                    min-date={this.minDate}
                    max-date={this.maxDate}
                    show-confirm
                    on-close={this.datePickerClose}
                    on-select={this.onSelect}
                >
                    <template slot='footer'>
                        <div class={styles.left}>
                            {
                                this.datePickerModal === 'date' ? <div class={styles.left_data_one}>
                                    <span>{this.$l.getLocale('fields.time')}:{`${this.beginDate}
                                    ${(this.datePickerFormat === 'YYYY-MM-DD HH:mm:ss' ||
                                            this.datePickerFormat === 'YYYY-MM-DD HH:mm')
                                            && this.beginDate ?
                                            ' ' + this.defaultTimer[0] : ''}`}</span>
                                </div> : <div class={styles.left_data_more}>
                                    <span>{this.$l.getLocale('fields.beginTime')}:{`${this.beginDate}
                                    ${(this.datePickerFormat === 'YYYY-MM-DD HH:mm:ss' ||
                                            this.datePickerFormat === 'YYYY-MM-DD HH:mm')
                                            && this.beginDate ?
                                            ' ' + this.defaultTimer[0] : ''}`}</span>
                                    <span>{this.$l.getLocale('fields.endTime')}:{`${this.endDate}
                                    ${(this.datePickerFormat === 'YYYY-MM-DD HH:mm:ss' ||
                                            this.datePickerFormat === 'YYYY-MM-DD HH:mm')
                                            && this.endDate ?
                                            ' ' + this.defaultTimer[1] : ''}`}</span>
                                </div>
                            }
                        </div>
                        <div class={styles.right}>
                            {
                                this.datePickerFormat === 'YYYY-MM-DD HH:mm:ss' ||
                                    this.datePickerFormat === 'YYYY-MM-DD HH:mm' ? <van-button
                                        size='small'
                                        disabled={!this.saveDisabled}
                                        on-click={this.onTimerClick}
                                    >{this.$l.getLocale('buttons.selectTime')}</van-button> : ''
                            }
                            <van-button type='danger' size='small'
                                disabled={!this.saveDisabled}
                                on-click={this.onSaveClick}>{this.$l.getLocale('buttons.ok')}</van-button>
                        </div>
                    </template>
                </van-calendar>
                <van-popup v-model={this.showTimer} round position='bottom'>
                    <van-picker
                        show-toolbar
                        title={this.$l.getLocale('buttons.selectTime')}
                        columns={this.columns}
                        on-cancel={() => this.showTimer = false}
                        on-confirm={this.onTimerConfirm}
                        on-change={this.onTimerChange}
                    />
                </van-popup>
            </div>
        );
    }
}
