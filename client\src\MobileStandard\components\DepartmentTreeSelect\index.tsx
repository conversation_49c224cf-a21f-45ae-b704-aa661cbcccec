import { InstanceParamMixin } from '@/MobileStandard/mixins';
import { Component, Vue, Emit, Prop, Watch } from 'vue-property-decorator';
import { orgTransferInputService } from './service';
import { userTransferInputService } from '../UserTransferInput/service';
import styles from './department-tree-select.module.less';
import lodash from 'lodash';

@Component({
    mixins: [InstanceParamMixin],
})
export class DepartmentTreeSelect extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop({ default: true }) multiple!: boolean;
    @Prop() items!: any;
    private showOrgTransferInput = false;
    private showSelectOrgs = false;
    private searchText = '';
    private breadcrumb: any = [{ label: this.$l.getLocale('fields.topLevel'), value: '' }];
    private breadcrumbVisible = true;
    private searchOrg: any = [];
    private selectOrgs: any = [];
    private parentId = '';
    @Watch('visible')
    visibleChange(newVal: boolean) {
        if (newVal) {
            this.searchText = '';
            this.breadcrumbVisible = true;
            this.breadcrumb = [{ label: this.$l.getLocale('fields.topLevel'), value: '' }];
            this.selectOrgs = JSON.parse(JSON.stringify(this.items));
            this.parentId = '';
            this.onLoadData();
        }
        this.showOrgTransferInput = newVal;
    }
    @Emit('close')
    orgTransferInputClose() { }
    @Emit('ok')
    orgTransferInputOk(items: any) { }
    private onLoadData() {
        this.breadcrumbVisible = true;
        this.searchOrg = [];
        userTransferInputService.selectOrg(this.parentId).subscribe((data: any) => {
            data.map((m: any) => {
                m['checked'] = false;
                if (this.selectOrgs.findIndex((f: any) => f.organizationId === m.value) > -1) {
                    m['checked'] = true;
                }
            });
            this.searchOrg = data;
        });
    }
    private onSearch() {
        if (this.searchText) {
            this.breadcrumbVisible = false;
            this.searchOrg = [];
            orgTransferInputService.searchOrganizations({ 'key-word': this.searchText }).subscribe((data: any) => {
                data.map((m: any) => {
                    m['checked'] = false;
                    if (this.selectOrgs.findIndex((f: any) => f.organizationId === m.value) > -1) {
                        m['checked'] = true;
                    }
                });
                this.searchOrg = data;
            });
        } else {
            this.onLoadData();
        }
    }
    private onBreadcrumbClick(item: any, index: number) {
        this.breadcrumb.splice(index + 1, this.breadcrumb.length - index);
        this.parentId = item.value;
        this.onLoadData();
    }
    private onNextOrg(org: any) {
        this.parentId = org.value;
        this.breadcrumb.push({ label: org.label, value: org.value });
        this.onLoadData();
    }
    private onSaveClick() {
        this.orgTransferInputOk(this.selectOrgs);
    }
    private onCancelClick() {
        this.orgTransferInputClose();
    }
    private onSelectOrgChange(checked: boolean, item: any) {
        if (this.multiple) {
            const selectOrgIndex = this.selectOrgs.findIndex((f: any) => f.organizationId === item.value);
            if (checked && selectOrgIndex === -1) {
                this.selectOrgs.push({
                    organizationName: item.label, organizationId: item.value,
                    organizationCode: item.code,
                    organizationPath: item.fullPathCode
                });
            } else if (!checked && selectOrgIndex > -1) {
                this.selectOrgs.splice(selectOrgIndex, 1);
            }
        } else {
            if (checked) {
                this.selectOrgs = [];
                this.selectOrgs.push({
                    organizationName: item.label, organizationId: item.value,
                    organizationCode: item.code,
                    organizationPath: item.fullPathCode
                });
            } else {
                const selectOrgIndex = this.selectOrgs.findIndex((f: any) => f.organizationId === item.value);
                if (selectOrgIndex > -1) {
                    this.selectOrgs.splice(selectOrgIndex, 1);
                }
            }
        }
        this.setOrgChecked();
    }
    private setOrgChecked() {
        this.searchOrg.map((m: any) => {
            m['checked'] = false;
            if (this.selectOrgs.findIndex((f: any) => f.organizationId === m.value) > -1) {
                m['checked'] = true;
            }
        });
    }
    private onSelectOrgClick() {
        this.showSelectOrgs = true;
    }
    private onSelectOrgClose() {
        this.setOrgChecked();
        this.showSelectOrgs = false;
    }
    private onOrgRemove(index: number) {
        this.selectOrgs.splice(index, 1);
        this.setOrgChecked();
    }
    private onSelectAll() {
        lodash.forEach(this.searchOrg, (s: any) => this.onSelectOrgChange(true, s));
    }
    private onUnSeelctAll() {
        lodash.forEach(this.searchOrg, (s: any) => this.onSelectOrgChange(false, s));
    }
    private onRemoveAll() {
        this.selectOrgs = [];
        this.setOrgChecked();
    }
    created() {
    }
    render() {
        return (
            <van-action-sheet v-model={this.showOrgTransferInput}
                round={false}
                class={styles.sheet}
                on-click-overlay={this.orgTransferInputClose}>
                <div class={styles.top}>
                    <form action='/'>
                        <van-search
                            v-model={this.searchText}
                            shape='round'
                            placeholder={this.$l.getLocale('placeholders.searchKw')}
                            on-search={this.onSearch}
                            on-clear={this.onLoadData}
                            class='van-hairline--bottom'
                        />
                    </form>
                    <div class={styles.breadcrumb + (!this.breadcrumbVisible ? ' ' + styles.breadcrumbHide : '')}>
                        {
                            this.breadcrumb.map((m: any, i: number) => {
                                return <div class={styles.breadcrumbItem +
                                    (i < this.breadcrumb.length - 1 ? ' ' + styles.breadcrumbColor : '')}
                                    on-click={() => this.onBreadcrumbClick(m, i)}>
                                    {
                                        m.label
                                    }
                                    {
                                        i < this.breadcrumb.length - 1 ? ' >' : ''
                                    }
                                </div>;
                            })
                        }
                    </div>
                </div>
                <div class={styles.content + (!this.breadcrumbVisible ? ' ' + styles.contentBreadcrumbHide : '')}>
                    <van-cell-group>
                        {
                            this.searchOrg.map((m: any, i: number) => {
                                return <van-cell title={m.label}>
                                    <template slot='title'>
                                        <van-checkbox
                                            name={m.value}
                                            v-model={m.checked}
                                            on-change={(checked: boolean) => this.onSelectOrgChange(checked, m)}
                                        >
                                            {`${!this.breadcrumbVisible && m.path ? m.path : m.label}`}
                                        </van-checkbox>
                                    </template>
                                    <template slot='right-icon'>
                                        {
                                            m.childCount > 0 ? <div class={styles.clusterContent} on-click={() => this.onNextOrg(m)}>
                                                <van-icon name='cluster-o' class={styles.clusterIcon} />
                                                <span>{this.$l.getLocale('fields.lowerLevel')}</span>
                                            </div> : ''
                                        }
                                    </template>
                                </van-cell>;
                            })
                        }
                    </van-cell-group>
                </div>
                <div class={styles.buttons}>
                    <div class={styles.checked} on-click={this.onSelectOrgClick}>
                        <span>{this.$l.getLocale('fields.selected')}:{this.selectOrgs.length}</span>
                        {
                            this.selectOrgs.length > 0 ?
                                <van-icon name='arrow-up' /> : ''
                        }
                    </div>
                    {
                        this.multiple ? <div>
                            <van-button type='default' color='#009a3e'
                                block
                                class={styles.small_button}
                                on-click={this.onSelectAll}>全选</van-button>
                            <van-button type='default' color='#ffffff'
                                block
                                class={styles.small_button + ' ' + styles.cancel}
                                on-click={this.onUnSeelctAll}>取消全选</van-button>
                        </div> : ''
                    }
                    <van-button type='default' color='#ffffff'
                        block
                        class={styles.button + ' ' + styles.cancel}
                        on-click={this.onCancelClick}>{this.$l.getLocale('buttons.cancel')}</van-button>
                    <van-button type='default' color='#0e9266'
                        block
                        class={styles.button}
                        on-click={this.onSaveClick}>{this.$l.getLocale('buttons.ok')}</van-button>
                </div>
                <van-action-sheet
                    v-model={this.showSelectOrgs}
                    round={false}
                    class={styles.selectSheet}>
                    <div class={styles.top}>
                        <div class={styles.activityInfo}>
                            <span>{this.$l.getLocale('fields.selected')}</span>
                            {
                                this.multiple ? <van-button type='default' color='#ffffff'
                                    block
                                    class={styles.small_button + ' ' + styles.cancel + ' ' + styles.removeAll}
                                    on-click={this.onRemoveAll}>删除全部</van-button> : ''
                            }
                        </div>
                        <van-cell-group border={false}
                            class={styles.selectContent}>
                            {
                                this.selectOrgs.map((m: any, i: number) => {
                                    return <van-cell
                                        title={m.organizationName}
                                        icon='contact'
                                    >
                                        <template slot='right-icon'>
                                            <i class={`iconfont icon-btn_deletelist ${styles.remove}`}
                                                on-click={() => this.onOrgRemove(i)} />
                                        </template>
                                    </van-cell>;
                                })
                            }
                        </van-cell-group>
                    </div>
                </van-action-sheet>
            </van-action-sheet>
        );
    }
}
