import { Observable, of, zip } from 'rxjs';
import { httpHelper } from '@/MobileStandard/common/utils';
import { map, tap } from 'rxjs/operators';

class OrgTransferInputService {
    searchOrganizations(params: any) {
        const _url = '/api/platform/v1/manage/search-organizations';
        return httpHelper.get(_url, { params });
    }
}

export const orgTransferInputService = new OrgTransferInputService();
