import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import { subjectHelper } from '@/MobileStandard/common/utils';

@Component
export class IndexMixins extends Vue {
    @Prop({ default: true }) visible!: boolean;
    @Prop() urlParamteres!: UrlParametersDto;
    comment = '';
    @Emit('change')
    onChange(value: any) { }
    public getValues() {
        return this.comment;
    }
    onValueChange(e: any) {
        this.comment = e;
        this.onChange(this.comment);
    }
    created() {
        // 草稿打开从草稿获取办理意见
        if (this.urlParamteres.pageType === 'start' && this.urlParamteres.draftId) {
            subjectHelper.getSubject('draftInfo$').subscribe((s: any) => {
                this.comment = s.data.approvalComments;
            });
        }
    }
}
