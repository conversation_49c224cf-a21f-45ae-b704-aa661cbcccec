import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import { Component } from 'vue-property-decorator';
import styles from './index.module.less';

@Component
export class ApprovalComment extends mixins(IndexMixins) {
    render() {
        return this.visible ? <instance-collapse-item
            title={this.urlParamteres.pageType === 'start' ?
                this.$l.getLocale('fields.startComment') :
                this.$l.getLocale('fields.approvalComment')}
        >
            <van-field
                value={this.comment}
                rows={4}
                autosize
                type='textarea'
                maxlength='1000'
                show-word-limit
                placeholder={this.$l.getLocale(['fields.please',
                    'fields.input',
                    this.urlParamteres.pageType === 'start' ?
                        'fields.startComment' :
                        'fields.approvalComment'])}
                on-input={(e: any) => this.onValueChange(e)}
            />
        </instance-collapse-item> : '';
    }
}
