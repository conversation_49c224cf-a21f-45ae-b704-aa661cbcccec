import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { approvalRecordParallelService } from './service';
import { InstanceRecordDto } from './types';
import { TaskCommonStatusEnum } from '../ApprovalRecord/types';
import lodash, { debounce } from 'lodash';
import styles from './index.module.less';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import { UserCard } from '../..';
import { guidHelper, subjectHelper } from '@/MobileStandard/common/utils';
import { InstanceBaseInfoDto } from '../BaseInfo/type';
import { processService } from '@/MobileStandard/services/process';
import { RecordTimelineTitle } from './RecordTimelineTitle';

@Component({
    components: {
        UserCard, RecordTimelineTitle
    }
})
export class IndexMixins extends Vue {
    private openId: any;
    @Prop({ default: true }) visible!: boolean;
    @Prop() urlParamteres!: UrlParametersDto;
    @Prop() statusData!: any;

    records: InstanceRecordDto[][] = [];
    baseInfo!: InstanceBaseInfoDto;
    formInfo!: any;
    systemPar!: any;
    cacheSystemPar!: any;
    keyParameter!: string[];
    cacheBaseInfo!: InstanceBaseInfoDto;
    cacheFormInfo!: any;
    fetchSteps = debounce(() => this.onSearchSteps(), 500);
    loading = true;
    @Emit('conversation')
    conversation(userId: string) {
        // this.openid(userId);
    }
    getStateContent(step: any) {
        let icon = 'shenpi_icon_tongyiyuanwenyijian';
        let iconColor = '#0e9266';
        let desc = '';
        let name = '';
        if (step.stepStatus === 0) {
            icon = 'paper-full';
            iconColor = '#1890ff';
            desc = '发起';
            if (step.resolverType === TaskCommonStatusEnum.reStart) {
                icon = 'paper-full';
                iconColor = '#1890ff';
                desc = '重新发起';
            } else if (step.resolverType === TaskCommonStatusEnum.recallStart) {
                icon = 'chehui';
                iconColor = '#be4848';
                desc = '发起人撤回';
            } else if (step.resolverType === TaskCommonStatusEnum.canceled) {
                icon = 'btn_deletelist';
                iconColor = '#be4848';
                desc = '作废';
            } else if (step.isAgent) {
                desc = '代发起';
            }
        } else if (step.stepStatus === 1) {
            if (step.actionCode === 'handover' ||
                step.resolverType === TaskCommonStatusEnum.handover) {
                icon = 'shenpi_icon_weituo';
                iconColor = '#0e9266';
                desc = '委托';
                name = step.toUserName;
            } else if (step.actionCode === 'approve_agreeopinion') {
                icon = 'shenpi_icon_tongyixiugaiyijian';
                iconColor = '#f49f34';
            } else if (step.actionCode === 'reject') {
                icon = 'shenpi_icon_tuihuixiugai';
                iconColor = '#f49f34';
                desc = '退回修改';
            } else if (step.actionCode === 'approve_opinion') {
                icon = 'shenpi_icon_woyouxiugaiyijian';
                iconColor = '#be4848';
            } else if (step.actionCode === 'counter-sign') {
                icon = 'icon_shenpibuzhou_yijianzhengxun';
                iconColor = '#0e9266';
                desc = '意见征询';
                name = step.toUserName;
            } else if (step.actionCode === 'approve_ignore') {
                icon = 'shenpi_icon_hulve';
                iconColor = '#0e9266';
            } else if (step.actionCode === 'refuse') {
                icon = 'shenpi_icon_butongyi';
                iconColor = '#be4848';
            } else if (step.actionCode === 'delay' ||
                step.resolverType === TaskCommonStatusEnum.delay) {
                desc = '延缓审核';
                icon = 'wait-fill';
                iconColor = '#be4848';
            } else {
                if (step.resolverType === TaskCommonStatusEnum.skippedWhenEmptyResolver) {
                    step.comment = this.$l.getLocale('tips.skippedWhenEmptyResolver');
                } else if (step.resolverType === TaskCommonStatusEnum.skippedWhenSameApprover) {
                    step.comment = this.$l.getLocale('tips.skippedWhenSameApprover');
                } else if (step.resolverType === TaskCommonStatusEnum.recallActivity) {
                    desc = `${step.stepName}步骤撤回`;
                    icon = 'chehui';
                    iconColor = '#be4848';
                } else if (step.resolverType === TaskCommonStatusEnum.canceled) {
                    desc = '作废';
                    icon = 'btn_deletelist';
                    iconColor = '#be4848';
                }
            }
        } else if (step.stepStatus === 2) {
            if (step.isRead
                || (this.urlParamteres.pageType === 'todo' && this.urlParamteres.taskId === step.taskId)
                || (step.stepName === '发起' && this.urlParamteres.pageType === 'start')
                || (step.stepName === '发起' && this.urlParamteres.pageType === 'my-processes')) {
                icon = 'icon_shenpibuzhou_banlizhong';
                iconColor = '#ffa839';
                desc = '办理中';
            } else {
                icon = 'icon_shenpibuzhou_banlizhong';
                iconColor = '#ffa839';
                desc = '待办';
            }
        } else {
            return '';
        }
        return [<i class={`iconfont icon-${icon} ${styles.state_icon}`} style={`color:${iconColor};`} />,
        desc ? <span class={styles.tscz_ms}>{desc}</span> : '',
        name ? <span class={styles.tscz_xm}>{name}</span> : ''];
    }
    getUserInfo(resolverUserId: string) {

    }
    getStepTypeImage(stepStatus?: number) {
        // console.log(stepStatus);
        let image = 'step_shenpiwancheng';
        switch (stepStatus) {
            case 0:
                image = 'step_faqi';
                break;
            case 2:
                image = 'step_dangqianbuzhou';
                break;
            case 3:
                image = '步骤状态_default';
                break;
        }
        return require(`../../../../assets/images/${image}.png`);
    }
    getRecordHtml(step: any) {
        return <div class={styles.step_html}>
            <div class={styles.first}>
                <div class={styles.user}>
                    {
                        step.resolverUserId && step.resolverUserId !== guidHelper.empty() ?
                            [<user-card
                                class={styles.user_name}
                                userInfo={{
                                    userId: step.resolverUserId,
                                    userName: step.resolverUserName,
                                    post: step.resolverUserOrgNamePath
                                }}
                                on-conversation={(userId: string) => this.conversation(userId)} />
                                , <img src={require(`../../../../assets/images/icon_shenpibuzhou_IMgoutong.png`)}
                                    class={styles.user_image}
                                    on-click={() => this.conversation(step.resolverUserId)} />] :
                            <div class={styles.user_name}>空</div>
                    }
                </div>
                {
                    this.getStateContent(step)
                }
            </div>
            <div class={styles.second}>
                {
                    step.comment
                }
            </div>
            <div class={styles.third}>
                <span class={styles.use_time}>
                    {
                        step.approvalDate
                    }
                </span>
            </div>
        </div>;
    }

    findSystemPar() {
        if (this.baseInfo &&
            this.formInfo) {
            const params = {
                templateId: this.baseInfo.parentProcessId,
                organizationId: this.formInfo['organizationId'] || this.baseInfo.organizationId,
                userId: this.baseInfo.userId,
                postId: this.formInfo['positionId'] || this.baseInfo.positionId,
                instanceNumber: this.urlParamteres.number,
            };
            if (!this.cacheSystemPar || (this.cacheSystemPar &&
                this.cacheSystemPar.postId !== params.postId)) {
                this.cacheSystemPar = lodash.cloneDeep(params);
                processService.GetSystemPar(params).subscribe(rs => {
                    const systemPar: any = {};
                    if (rs) {
                        rs.forEach((item: any) => {
                            systemPar[item.label] = item.value;
                        });
                    }
                    this.systemPar = systemPar;
                });
            }
        }
    }
    findProcessMainParams() {
        if (this.baseInfo &&
            this.formInfo) {
            // 获取推演关键参数
            processService.getProcessMainParams(
                this.baseInfo.parentProcessId || '',
                this.urlParamteres.number || '',
                '').subscribe((key: any) => {
                    this.keyParameter = [];
                    if (key && key.length > 0) {
                        const items = key.filter((d: any) => d.keyParameterType === 1 || d.keyParameterType === 2);
                        if (items && items.length > 0) {
                            this.keyParameter = items.map((m: any) => m.keyParameter);
                        }
                    }
                });
        }
    }
    onSearchSteps() {
        if (!this.cacheBaseInfo || !this.cacheFormInfo) {
            this.cacheBaseInfo = lodash.cloneDeep(this.baseInfo);
            this.cacheFormInfo = lodash.cloneDeep(this.formInfo);
        }
        if (this.baseInfo && this.formInfo && this.keyParameter && this.systemPar) {
            // 没推演过、用户发生改变、用户岗位发生改变、用户组织发生改变、关键参数信息发生改变
            let isDeduction = false;
            if (this.cacheBaseInfo.userId !== this.baseInfo.userId
                || this.cacheBaseInfo.positionId !== this.baseInfo.positionId
                || this.cacheBaseInfo.organizationId !== this.baseInfo.organizationId) {
                isDeduction = true;
            } else if (this.keyParameter.length > 0 &&
                lodash.some(this.keyParameter, (k: any) => this.cacheFormInfo[k] !== this.formInfo[k])) {
                isDeduction = true;
            }
            if (isDeduction) {
                this.cacheBaseInfo = lodash.cloneDeep(this.baseInfo);
                this.cacheFormInfo = lodash.cloneDeep(this.formInfo);
                this.getStepInfo();
            }
        }
    }
    getStepInfo() {
        const params = this.cacheBaseInfo && this.cacheFormInfo ?
            lodash.assign(lodash.cloneDeep(this.formInfo), lodash.cloneDeep(this.baseInfo)) :
            {};
        approvalRecordParallelService.getHistoryInfo(this.urlParamteres.number || '', params).subscribe(s => {
            if (s) {
                this.records = s.data;
                this.loading = false;
            }
        });
    }
    created() {
        this.loading = true;
        this.getStepInfo();
        if (this.urlParamteres.pageType === 'todo') {
            subjectHelper.getSubject('baseInfo$').subscribe((s: any) => {
                this.baseInfo = s;
                this.findSystemPar();
                this.findProcessMainParams();
                this.fetchSteps();
            });

            subjectHelper.getSubject('formInfo$').subscribe((s: any) => {
                this.formInfo = s;
                this.findSystemPar();
                this.fetchSteps();
            });

            this.loading = false;
        }
    }
}
