// @import '../../../../themes/default/variables.less';

.container {
  display: flex;
  align-items: baseline;
  cursor: default;
  color: #999;
  font-size: 12x;

  .user {
    display: inline-block;
    // height: 40px;
    // width: 100px;
    // font-size: @font-size-lg;
    // font-weight: @font-weight-bold;

    .userName {
      color: #333;
      font-size: 14px;
      font-weight: 700;
      margin-right: 10px;
    }

    .resolver {
      color: #999 !important;
      font-size: 12x;
      margin-left: 10px;
      font-weight: 300;
    }
  }

  .auto {
    flex: 1 1 auto;
  }

  .duration {
    width: 168px;
    text-align: left;
    padding-right: 0px;
    display: inline-block;
  }

  .status {
    // width: 200px;
    // text-align: center;
    width: 220px;
    text-align: left;
    float: right;
  }

  .time {
    width: 148px;
    text-align: left;
    float: right;
    // display: inline-block;
    height: 21px;
  }

  .authorizated {
    padding-right: 20px;
    margin-left: -20px;
  }

  .approval {
    padding-right: 20px;
    margin-left: -20px;
  }

}

.contextContainer {
  cursor: default;
  // color: #999;
  font-size: 12x;
  word-break: break-all;
  white-space: pre-wrap;
  padding: 0 0px;

  .resolveTime {
    color: #999;
    text-align:right;
    font-size: 12px !important;
  }
}

@process-start: #2998fe;
@process-ready: #2998fe;
@process-processing: #f5b25e;
@process-approved: #41ba42;
@process-refused: #9c9c9c;
@process-canceled: #9c9c9c;
@process-rejected: #9c9c9c;
@process-todo: #9c9c9c;
@process-parallel: #8B50DF;
@process-suspended: #9c9c9c;

.tagColorIcon(@tag-color) {
    color: @tag-color;
}
.tagColor(@tag-color) {
    color: @tag-color !important;
    background-color: tint(@tag-color, 80%);
}
.start {
    .tagColor(@process-start);
}
.ready {
    .tagColor(@process-ready);
}
.processing {
    .tagColor(@process-processing);
}
.approved {
    .tagColor(@process-approved);
}
.refused {
    .tagColor(@process-refused);
}
.canceled {
    .tagColor(@process-canceled);
}
.rejected {
    .tagColor(@process-rejected);
}
.todo {
    .tagColor(@process-todo);
}
.suspended {
    .tagColor(@process-suspended);
}
