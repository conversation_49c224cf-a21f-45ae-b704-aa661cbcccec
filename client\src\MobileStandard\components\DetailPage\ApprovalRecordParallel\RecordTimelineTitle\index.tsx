import { Component, Vue, Prop, Watch } from 'vue-property-decorator';

import styles from './component.record-timeline-title.module.less';
import { approvalRecordParallelService } from '../service';
// import { ApproveStatus } from '../types';
import { InstanceRecordDto } from '../types';

@Component({
  // components: { ApproveStatus },
})
export class RecordTimelineTitle extends Vue {
  @Prop() title!: InstanceRecordDto;

  private isShowResolveUser(record: InstanceRecordDto) {
    return (
      record.state !== 'todo' &&
      record.detailStatus !== 'handover' &&
      record.userId &&
      record.resolveUserId &&
      record.userId !== record.resolveUserId
    );
  }
  render() {
    // console.log(this.title);
    return this.title.stepType !== 'end' ? (
      <div>
        <div class={styles.container}>
          <div style='flex:1;'>
            {this.isShowResolveUser(this.title) ? (
              <div>
                <span class={styles.user}>
                  {this.title.resolveUserName}
                </span>
                <span style='padding-left:0px'>代</span>
                <span style='padding:0 25px'>
                  {this.title.userName}
                </span>
                <span class={styles.approval}>{this.$t('framework.approval')}</span>
              </div>
            ) : (
              [
                <span class={styles.user}>
                  {this.title.userName
                    ? [
                      <span class={styles.userName}>
                        {this.title.userName}
                      </span>,
                    ]
                    : ''}
                  {this.title.detailStatus === 'extrainsert' || this.title.detailStatus === 'extraappend' ? (
                    <span style='padding-left: 10px;'>
                      &nbsp;&nbsp;加签给&nbsp;&nbsp;
                      {this.title.toUser.map((e: any) => {
                        return e.userName;
                      }).join(';')}
                    </span>
                  ) : (
                    ''
                  )}
                </span>
              ]
            )}
            {this.title.detailStatus === 'handover' ? (
              <span style='padding-left: 10px;'>
                &nbsp;&nbsp;转交给&nbsp;&nbsp;
                {this.title.toUser.map((e: any) => {
                  return e.userName;
                }).join(';')}
              </span>
            ) : (
              ''
            )}
            {this.title.detailStatus === 'rejectactivity' || this.title.detailStatus === 'rejectactivitydirect' ? (
              <span
                title={this.title.toActivityName}
                style='width:300px; padding-left: 10px; overflow: hidden; text-overflow:ellipsis; white-space:nowrap; cursor: pointer;'
              >
                &nbsp;&nbsp;退回到&nbsp;&nbsp; {this.title.toActivityName}
              </span>
            ) : (
              ''
            )}</div>
          <div style='text-align:right; font-size:12px' class={styles[this.title.state]}>
            {this.title.stateName}
          </div>
        </div>
        <div >
          {this.title.commentTextArray && this.title.commentTextArray.length !== 0
            ? this.title.commentTextArray.map((item: any) =>
              item !== null && item !== '' ? <div class={styles.contextContainer}>{item}</div> : ''
            )
            : ''
          }
        </div>
        <div class={styles.contextContainer} >
          {this.title.state === 'processing' ? (
            <span>&nbsp;&nbsp;</span>
          ) : (
            <span class={styles.resolveTime}>{this.title.resolveTime}</span>
          )}
        </div>
      </div>
    ) : (
      ''
    );
  }
}
