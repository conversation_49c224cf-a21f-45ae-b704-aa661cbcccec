.record {
    padding: 10px 0;

    .step {
        .ul {
            margin: 0;
            padding: 0;
            list-style: none;

            .li {
                position: relative;

                .line_tail {
                    position: absolute;
                    border-left: 1px solid #e8e8e8;
                    height: 100%;
                    left: 15px;
                }

                .line_head {
                    position: absolute;

                    img {
                        height: 30px;
                        width: 30px;
                    }
                }

                .content {
                    margin-left: 40px;
                    padding-bottom: 10px;
                    .content_parastep {
                        border: 1px solid #ecedef;
                        border-radius: 4px;
                        margin-bottom: 5px;
                    }
                    .content_step {
                        border: 1px solid #e8e8e8;
                        border-radius: 4px;

                        .content_step_name {
                            height: 30px;
                            line-height: 30px;
                            background-color: #e9edf2;
                            font-size: 14px;
                            color: #45575e;
                            padding-left: 12px;
                            position: relative;
                        }

                        .content_step_name::before {
                            position: absolute;
                            left: -8px;
                            top: 7px;
                            content: '';
                            display: inline-block;
                            width: 15px;
                            height: 15px;
                            background: #e9edf2;
                            -webkit-transform: rotate(45deg);
                            -ms-transform: rotate(45deg);
                            transform: rotate(45deg);
                            border-left: 1px solid #e8e8e8;
                            border-bottom: 1px solid #e8e8e8;
                        }

                        .content_step_approval {
                            // background-color: #f0f2f5;
                            padding: 12px;
                            border-radius: 0px 4px;
                        }
                    }
                }
            }
        }
    }

    .step:first-child {
        .line_tail {
            margin-top: 10px;
        }
    }

    .step:last-child {
        .line_tail {
            height: 10px !important;
        }
    }
}