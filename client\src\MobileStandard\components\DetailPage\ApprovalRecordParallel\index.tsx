import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import { Component } from 'vue-property-decorator';
import styles from './index.module.less';
import lodash from 'lodash';
import { InstanceRecordDto } from './types';

@Component
export class ApprovalRecordParallel extends mixins(IndexMixins) {
    render() {
        return this.visible ? <instance-collapse-item
            title={this.$l.getLocale('fields.approvalRecord')}
        >
            <div class={styles.record}>
                {
                    (this.records || [])
                        .filter((tasks: any) => (tasks || []).length > 0)
                        .map((tasks: any, si: number) => {
                            const lastTask = (tasks.length > 0 ? tasks[tasks.length - 1] : {}) as InstanceRecordDto;
                            return (tasks[0].stepType || '') !== 'end' ? (
                                <div class={styles.step}>
                                    {
                                        tasks[0].isParallel ?
                                            tasks[0].paralleSteps.map((e: any, parIndex: number) => {
                                                return (
                                                    <ul class={styles.ul}>
                                                        <li class={styles.li}>
                                                            <div class={styles.content}>
                                                                <div class={styles.content_parastep}>
                                                                    {e.map((a: any, parStepIndex: number) => {
                    return (
                        <div class={styles.step}>
                            <ul class={styles.ul}>
                                <li class={styles.li}>
                                    <div class={styles.line_tail}></div>
                                    <div class={styles.line_head}>
                                        <img src={this.getStepTypeImage(a.stepStatus)} />
                                    </div>
                                    <div class={styles.content}>
                                        <div class={styles.content_step}>
                                            <div class={styles.content_step_name}>
                                                {
                                                    a.name
                                                }
                                            </div>
                    {
                        <div class={styles.content_step_approval}>
                            {a.tasks && a.tasks.length > 0 &&
                                a.tasks.map((task: any) => {
                                    task.state = task.status || 'todo';
                                    task.resolveTime = task.finishTime;
                                    task.stayDuration = (Math.floor((Number(task.elapsedTime) / 3600) * 100) / 100).toString();
                                    task.commentTextArray = task.comments || [];
                                    task.stateName = task.detailStatusName || task.statusName;
                                    return (
                                        <div>
                                            {console.log(task)}
                                            <record-timeline-title title={task} />
                                        </div>
                                    );
                                })
                            }
                        </div>
                    }
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    );
                                                                    })
                                                                    }
                                                                </div></div>
                                                        </li>
                                                    </ul>
                                                );
                                            })
                                            :
                                            (
                                                <div class={styles.step}>
                                                    <ul class={styles.ul}>
                                                        <li class={styles.li}>
                                                            <div class={styles.line_tail}></div>
                                                            <div class={styles.line_head}>
                                                                <img src={this.getStepTypeImage(tasks[0].stepStatus)} />
                                                            </div>
                                                            <div class={styles.content}>
                                                                <div class={styles.content_step}>
                                                                    <div class={styles.content_step_name}>
                                                                        {
                                                                            tasks[0].stepName
                                                                        }
                                                                    </div>
                                                                    {
                                                                        <div class={styles.content_step_approval}>
                                                                            {tasks.map((task: any) => (
                                                                                <div style=''>
                                                                                    {/* {console.log(task)} */}
                                                                                    <record-timeline-title title={task} />
                                                                                </div>
                                                                            ))}
                                                                        </div>
                                                                    }
                                                                </div>
                                                            </div>
                                                        </li>
                                                    </ul>
                                                </div>
                                            )
                                    }
                                </div>) :
                                (
                                    ''
                                );
                        })
                }
            </div>
        </instance-collapse-item> : '';
    }
}
