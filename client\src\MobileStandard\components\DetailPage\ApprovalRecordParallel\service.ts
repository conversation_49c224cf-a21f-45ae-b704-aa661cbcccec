import { httpHelper, i18nHelper } from '@/MobileStandard/common/utils';
import { map } from 'rxjs/operators';
import { ApproveState, InstanceRecordResultDto } from './types';

class ApprovalRecordParallelService {
    getHistoryInfo(number: string, formdata: any) {
        const _url = `/api/engine/v1/instances/${number}/steps`;
        return httpHelper.post(_url, formdata, undefined, { loading: false }).pipe(
            map(data => {
                const result: InstanceRecordResultDto = { data: [], branchName: '' };
                result.datum = data;
                result.branchName = data.branchName;
                data.steps.forEach((d: any) => {
                    const tasks = (d.tasks || []).length > 0 ? d.tasks : [d];
                    result.data.push(
                        tasks.map((task: any) => ({
                            userName: task.userName,
                            userId: task.userId,
                            toUser: task.toUser,
                            stepName: task.activityName || task.name,
                            state: task.status || 'todo',
                            stepStatus: !task.status ? 3 : task.status === 'processing' ? 2 : task.status === 'start' ? 0 : 1,
                            stepType: task.nodeType,
                            stateName: task.detailStatusName || task.statusName,
                            stayDuration: (Math.floor((Number(task.elapsedTime) / 3600) * 100) / 100).toString(),
                            resolveTime: task.finishTime,
                            commentTextArray: task.comments || [],
                            resolveUserId: task.resolveUserId,
                            resolveUserName: task.resolveUserName,
                            detailStatus: task.detailStatus,
                            activityResolverName: task.activityResolverName,
                            activityResolveType: task.activityResolveType,
                            emptyApproverType: d.emptyApproverType,
                            paralleSteps: d.paralleSteps,
                            isParallel: d.isParallel,
                            toActivityName: task.toActivityName
                        }))
                    );
                });

                return result;
            })
        );
    }
    getIconType(state: ApproveState): string {
        switch (state) {
            case ApproveState.start:
            case ApproveState.approved:
            case ApproveState.canceled:
            case ApproveState.refused:
            case ApproveState.rejected:
                return 'check-circle';
            default:
                return 'clock-circle';
        }
    }
    getDurationText(state: ApproveState, stayDuration: string | undefined) {
        return (state !== ApproveState.approved && state !== ApproveState.rejected) || stayDuration === undefined
            ? ''
            :
            i18nHelper.getLocale('fields.approvalDuration') +
            i18nHelper.getLocale('fields.colon') +
            stayDuration +
            i18nHelper.getLocale('fields.hour');
    }
}

export const approvalRecordParallelService = new ApprovalRecordParallelService();
