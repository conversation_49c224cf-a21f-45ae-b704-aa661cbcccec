export interface InstanceRecordDto {
    // toUser: [];
    toActivityName: any;
    userId?: string;
    userName: string;
    stepName: string;
    state: ApproveState;
    stateName?: string;
    stepType?: string;
    stayDuration?: string;
    resolveTime?: string;
    commentTextArray: string[];
    statusName?: string;
    resolveUserId?: string;
    resolveUserName?: string;
    activityResolverName?: string;
    activityResolveType?: number;
    detailStatus?: string;
    emptyApproverType?: number;
    isParallel?: boolean;
    paralleSteps: [];
    toUser: [];
    // toUserID: string;
    // toUserName: string;
}

// 审批 状态枚举
export enum ApproveState {
    start = 'start',
    ready = 'ready',
    processing = 'processing',
    approved = 'approved',
    refused = 'refused',
    canceled = 'canceled',
    rejected = 'rejected',
    todo = 'todo',
    suspended = 'suspended',
}

export interface InstanceRecordResultDto {
    datum?: any;
    data: InstanceRecordDto[][];
    branchName?: string;
}

export interface SortStepDto {
    stepId?: string;
    stepName?: string;
    stepStatus?: number;
    stepType?: string;
}

export enum TaskCommonStatusEnum {
    done = 2,
    reStart = 9,
    recallStart = 30,
    skippedWhenEmptyResolver = 32,
    skippedWhenSameApprover = 33,
    recallActivity = 31,
    handover = 20,
    start = 0,
    canceled = -1,
    delay = 23,
    extraAppend = 21,
    extraInsert = 22,
    rejectStart = 10,
    rejectActivity = 11,
    rejectStartDirect = 12,
    rejectActivityDirect = 13,
    interveneJumpStartNode  = 45
}
