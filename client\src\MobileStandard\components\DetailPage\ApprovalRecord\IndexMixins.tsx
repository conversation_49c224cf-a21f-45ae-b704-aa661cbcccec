import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { approvalRecordService } from './service';
import { ApprovalRecordDto, SortStepDto, TaskCommonStatusEnum } from './types';
import lodash, { debounce } from 'lodash';
import styles from './index.module.less';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import { UserCard } from '../..';
import { guidHelper, subjectHelper } from '@/MobileStandard/common/utils';
import { InstanceBaseInfoDto } from '../BaseInfo/type';
import { processService } from '@/MobileStandard/services/process';

@Component({
    components: {
        UserCard
    }
})
export class IndexMixins extends Vue {
    private openId: any;
    @Prop({ default: true }) visible!: boolean;
    @Prop() urlParamteres!: UrlParametersDto;
    sourceRecord: ApprovalRecordDto[] = [];
    stepIdSort: SortStepDto[] = [];
    baseInfo!: InstanceBaseInfoDto;
    formInfo!: any;
    systemPar!: any;
    cacheSystemPar!: any;
    keyParameter!: string[];
    cacheBaseInfo!: InstanceBaseInfoDto;
    cacheFormInfo!: any;
    fetchSteps = debounce(() => this.onSearchSteps(), 500);
    @Emit('conversation')
    conversation(userId: string) {
        this.openid(userId);
    }
    private openid(startUserLoginId: string) {
        const param = {
            userId: startUserLoginId
            // userId: 'songyang_hz'
        };
        console.log(param);
        approvalRecordService.getOpenid(param).subscribe(data => {
            console.log(data);
            this.openId = data;
            window.open('https://applink.feishu.cn/client/chat/open?openId=' + this.openId);
        });
    }
    getStepTypeImage(stepStatus?: number) {
        console.log(stepStatus);
        let image = 'step_shenpiwancheng';
        switch (stepStatus) {
            case 0:
                image = 'step_faqi';
                break;
            case 2:
                image = 'step_dangqianbuzhou';
                break;
            case 3:
                image = '步骤状态_default';
                break;
        }
        return require(`../../../../assets/images/${image}.png`);
    }
    getFirstStepApprovalHtml(step: any) {
        // 找到第一层步骤
        const firstStep = lodash.sortBy(lodash.filter(this.sourceRecord,
            (s: any) => s.stepName === step.stepName && !s.sourceId), 'approvalDate');
        // 过滤空审批人
        let glEmptyUserStep: any = [];
        if (firstStep && firstStep.length > 0) {
            glEmptyUserStep = lodash.sortBy(lodash.filter(firstStep,
                (s: any) => s.resolverUserId !== guidHelper.empty &&
                    s.resolverType !== TaskCommonStatusEnum.skippedWhenEmptyResolver), 'approvalDate');
            if (glEmptyUserStep && glEmptyUserStep.length === 0) {
                glEmptyUserStep = [firstStep[firstStep.length - 1]];
            }
        }
        return lodash.map(glEmptyUserStep, (fs: any, fsi: number) => {
            return [this.getRecordHtml(fs),
            this.getSecondStepApprovalHtml(fs, 1),
            this.getStepApprovalHtml_Expand(fs, glEmptyUserStep, fsi, 1)];
        });
    }
    getSecondStepApprovalHtml(step: any, level: number): any {
        const approvals = lodash.sortBy(lodash.filter(this.sourceRecord,
            (s: any) => s.stepName === step.stepName &&
                s.sourceId && s.sourceResolveType &&
                s.sourceId === step.taskId), (b: any) => 'approvalDate');
        // 过滤空审批人
        let glEmptyUserStep: any = [];
        if (approvals && approvals.length > 0) {
            glEmptyUserStep = lodash.sortBy(lodash.filter(approvals,
                (s: any) => s.resolverUserId !== guidHelper.empty &&
                    s.resolverType !== TaskCommonStatusEnum.skippedWhenEmptyResolver), 'approvalDate');
            if (glEmptyUserStep && glEmptyUserStep.length === 0) {
                glEmptyUserStep = [approvals[approvals.length - 1]];
            }
        }
        if (glEmptyUserStep && glEmptyUserStep.length > 0) {
            const html = lodash.map(glEmptyUserStep, (gg: ApprovalRecordDto, ggi: number) => {
                return [this.getRecordHtml(gg),
                this.getSecondStepApprovalHtml(gg, 2),
                this.getStepApprovalHtml_Expand(gg, glEmptyUserStep, ggi, 2)];
            });
            return level === 1 ? <div class={styles.secondLevel}>{html}</div> : html;
        }
        return '';
    }
    getStepApprovalHtml_Expand(step: any, steps: any, stepIndex: number, level: any): any {
        if (stepIndex + 1 < steps.length &&
            steps[stepIndex + 1].taskId === step.taskId &&
            steps[stepIndex + 1].resolverType === TaskCommonStatusEnum.recallActivity) {
            return '';
        }
        // 找到当前步骤的追随步骤
        const firstStep = lodash.sortBy(lodash.filter(this.sourceRecord,
            (s: any) => s.stepName === step.stepName && s.sourceId && !s.sourceResolveType &&
                s.sourceId === step.taskId), 'approvalDate');
        // 过滤空审批人
        let glEmptyUserStep: any = [];
        if (firstStep && firstStep.length > 0) {
            glEmptyUserStep = lodash.sortBy(lodash.filter(firstStep,
                (s: any) => s.resolverUserId !== guidHelper.empty &&
                    s.resolverType !== TaskCommonStatusEnum.skippedWhenEmptyResolver), 'approvalDate');
            if (glEmptyUserStep && glEmptyUserStep.length === 0) {
                glEmptyUserStep = [firstStep[firstStep.length - 1]];
            }
        }
        return lodash.map(glEmptyUserStep, (fs: any, fsi: number) => {
            return [
                this.getRecordHtml(fs),
                this.getSecondStepApprovalHtml(fs, level),
                this.getStepApprovalHtml_Expand(fs, glEmptyUserStep, fsi, level)
            ];
        });
    }
    getStateContent(step: any) {
        let icon = 'shenpi_icon_tongyiyuanwenyijian';
        let iconColor = '#0e9266';
        let desc = '';
        let name = '';
        if (step.stepStatus === 0) {
            icon = 'paper-full';
            iconColor = '#1890ff';
            desc = '发起';
            if (step.resolverType === TaskCommonStatusEnum.reStart) {
                icon = 'paper-full';
                iconColor = '#1890ff';
                desc = '重新发起';
            } else if (step.resolverType === TaskCommonStatusEnum.recallStart) {
                icon = 'chehui';
                iconColor = '#be4848';
                desc = '发起人撤回';
            } else if (step.resolverType === TaskCommonStatusEnum.canceled) {
                icon = 'btn_deletelist';
                iconColor = '#be4848';
                desc = '作废';
            } else if (step.isAgent) {
                desc = '代发起';
            }
        } else if (step.stepStatus === 1) {
            if (step.actionCode === 'handover' ||
                step.resolverType === TaskCommonStatusEnum.handover) {
                icon = 'shenpi_icon_weituo';
                iconColor = '#0e9266';
                desc = '委托';
                name = step.toUserName;
            } else if (step.actionCode === 'approve_agreeopinion') {
                icon = 'shenpi_icon_tongyixiugaiyijian';
                iconColor = '#f49f34';
            } else if (step.actionCode === 'reject') {
                icon = 'shenpi_icon_tuihuixiugai';
                iconColor = '#f49f34';
                desc = '退回修改';
            } else if (step.actionCode === 'approve_opinion') {
                icon = 'shenpi_icon_woyouxiugaiyijian';
                iconColor = '#be4848';
            } else if (step.actionCode === 'counter-sign') {
                icon = 'icon_shenpibuzhou_yijianzhengxun';
                iconColor = '#0e9266';
                desc = '意见征询';
                name = step.toUserName;
            } else if (step.actionCode === 'approve_ignore') {
                icon = 'shenpi_icon_hulve';
                iconColor = '#0e9266';
            } else if (step.actionCode === 'refuse') {
                icon = 'shenpi_icon_butongyi';
                iconColor = '#be4848';
            } else if (step.actionCode === 'delay' ||
                step.resolverType === TaskCommonStatusEnum.delay) {
                desc = '延缓审核';
                icon = 'wait-fill';
                iconColor = '#be4848';
            } else {
                if (step.resolverType === TaskCommonStatusEnum.skippedWhenEmptyResolver) {
                    step.comment = this.$l.getLocale('tips.skippedWhenEmptyResolver');
                } else if (step.resolverType === TaskCommonStatusEnum.skippedWhenSameApprover) {
                    step.comment = this.$l.getLocale('tips.skippedWhenSameApprover');
                } else if (step.resolverType === TaskCommonStatusEnum.recallActivity) {
                    desc = `${step.stepName}步骤撤回`;
                    icon = 'chehui';
                    iconColor = '#be4848';
                } else if (step.resolverType === TaskCommonStatusEnum.canceled) {
                    desc = '作废';
                    icon = 'btn_deletelist';
                    iconColor = '#be4848';
                }
            }
        } else if (step.stepStatus === 2) {
            if (step.isRead
                || (this.urlParamteres.pageType === 'todo' && this.urlParamteres.taskId === step.taskId)
                || (step.stepName === '发起' && this.urlParamteres.pageType === 'start')
                || (step.stepName === '发起' && this.urlParamteres.pageType === 'my-processes')) {
                icon = 'icon_shenpibuzhou_banlizhong';
                iconColor = '#ffa839';
                desc = '办理中';
            } else {
                icon = 'icon_shenpibuzhou_banlizhong';
                iconColor = '#ffa839';
                desc = '待办';
            }
        } else {
            return '';
        }
        return [<i class={`iconfont icon-${icon} ${styles.state_icon}`} style={`color:${iconColor};`} />,
        desc ? <span class={styles.tscz_ms}>{desc}</span> : '',
        name ? <span class={styles.tscz_xm}>{name}</span> : ''];
    }
    getUserInfo(resolverUserId: string) {

    }
    getRecordHtml(step: any) {
        return <div class={styles.step_html}>
            <div class={styles.first}>
                <div class={styles.user}>
                    {
                        step.resolverUserId && step.resolverUserId !== guidHelper.empty() ?
                            [<user-card
                                class={styles.user_name}
                                userInfo={{
                                    userId: step.resolverUserId,
                                    userName: step.resolverUserName,
                                    post: step.resolverUserOrgNamePath
                                }}
                                on-conversation={(userId: string) => this.conversation(userId)} />
                                ] :
                            <div class={styles.user_name}>空</div>
                    }
                </div>
                {
                    this.getStateContent(step)
                }
            </div>
            <div class={styles.second}>
                {
                    step.comment
                }
            </div>
            <div class={styles.third}>
                <span class={styles.use_time}>
                    {
                        step.approvalDate
                    }
                </span>
            </div>
        </div>;
    }
    findSystemPar() {
        if (this.baseInfo &&
            this.formInfo) {
            const params = {
                templateId: this.baseInfo.parentProcessId,
                organizationId: this.formInfo['organizationId'] || this.baseInfo.organizationId,
                userId: this.baseInfo.userId,
                postId: this.formInfo['positionId'] || this.baseInfo.positionId,
                instanceNumber: this.urlParamteres.number,
            };
            if (!this.cacheSystemPar || (this.cacheSystemPar &&
                this.cacheSystemPar.postId !== params.postId)) {
                this.cacheSystemPar = lodash.cloneDeep(params);
                processService.GetSystemPar(params).subscribe(rs => {
                    const systemPar: any = {};
                    if (rs) {
                        rs.forEach((item: any) => {
                            systemPar[item.label] = item.value;
                        });
                    }
                    this.systemPar = systemPar;
                });
            }
        }
    }
    findProcessMainParams() {
        if (this.baseInfo &&
            this.formInfo) {
            // 获取推演关键参数
            processService.getProcessMainParams(
                this.baseInfo.parentProcessId || '',
                this.urlParamteres.number || '',
                '').subscribe((key: any) => {
                    this.keyParameter = [];
                    if (key && key.length > 0) {
                        const items = key.filter((d: any) => d.keyParameterType === 1 || d.keyParameterType === 2);
                        if (items && items.length > 0) {
                            this.keyParameter = items.map((m: any) => m.keyParameter);
                        }
                    }
                });
        }
    }
    onSearchSteps() {
        if (!this.cacheBaseInfo || !this.cacheFormInfo) {
            this.cacheBaseInfo = lodash.cloneDeep(this.baseInfo);
            this.cacheFormInfo = lodash.cloneDeep(this.formInfo);
        }
        if (this.baseInfo && this.formInfo && this.keyParameter && this.systemPar) {
            // 没推演过、用户发生改变、用户岗位发生改变、用户组织发生改变、关键参数信息发生改变
            let isDeduction = false;
            if (this.cacheBaseInfo.userId !== this.baseInfo.userId
                || this.cacheBaseInfo.positionId !== this.baseInfo.positionId
                || this.cacheBaseInfo.organizationId !== this.baseInfo.organizationId) {
                isDeduction = true;
            } else if (this.keyParameter.length > 0 &&
                lodash.some(this.keyParameter, (k: any) => this.cacheFormInfo[k] !== this.formInfo[k])) {
                isDeduction = true;
            }
            if (isDeduction) {
                this.cacheBaseInfo = lodash.cloneDeep(this.baseInfo);
                this.cacheFormInfo = lodash.cloneDeep(this.formInfo);
                this.getStepInfo();
            }
        }
    }
    getStepInfo() {
        const params = this.cacheBaseInfo && this.cacheFormInfo ?
            lodash.assign(lodash.cloneDeep(this.formInfo), lodash.cloneDeep(this.baseInfo)) :
            {};
        approvalRecordService.getStepInfo(this.urlParamteres.number || '', params).subscribe(data => {
            // 处理数据方便展示
            const recallStartItem = lodash.filter(data || [], (s: any) => s.resolverType === TaskCommonStatusEnum.recallStart);
            const startItem = lodash.find(data || [], (s: any) => s.resolverType === TaskCommonStatusEnum.start);
            if (recallStartItem && recallStartItem.length > 0 && startItem) {
                lodash.forEach(recallStartItem, (rs: any) => {
                    rs.resolverUserId = startItem.resolverUserId;
                    rs.resolverUserName = startItem.resolverUserName;
                    rs.resolverUserOrgNamePath = startItem.resolverUserOrgNamePath;
                });
            }
            subjectHelper.getSubject('approvalRecord$').next(data || []);
            this.sourceRecord = data || [];
            // 优先找到最后一步重新发起步骤，如有按照此步骤之后的步骤作为展示步骤
            let records = lodash.cloneDeep(data || []);
            // const lastReStartStepIndex = lodash.findLastIndex(records,
            //     (s: ApprovalRecordDto) => s.stepName === '开始' &&
            //         (s.stepStatus === 0 || s.stepStatus === 2));
            // if (lastReStartStepIndex > -1) {
            //     records = lodash.drop(records, lastReStartStepIndex);
            // }
            this.sourceRecord = lodash.map(this.sourceRecord, (m: any) => {
                return {
                    ...m,
                    stepName: m.stepName === '开始' || m.stepName === '重新发起' || m.stepStatus === 0 ?
                        '发起' : m.stepName
                };
            });
            records = lodash.map(records, (m: any) => {
                return {
                    ...m,
                    stepName: m.stepName === '开始' || m.stepName === '重新发起' || m.stepStatus === 0 ?
                        '发起' : m.stepName
                };
            });
            lodash.forEach(records, (s: ApprovalRecordDto) => {
                if (s.stepName) {
                    let item = lodash.find(this.stepIdSort, (ss: any) => ss.stepName === s.stepName);
                    if (!item) {
                        item = {
                            stepId: s.stepId,
                            stepName: s.stepName,
                            stepStatus: s.stepStatus,
                            stepType: s.stepType
                        };
                        this.stepIdSort.push(item);
                    } else if (s.stepStatus === 2) {
                        item.stepStatus = 2;
                    }
                }
            });
        });
    }
    created() {
        this.getStepInfo();
        if (this.urlParamteres.pageType === 'todo') {
            subjectHelper.getSubject('baseInfo$').subscribe((s: any) => {
                this.baseInfo = s;
                this.findSystemPar();
                this.findProcessMainParams();
                this.fetchSteps();
            });

            subjectHelper.getSubject('formInfo$').subscribe((s: any) => {
                this.formInfo = s;
                this.findSystemPar();
                this.fetchSteps();
            });
        }
    }
}
