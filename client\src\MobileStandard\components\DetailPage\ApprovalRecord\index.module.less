.record {
    padding: 10px 0;

    .step {
        .ul {
            margin: 0;
            padding: 0;
            list-style: none;

            .li {
                position: relative;

                .line_tail {
                    position: absolute;
                    border-left: 1px solid #e8e8e8;
                    height: 100%;
                    left: 15px;
                }

                .line_head {
                    position: absolute;

                    img {
                        height: 30px;
                        width: 30px;
                    }
                }

                .content {
                    margin-left: 40px;
                    padding-bottom: 10px;

                    .content_step {
                        border: 1px solid #e8e8e8;
                        border-radius: 4px;

                        .content_step_name {
                            height: 30px;
                            line-height: 30px;
                            background-color: #e9edf2;
                            font-size: 14px;
                            color: #45575e;
                            padding-left: 12px;
                            position: relative;
                        }

                        .content_step_name::before {
                            position: absolute;
                            left: -8px;
                            top: 7px;
                            content: '';
                            display: inline-block;
                            width: 15px;
                            height: 15px;
                            background: #e9edf2;
                            -webkit-transform: rotate(45deg);
                            -ms-transform: rotate(45deg);
                            transform: rotate(45deg);
                            border-left: 1px solid #e8e8e8;
                            border-bottom: 1px solid #e8e8e8;
                        }

                        .content_step_approval {
                            background-color: #f6f7f9;
                            padding: 12px;
                            border-radius: 0px 4px;

                            .step_html:not(:first-child) {
                                border-top: 1px dashed #dadada;
                                margin-top: 10px;
                                padding-top: 10px;
                            }

                            .step_html {
                                .first {
                                    display: flex;
                                    align-items: center;

                                    .user {
                                        flex: 1;
                                        display: flex;
                                        align-items: center;

                                        .user_name {
                                            font-size: 16px;
                                            font-weight: bold;
                                            color: #333333;
                                            cursor: pointer;
                                        }

                                        .user_image {
                                            margin-left: 5px;
                                            font-size: 15px;
                                            color: #0e9266;
                                        }
                                    }

                                    .state_icon {
                                        font-size: 13px;
                                    }

                                    .tscz_ms {
                                        margin-left: 5px;
                                        font-size: 12px;
                                        color: #333333;
                                    }

                                    .tscz_xm {
                                        margin-left: 5px;
                                        font-size: 12px;
                                        color: #333333;
                                    }
                                }

                                .second {
                                    min-height: 10px;
                                    font-size: 14px;
                                    color: #333333;
                                    white-space: break-spaces;
                                    word-break: break-all;
                                }

                                .third {
                                    text-align: right;

                                    .use_time {
                                        font-size: 12px;
                                        color: #999999;
                                        margin-left: 10px;
                                    }
                                }
                            }

                            .secondLevel {
                                background-color: #edeef1;
                                border-radius: 4px;
                                padding-left: 20px;
                                padding-top: 15px;
                                padding-bottom: 15px;
                                padding-right: 10px;
                                margin-top: 20px;
                                position: relative;
                                border: 1px solid #edeef1;

                                .user {
                                    width: 105px;
                                }

                                .third {
                                    padding-right: 0px;
                                }

                                .third:not(:last-child) {
                                    border-bottom: 1px dashed #dadada;
                                    margin-bottom: 10px;
                                    padding-bottom: 10px;
                                }

                                .secondLevel {
                                    padding-top: 0;
                                    padding-bottom: 0;
                                    padding-left: 0;
                                    padding-right: 0;
                                }

                                .secondLevel::before {
                                    display: none;
                                }
                            }

                            .secondLevel::before {
                                position: absolute;
                                left: 15px;
                                top: -9px;
                                content: '';
                                display: inline-block;
                                width: 15px;
                                height: 15px;
                                background: #edeef1;
                                transform: rotate(45deg);
                                border-left: 1px solid #edeef1;
                                border-top: 1px solid #edeef1;
                            }

                            .secondLevel:not(:last-child) {
                                border-bottom: 1px dashed #dadada;
                                margin-bottom: 10px;
                                padding-bottom: 10px;
                            }
                        }
                    }
                }
            }
        }
    }

    .step:first-child {
        .line_tail {
            margin-top: 10px;
        }
    }

    .step:last-child {
        .line_tail {
            height: 10px !important;
        }
    }
}