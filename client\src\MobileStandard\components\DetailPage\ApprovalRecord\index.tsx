import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import { Component } from 'vue-property-decorator';
import styles from './index.module.less';
import lodash from 'lodash';
import { ApprovalRecordDto, SortStepDto } from './types';

@Component
export class ApprovalRecord extends mixins(IndexMixins) {
    render() {
        return this.visible ? <instance-collapse-item
            title={this.$l.getLocale('fields.approvalRecord')}
        >
            <div class={styles.record}>
                {
                    lodash.map(this.stepIdSort, (s: SortStepDto) => {
                        return <div class={styles.step}>
                            <ul class={styles.ul}>
                                <li class={styles.li}>
                                    <div class={styles.line_tail}></div>
                                    <div class={styles.line_head}>
                                        <img src={this.getStepTypeImage(s.stepStatus)} />
                                    </div>
                                    <div class={styles.content}>
                                        <div class={styles.content_step}>
                                            <div class={styles.content_step_name}>
                                                {
                                                    s.stepName
                                                }
                                            </div>
                                            {
                                                s.stepType !== 'end' ?
                                                    <div class={styles.content_step_approval}>
                                                        {
                                                            this.getFirstStepApprovalHtml(s)
                                                        }
                                                    </div> : ''
                                            }
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>;
                    })
                }
            </div>
        </instance-collapse-item> : '';
    }
}
