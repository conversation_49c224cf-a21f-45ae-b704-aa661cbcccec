import { httpHelper } from '@/MobileStandard/common/utils';
import { Observable } from 'rxjs';

class ApprovalRecordService {
    getHistoryInfo(instanceNumber: string) {
        return httpHelper.get('/api/engine/v2/instances/history-info',
            { params: { instanceNumber: instanceNumber } });
    }
    getOpenid(params: any): Observable<any[]> {
        const url = `/api/platform/feishu/openId`;
        return httpHelper.get(url, { params });
    }
    getStepInfo(instanceNumber: string, params: any) {
        return httpHelper.post(
            '/api/engine/v2/instances/step-info',
            params,
            {
                params: {
                    instanceNumber: instanceNumber
                },
            }
        );
    }
}

export const approvalRecordService = new ApprovalRecordService();
