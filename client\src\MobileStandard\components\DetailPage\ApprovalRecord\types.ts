export interface ApprovalRecordDto {
    activityId?: string;
    actionCode?: string;
    stepId?: string;
    stepName?: string;
    resolverUserId?: string;
    resolverUserName?: string;
    useTime?: string;
    stepStatus?: number;
    stepOrder?: string;
    resolverUserOrgNamePath?: string;
    comment?: string;
    sourceResolveType?: number;
    toUserId?: string;
    toUserName?: string;
    toStepName?: string;
    resolverType?: number;
    stepType?: string;
    specialLabel?: string;
}

export interface SortStepDto {
    stepId?: string;
    stepName?: string;
    stepStatus?: number;
    stepType?: string;
}

export enum TaskCommonStatusEnum {
    done = 2,
    reStart = 9,
    recallStart = 30,
    skippedWhenEmptyResolver = 32,
    skippedWhenSameApprover = 33,
    recallActivity = 31,
    handover = 20,
    start = 0,
    canceled = -1,
    delay = 23,
    extraAppend = 21,
    extraInsert = 22,
    rejectStart = 10,
    rejectActivity = 11,
    rejectStartDirect = 12,
    rejectActivityDirect = 13,
    interveneJumpStartNode  = 45
}
