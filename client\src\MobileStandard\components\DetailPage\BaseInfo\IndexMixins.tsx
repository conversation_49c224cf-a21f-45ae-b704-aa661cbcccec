import { Component, Prop, Vue } from 'vue-property-decorator';
import lodash from 'lodash';
import { baseInfoService } from './service';
import { InstanceCollapseItem } from '../..';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import { AgentUserDto, AgentUserPositionDto, InstanceBaseInfoDto } from './type';
import { authService, UserPosition } from '@/MobileStandard/services/auth';
import { guidHelper, subjectHelper } from '@/MobileStandard/common/utils';
import { dateHelper } from '@/MobileStandard/common/utils/date-helper';
import { RejectTypeEnum } from '../PageBottom/types';

@Component
export class IndexMixins extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: true }) editable!: boolean;
  @Prop() urlParamteres!: UrlParametersDto;
  baseInfo: InstanceBaseInfoDto = {
    topic: '',
    userId: undefined,
    userName: '',
    userAccount: '',
    positionId: undefined,
    positionName: '',
    organizationId: '',
    organizationPath: '',
    organizationPathText: '',
    topBusinessTypeName: '',
    processName: '',
    processId: '',
    parentProcessId: '',
    category: ''
  };
  positions: any = [];
  users: AgentUserDto[] = [];
  customProcessTopic = '';
  keyParameters = [];
  formInfo: any = {};
  systemPar: any = {};
  topicChange = lodash.debounce(this.combineTopic, 500);
  userNameVisible = false;
  positionNameVisible = false;
  topicChangeCount = 0;
  onPositionIdChange(value: any, index: number) {
    this.baseInfo.positionId = value.value;
    const position = lodash.find(this.positions, (p: UserPosition) => p.value === value.value);
    this.baseInfo.positionName = position ? position.label : '';
    let organizationId = '';
    if (position && position.organizationPath) {
      const paths = position.organizationPath.split('_');
      organizationId = paths[paths.length - 1].split('.')[0];
    }
    this.baseInfo.organizationId = organizationId;
    this.baseInfo.organizationPath = position ? position.organizationPath : '';
    this.baseInfo.organizationPathText = position ? position.organizationPathText : '';
    this.topicChangeCount += 1;
    if (this.baseInfo.organizationPath) {
      this.getAuathId();
    } else {
      this.urlParamteres.authId = '';
      this.customProcessTopic = '';
      this.keyParameters = [];
      this.combineTopic();
      subjectHelper.getSubject('baseInfo$').next(this.baseInfo);
    }
    this.positionNameVisible = false;
  }
  onUserIdChange(value: any, index: number) {
    this.baseInfo.userId = value.value;
    const user = lodash.find(this.users, (p: AgentUserDto) => p.value === value.value);
    this.baseInfo.userName = user ? user.label : '';
    this.baseInfo.userAccount = user ? user.userAccount : '';
    this.positions = user ? user.positions : [];
    const position = user ? lodash.find(user.positions, (p: AgentUserPositionDto) => p.primaryPosition) : undefined;
    this.baseInfo.positionId = position ? position.value as string : undefined;
    this.baseInfo.positionName = position ? position.label : '';
    let organizationId = '';
    if (position && position.organizationPath) {
      const paths = position.organizationPath.split('_');
      organizationId = paths[paths.length - 1].split('.')[0];
    }
    this.baseInfo.organizationId = organizationId;
    this.baseInfo.organizationPath = position ? position.organizationPath : '';
    this.baseInfo.organizationPathText = position ? position.organizationPathText : '';
    this.topicChangeCount += 1;
    if (this.baseInfo.organizationPath) {
      this.getAuathId(true);
    } else {
      this.urlParamteres.authId = '';
      this.customProcessTopic = '';
      this.keyParameters = [];
      this.combineTopic(true);
      subjectHelper.getSubject('baseInfo$').next(this.baseInfo);
    }
    this.userNameVisible = false;
  }
  combineTopic(isUserChange = false) {
    if (this.customProcessTopic && (this.urlParamteres.number ? this.topicChangeCount > 1 : true)) {
      let topic: any = this.customProcessTopic;
      const pars = Object.assign(this.formInfo, this.systemPar);
      this.keyParameters.forEach((v: any) => {
        const key = '/\\[@:' + v.keyParameter + '\\]/g';
        if (pars && pars[v.keyParameter]) {
          if (Array.isArray(pars[v.keyParameter])) {
            let paramValue = '';
            if (JSON.stringify(pars[v.keyParameter]).indexOf('label') > -1) {
              paramValue = pars[v.keyParameter][0].label;
            } else if (JSON.stringify(pars[v.keyParameter]).indexOf('userName') > -1) {
              paramValue = pars[v.keyParameter][0].userName;
            } else if (JSON.stringify(pars[v.keyParameter]).indexOf('organizationName') > -1) {
              paramValue = pars[v.keyParameter][0].organizationName;
            }
            topic = topic.replace(eval(key), paramValue);
          } else {
            topic = topic.replace(eval(key), pars[v.keyParameter]);
          }
        } else {
          topic = topic.replace(eval(key), '');
        }
      });
      if (topic.indexOf('[@') > -1) {
        this.baseInfo.topic = `${this.baseInfo.userName}${this.$t('fields.d')}${this.baseInfo.processName}`;
      } else {
        this.baseInfo.topic = topic;
      }
    } else {
      if (!this.baseInfo.topic || this.baseInfo.topic.indexOf('undefined') > -1 || isUserChange) {
        this.baseInfo.topic = `${this.baseInfo.userName}${this.$t('fields.d')}${this.baseInfo.processName}`;
      }
    }
  }
  get userNameIndex() {
    const index = lodash.findIndex(this.users, (s: any) => s.value === this.baseInfo.userId);
    return index > -1 ? index : 0;
  }
  get positionNameIndex() {
    const index = lodash.findIndex(this.positions, (s: any) => s.value === this.baseInfo.positionId);
    return index > -1 ? index : 0;
  }
  public getValues() {
    return { isSuccess: true, data: this.baseInfo };
  }
  getCustomProcessTopicKeyParameter(isUserChange = false) {
    // 获取自定义标题表达式和关键参数
    const number = this.urlParamteres.againStartNumber && !this.urlParamteres.processVersionChange ?
      this.urlParamteres.againStartNumber : !this.urlParamteres.authId ?
        this.urlParamteres.number : '';
    const authId = this.urlParamteres.againStartNumber && !this.urlParamteres.processVersionChange ?
      '' : this.urlParamteres.authId;
    baseInfoService.getCustomProcessTopicKeyParameter(this.baseInfo.parentProcessId || '',
      authId || '',
      number || '')
      .subscribe((data: any) => {
        this.customProcessTopic = data.customProcessTopic;
        this.keyParameters = data.keyParameters;
        this.topicChange(isUserChange);
      });
  }
  getAuathId(isUserChange = false) {
    baseInfoService.getAuthId(this.baseInfo.parentProcessId || '',
      this.baseInfo.organizationPath || '').subscribe((d: string) => {
        this.urlParamteres.authId = d;
        subjectHelper.getSubject('baseInfo$').next(this.baseInfo);
        this.getCustomProcessTopicKeyParameter(isUserChange);
      });
  }
  created() {
    if (this.urlParamteres.pageType === 'start') {
      // 订阅表单数据
      subjectHelper.getSubject('formInfo$').subscribe((s: any) => {
        this.topicChangeCount += 1;
        this.formInfo = this.urlParamteres.againStartNumber ? Object.assign(s, this.baseInfo) : s;
        this.topicChange();
      });
      subjectHelper.getSubject('systemPar$').subscribe((s: any) => {
        this.systemPar = s;
        this.topicChange();
      });
      if (this.urlParamteres.draftId && !this.urlParamteres.number
        && !this.urlParamteres.againStartNumber) {
        // 订阅草稿数据
        subjectHelper.getSubject('draftInfo$').subscribe((s: any) => {
          this.baseInfo = s.data.baseInfo;
          this.baseInfo.parentProcessId = this.baseInfo.parentProcessId ? this.baseInfo.parentProcessId : this.baseInfo.processId;
          const user = lodash.find(this.users, (p: AgentUserDto) => p.value === this.baseInfo.userId);
          this.positions = user ? user.positions : [];
          if (!this.urlParamteres.authId || (this.urlParamteres.authId
            && this.urlParamteres.authId === guidHelper.empty())) {
            if (this.baseInfo.organizationPath) {
              this.getAuathId();
            }
          } else {
            subjectHelper.getSubject('baseInfo$').next(this.baseInfo);
            this.getCustomProcessTopicKeyParameter();
          }
        });
      } else if (!this.urlParamteres.number) {
        // 订阅process数据,发起页才会有(退回的不算),再次发起的
        subjectHelper.getSubject('processInfo$').subscribe((s: any) => {
          // 如果是再次发起获取历史数据并比对版本
          if (this.urlParamteres.againStartNumber) {
            baseInfoService.getInstanceInfo(this.urlParamteres.againStartNumber || '',
              '', this.urlParamteres.movitech_token || '')
              .subscribe(data => {
                if (s.processVersion !== data.processVersion) {
                  this.urlParamteres.processVersionChange = true;
                }
                let organizationId = '';
                if (data && data.organizationPath) {
                  const paths = data.organizationPath.split('_');
                  organizationId = paths[paths.length - 1].split('.')[0];
                }
                this.baseInfo = {
                  topic: data.topic,
                  userId: lodash.toUpper(data.userId),
                  userName: data.userName,
                  positionId: lodash.toUpper(data.positionId),
                  positionName: data.positionName,
                  organizationId: organizationId,
                  organizationPath: data.organizationPath,
                  organizationPathText: data.organizationPathText,
                  category: data.category,
                  processName: data.processName,
                  processId: data.processId,
                  parentProcessId: data.parentProcessId ? data.parentProcessId : data.processId
                };
                this.baseInfo.startDate = dateHelper.dateFormat(new Date(), 'YYYY-MM-DD HH:mm');
                const user = lodash.find(this.users, (p: AgentUserDto) => p.value === this.baseInfo.userId);
                this.positions = user ? user.positions : [];
                if (!this.urlParamteres.authId || (this.urlParamteres.authId
                  && this.urlParamteres.authId === guidHelper.empty())) {
                  if (this.baseInfo.organizationPath) {
                    this.getAuathId();
                  }
                } else {
                  subjectHelper.getSubject('baseInfo$').next(this.baseInfo);
                  this.getCustomProcessTopicKeyParameter();
                }
              });
          } else {
            this.baseInfo.processId = this.urlParamteres.processId;
            this.baseInfo.parentProcessId = s.parentProcessId ? s.parentProcessId : this.baseInfo.processId;
            this.baseInfo.processName = s.name;
            this.baseInfo.category = s.param1;
            this.baseInfo.processVersion = s.processVersion;
            if (this.baseInfo.positionId && this.baseInfo.processId) {
              if (!this.urlParamteres.authId || (this.urlParamteres.authId
                && this.urlParamteres.authId === guidHelper.empty())) {
                if (this.baseInfo.organizationPath) {
                  this.getAuathId();
                }
              } else {
                subjectHelper.getSubject('baseInfo$').next(this.baseInfo);
                this.getCustomProcessTopicKeyParameter();
              }
            }
          }
        });
      }
    }
  }
  mounted() {
    if (this.urlParamteres.number &&
      !this.urlParamteres.againStartNumber) {
      // 详情页或退回到发起的
      baseInfoService.getInstanceInfo(this.urlParamteres.number,
        this.urlParamteres.taskId || '', this.urlParamteres.movitech_token || '')
        .subscribe(data => {
          let organizationId = '';
          if (data && data.organizationPath) {
            const paths = data.organizationPath.split('_');
            organizationId = paths[paths.length - 1].split('.')[0];
          }
          this.baseInfo = {
            ...data, userId: lodash.toUpper(data.userId),
            positionId: lodash.toUpper(data.positionId),
            organizationId: organizationId
          };
          this.baseInfo.parentProcessId = this.baseInfo.parentProcessId ? this.baseInfo.parentProcessId : this.baseInfo.processId;
          const user = lodash.find(this.users, (p: AgentUserDto) => p.value === this.baseInfo.userId);
          this.positions = user ? user.positions : [];
          if (this.urlParamteres.pageType === 'start' &&
            ((this.baseInfo.status !== 'rejected' ||
              (this.baseInfo.istatus || 0).toString() !== RejectTypeEnum.rejectStartDirect) &&
              (!this.urlParamteres.authId || (this.urlParamteres.authId
                && this.urlParamteres.authId === guidHelper.empty())))) {
            if (this.baseInfo.organizationPath) {
              this.getAuathId();
            }
          } else {
            subjectHelper.getSubject('baseInfo$').next(this.baseInfo);
            this.getCustomProcessTopicKeyParameter();
          }
        });
    }
    // 发起页面需要的
    if (this.urlParamteres.pageType === 'start') {
      // 加载代理人
      baseInfoService.getStartUsers().subscribe(data => {
        if (data) {
          lodash.forEach(data, (d: any) => {
            d.value = lodash.toUpper(d.value);
            d.positions = (d.positions || []).map((m: any) => {
              return {
                ...m,
                value: lodash.toUpper(m.value)
              };
            });
            // 把当前登录人放在第一个
            if (d.value === lodash.toUpper(authService.user.id)) {
              this.users.splice(0, 0, d);
            } else {
              this.users.push(d);
            }
          });
          if (this.baseInfo.userId) {
            const user = lodash.find(this.users, (p: AgentUserDto) => p.value === this.baseInfo.userId);
            this.positions = user ? user.positions : [];
          } else {
            this.positions = this.users[0].positions;
          }
        }
        if (!this.urlParamteres.draftId && !this.urlParamteres.number
          && !this.urlParamteres.againStartNumber) {
          // 新发起页面、没草稿、没流程实例
          this.baseInfo.userId = lodash.toUpper(authService.user.id);
          this.baseInfo.userName = authService.user.name;
          this.baseInfo.userAccount = authService.user.account;
          this.baseInfo.startDate = dateHelper.dateFormat(new Date(), 'YYYY-MM-DD HH:mm');
          if (this.urlParamteres.postId) {
            const position = lodash.find(this.positions, (p: UserPosition) => p.value === lodash.toUpper(this.urlParamteres.postId));
            if (position) {
              this.baseInfo.positionId = position.value;
              this.baseInfo.positionName = position.label;
              let organizationId = '';
              if (position.organizationPath) {
                const paths = position.organizationPath.split('_');
                organizationId = paths[paths.length - 1].split('.')[0];
              }
              this.baseInfo.organizationId = organizationId;
              this.baseInfo.organizationPath = position.organizationPath;
              this.baseInfo.organizationPathText = position.organizationPathText;
            }
          }
          if (this.positions.length === 1) {
            this.baseInfo.positionId = this.positions[0].value;
            this.baseInfo.positionName = this.positions[0].label;
            let organizationId = '';
            if (this.positions[0].organizationPath) {
              const paths = this.positions[0].organizationPath.split('_');
              organizationId = paths[paths.length - 1].split('.')[0];
            }
            this.baseInfo.organizationId = organizationId;
            this.baseInfo.organizationPath = this.positions[0].organizationPath;
            this.baseInfo.organizationPathText = this.positions[0].organizationPathText;
          }
          if (this.baseInfo.positionId && this.baseInfo.processId) {
            if (!this.urlParamteres.authId || (this.urlParamteres.authId
              && this.urlParamteres.authId === guidHelper.empty())) {
              if (this.baseInfo.organizationPath) {
                this.getAuathId();
              }
            } else {
              subjectHelper.getSubject('baseInfo$').next(this.baseInfo);
              this.getCustomProcessTopicKeyParameter();
            }
          }
        }
      });
    }
  }
}
