import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import { Component } from 'vue-property-decorator';

@Component
export class BaseInfo extends mixins(IndexMixins) {
    render() {
        return this.visible ? <instance-collapse-item title={this.$l.getLocale('fields.baseInfo')}>
            <van-field
                v-model={this.baseInfo.topic}
                rows={1}
                autosize
                maxlength={500}
                type='textarea'
                placeholder={`${this.$l.getLocale('fields.please')}${this.$l.getLocale('fields.input')}`}
                label={this.$l.getLocale('fields.processTopic')}
                required={this.editable}
                input-align='right'
                error-message-align='right'
                readonly={!this.editable}
                rules={[{
                    required: this.editable,
                    message: `${this.$l.getLocale('fields.please')}${this.$l.getLocale('fields.input')}`
                }]} />

            <van-field
                value={this.baseInfo.userName}
                placeholder={`${this.$l.getLocale('fields.please')}${this.$l.getLocale('fields.select')}`}
                label={this.$l.getLocale('fields.applicant')}
                required={this.editable}
                input-align='right'
                error-message-align='right'
                readonly={!this.editable}
                is-link={this.editable}
                rules={[{
                    required: this.editable,
                    message: `${this.$l.getLocale('fields.please')}${this.$l.getLocale('fields.select')}`
                }]}
                on-click={() => this.userNameVisible = this.editable} />
            <van-popup v-model={this.userNameVisible}
                position='bottom'>
                <van-picker
                    show-toolbar
                    columns={this.users}
                    value-key='label'
                    title={this.$l.getLocale('fields.applicant')}
                    default-index={this.userNameIndex}
                    on-confirm={this.onUserIdChange}
                    on-cancel={() => this.userNameVisible = false}
                />
            </van-popup>
            <van-field
                value={this.baseInfo.positionName}
                placeholder={`${this.$l.getLocale('fields.please')}${this.$l.getLocale('fields.select')}`}
                label={this.$l.getLocale('fields.applicantPosition')}
                required={this.editable}
                input-align='right'
                error-message-align='right'
                readonly={!this.editable}
                is-link={this.editable}
                rules={[{
                    required: this.editable,
                    message: `${this.$l.getLocale('fields.please')}${this.$l.getLocale('fields.select')}`
                }]}
                on-click={() => this.positionNameVisible = this.editable} />
            <van-popup v-model={this.positionNameVisible}
                position='bottom'>
                <van-picker
                    show-toolbar
                    columns={this.positions}
                    value-key='label'
                    title={this.$l.getLocale('fields.applicantPosition')}
                    default-index={this.positionNameIndex}
                    on-confirm={this.onPositionIdChange}
                    on-cancel={() => this.positionNameVisible = false}
                />
            </van-popup>
            <van-field
                value={this.baseInfo.organizationPathText}
                rows={1}
                autosize
                type='textarea'
                // label={this.$l.getLocale('fields.company')}
                label='所属组织'
                input-align='right'
                readonly
            />
            {
                this.urlParamteres.pageType !== 'start' ?
                    [
                        <van-field
                            value={this.urlParamteres.number}
                            label={this.$l.getLocale('fields.processNumber')}
                            input-align='right'
                            readonly
                        />,
                        <van-field
                            value={this.baseInfo.startDate}
                            label={this.$l.getLocale('fields.startTime')}
                            input-align='right'
                            readonly
                        />
                    ] : ''
            }
        </instance-collapse-item> : '';
    }
}
