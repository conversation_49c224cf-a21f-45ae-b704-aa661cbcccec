import { httpHelper } from '@/MobileStandard/common/utils';
import { Observable } from 'rxjs';
import { AgentUserDto, InstanceBaseInfoDto } from './type';
class BaseInfoService {
    getCustomProcessTopicKeyParameter(processId: string, authId: string, instanceNumber: string) {
        return httpHelper.get(`/api/process/v1/process-auth/topic-key-parameter`,
            { params: { 'process-id': processId, 'auth-id': authId, 'instance-number': instanceNumber } });
    }
    getInstanceInfo(number: string, taskId: string, token: string): Observable<InstanceBaseInfoDto> {
        const _url = `/api/process/v1/instances/${number}/info`;
        return httpHelper.get(_url, { params: { 'task-id': taskId, 'token': token } });
    }
    getStartUsers(): Observable<AgentUserDto[]> {
        const _url = `/api/platform/v1/agent-users?hasCurrentUser=true`;
        return httpHelper.get(_url);
    }
    getBusinessStartUser(bsid: string, btid: string, boid: string) {
        return httpHelper.get(`/api/process/endpoints/business-data/start-user`, { params: { bsid, btid, boid } });
    }
    getAuthId(processId: string, orgPath: string) {
        const _url = `/api/process/v1/process-auth/auth-id`;
        return httpHelper.get(_url, { params: { 'process-id': processId, 'org-path': orgPath } });
    }
}

export const baseInfoService = new BaseInfoService();
