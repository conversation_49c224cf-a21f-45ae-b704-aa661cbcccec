import { ValueLabelPair } from '@/MobileStandard/common/defines';

export interface InstanceBaseInfoDto {
  source?: string;
  topic?: string;
  userId?: string;
  userName?: string;
  userAccount?: string;
  ownerId?: string;
  positionId?: string;
  positionName?: string;
  organizationId?: string;
  organizationPathText?: string;
  organizationPath?: string;
  startDate?: string;
  status?: string;
  istatus?: number;
  formEditable?: boolean;
  processId?: string;
  processName?: string;
  processVersion?: string;
  topBusinessTypeName?: string;
  authenticationMemberId?: string;
  btid?: string;
  boid?: string;
  canStartInvalidWithdraw?: boolean;
  actions?: InstanceActionDto[];
  authId?: string;
  instanceId?: string;
  parentProcessId?: string;
  category?: string;
  isShouFaWen?: boolean;
}

export interface InstanceActionDto {
  code: string;
  name: string;
}

export interface AgentUserDto extends ValueLabelPair {
  positions: AgentUserPositionDto[];
  userAccount?: string;
}

export interface AgentUserPositionDto extends ValueLabelPair {
  organizationPathText: string;
  organizationPath: string;
  primaryPosition: boolean;
}

/**
 * 审批 状态枚举
 */
export enum ApproveState {
  start = 'start',
  ready = 'ready',
  processing = 'processing',
  approved = 'approved',
  refused = 'refused',
  canceled = 'canceled',
  rejected = 'rejected',
  todo = 'todo',
  suspended = 'suspended',
}

/**
 * 完成的审批状态
 */
export const completedStatuses = [ApproveState.approved, ApproveState.refused, ApproveState.canceled];
