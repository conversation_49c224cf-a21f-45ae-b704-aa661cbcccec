import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import styles from './document.module.less';
import lodash from 'lodash';
import { documentService } from './service';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import { Settings } from '@/MobileStandard/common/defines';
import { guidHelper, subjectHelper } from '@/MobileStandard/common/utils';
import { downloadHelper } from '@/MobileStandard/common/utils/download-helper';

@Component
export class IndexMixins extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop({ default: true }) upload!: boolean;
    @Prop({ default: true }) delete!: boolean;
    @Prop({ default: true }) download!: boolean;
    @Prop() urlParamteres!: UrlParametersDto;
    fileList: any = [];
    UploadFileAcceptSufixes = ['doc', 'docx'];
    @Emit('document-click')
    documentClick(fileId: any, isGo: boolean) { }
    getSuffix(name: string) {
        return name.slice(name.lastIndexOf('.') + 1).toLowerCase();
    }
    beforeRead(e: any) {
        let result = true;
        if (Array.isArray(e)) {
            e.map((ee: any) => {
                if (!this.suffixIncludes(ee)) {
                    result = false;
                }
            });
        } else {
            if (!this.suffixIncludes(e)) {
                result = false;
            }
        }
        if (!result) {
            this.$toast({ type: 'fail', overlay: true, forbidClick: true, message: '上传附件格式错误' });
        }
        if (result) {
            if (Array.isArray(e)) {
                e.map((ee: any) => {
                    if (ee.size > (Settings.UploadFileSize * 1024 * 1024)) {
                        result = false;
                    }
                });
            } else {
                if (e.size > (Settings.UploadFileSize * 1024 * 1024)) {
                    result = false;
                }
            }
            if (!result) {
                this.$toast({ type: 'fail', overlay: true, forbidClick: true, message: `上传附件大小不能超过${Settings.UploadFileSize}MB` });
            }
        }

        return result;
    }
    suffixIncludes(e: any) {
        const suffix = this.getSuffix(e.name);
        if (!this.UploadFileAcceptSufixes.includes(suffix)) {
            return false;
        }
        return true;
    }
    afterRead(e: any) {
        if (Array.isArray(e)) {
            e.forEach((f: any) => {
                f.file['id'] = guidHelper.generate();
                f.status = 'uploading';
                f.message = this.$l.getLocale('tips.uploading');
                this.uploadGW(f);
            });
        } else {
            e.file['id'] = guidHelper.generate();
            e.status = 'uploading';
            e.message = this.$l.getLocale('tips.uploading');
            this.uploadGW(e);
        }
    }
    uploadGW(e: any) {
        const formData = new FormData();
        formData.append('file', e.file);
        documentService.uploadGW(formData).subscribe((data: any) => {
            if (data && data[0]) {
                e.message = '';
                e.status = '';
                this.fileList.push({
                    id: data[0].id,
                    name: e.file.name,
                    uploadTime: data[0].uploadTime,
                    uploadUserName: data[0].uploadUserName,
                    uploadUserId: data[0].uploadUserId,
                    operationType: 1,
                    isNew: true
                });
                this.documentClick(data[0].id, false);
            } else {
                e.status = 'failed';
                e.message = this.$l.getLocale('tips.uploadFail');
            }
        });
    }
    viewGW(record: any) {
        // 告诉外部页面我点了公文
        this.documentClick(record.id, true);
    }
    deleteGW(id: any) {
        const deletefile = lodash.find(this.fileList, (f: any) => f.id === id);
        if (deletefile != null) {
            deletefile.operationType = -1;
        }
        this.documentClick('', false);
    }
    getFileIcon(item: any) {
        let name = '';
        const extension = item.name.split('.').pop();
        switch (extension) {
            case 'pdf':
                name = 'pdf';
                break;
            case 'jpg':
                name = 'jpg';
                break;
            case 'png':
                name = 'png';
                break;
            case 'bmp':
                name = 'bmp';
                break;
            case 'doc':
            case 'docx':
                name = 'world';
                break;
            case 'xls':
            case 'xlsx':
                name = 'excel';
                break;
            case 'ppt':
                name = 'ppt';
                break;
            case 'text':
            case 'txt':
                name = 'txt';
                break;
            case 'rar':
            case 'zip':
            case 'arj':
                name = 'yasuobao';
                break;
            default:
                name = 'yasuobao';
                break;
        }
        return require(`../../../../assets/images/${name}.png`);
    }
    downGWYY(name: string) {
        const newName = lodash.replace(name, `.${this.getSuffix(name)}`, '.pdf');
        const res = `/api/process/v1/contractlock/downloadcontract?number=${this.urlParamteres.number || ''}`;
        const link = document.createElement('a');
        link.href = window.location.href.replace(this.$route.fullPath, '/' + lodash.trimStart(lodash.trimStart(res, '//'), '/'));
        link.download = newName;
        link.click();
    }
    public getValues() {
        return lodash.filter(this.fileList, (f: any) => f.operationType !== -1);
    }
    public rename(name: string) {
        const file = lodash.filter(this.fileList, (f: any) => f.operationType !== -1)[0];
        const suffix = this.getSuffix(file.name);
        console.log(suffix);
        file.name = `${name}.${suffix}`;
    }
    created() {
        // 草稿打开从草稿获取办理意见
        if (this.urlParamteres.pageType === 'start' && this.urlParamteres.draftId &&
            !this.urlParamteres.number && !this.urlParamteres.againStartNumber) {
            subjectHelper.getSubject('draftInfo$').subscribe((s: any) => {
                this.fileList = s.data.instanceDocument || [];
                const newFiles = lodash.filter(this.fileList, (f: any) => f.operationType !== -1);
                if (newFiles.length > 0) {
                    this.documentClick(newFiles[0].id, false);
                }
            });
        }
    }
    mounted() {
        if (this.urlParamteres.number || this.urlParamteres.againStartNumber) {
            const number = this.urlParamteres.number || this.urlParamteres.againStartNumber;
            documentService.get(number || '').subscribe((data: any) => {
                const newData = (data || []).map((m: any, i: number) => {
                    return {
                        ...m,
                        isNew: true,
                        operationType: this.urlParamteres.pageType === 'start' &&
                            this.urlParamteres.againStartNumber && !this.urlParamteres.number ? 1 : m.operationType
                    };
                });
                this.fileList = [...newData];
                const newFiles = lodash.filter(this.fileList, (f: any) => f.operationType !== -1);
                if (newFiles.length > 0) {
                    this.documentClick(newFiles[0].id, false);
                }
            });
        }
    }
}
