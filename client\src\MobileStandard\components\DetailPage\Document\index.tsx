import { Component } from 'vue-property-decorator';
import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import styles from './document.module.less';
import lodash from 'lodash';
import { authService } from '@/MobileStandard/services/auth';
import { dateHelper } from '@/MobileStandard/common/utils/date-helper';

@Component
export class Document extends mixins(IndexMixins) {
    render() {
        return this.visible ?
            <instance-collapse-item title='公文正文'>
                {
                    lodash.map(lodash.filter(this.fileList, (ff: any) => ff.operationType !== -1), (f: any) => {
                        return <div class={styles.attachment}>
                            <div class={styles.icon}>
                                <van-icon
                                    name={this.getFileIcon(f)}
                                    size='38'
                                />
                            </div>
                            <div class={styles.content}>
                                <span class={styles.name} on-click={() => this.viewGW(f)}>{f.name}</span>
                                <div class={styles.second_row}>
                                    <span class={styles.user_name}>{f.uploadUserName + ' | ' + dateHelper.dateFormat(f.uploadTime)}</span>
                                </div>
                            </div>
                            {
                                this.delete && authService.user.account === f.uploadUserId ?
                                    <div class={styles.buttons}>
                                        <div class={styles.delete}
                                            on-click={() => this.deleteGW(f.id)}>
                                            <i class='iconfont icon-btn_deletelist'
                                                style='color:#666666;font-size:20px;'></i>
                                        </div>
                                    </div> : ''
                            }
                            {
                                this.download && f.sealState === '2' && this.urlParamteres.number ?
                                    <div class={styles.buttons}>
                                        <div class={styles.sealdowloand}
                                            on-click={() => this.downGWYY(f.name)}>
                                            <div class={styles.sealdowloand_png}></div>
                                        </div>
                                    </div> : ''
                            }
                        </div>;
                    })
                }
                {
                    this.upload &&
                        lodash.filter(this.fileList || [], (f: any) => f.operationType !== -1).length === 0 ?
                        <div class={styles.upload_area}>
                            <van-uploader
                                accept=''
                                preview-full-image={false}
                                before-read={(e: any) => this.beforeRead(e)}
                                after-read={(e: any) => this.afterRead(e)}
                                class={styles.uploader}
                            >
                                <img src={require(`../../../../assets/images/btn_addpic.png`)} />
                            </van-uploader>
                        </div> : ''
                }
            </instance-collapse-item> : '';
    }
}
