import { httpHelper } from '@/MobileStandard/common/utils';
import { downloadHelper } from '@/MobileStandard/common/utils/download-helper';
import { Observable } from 'rxjs';

class DocumentService {
  uploadGW(formData: any) {
    const _url = `/api/process/v1/documents/upload/gw`;
    return httpHelper.post(_url, formData);
  }
  downGW(id: string, name: string): Observable<any> {
    const _url = `/api/process/v1/documents/download/gw/${id}`;
    return httpHelper.get(_url, {}, { loading: false });
  }
  downGWYY(number: string, name: string): Observable<any> {
    const _url = `/api/process/v1/contractlock/downloadcontract?number=${number}`;
    return downloadHelper.get(_url, name);
  }
  get(number: string): Observable<any> {
    const _url = `/api/process/v1/documents/gw/${number}`;
    return httpHelper.get(_url);
  }
}

export const documentService = new DocumentService();
