import { Component, Vue } from 'vue-property-decorator';
import { WebOffice } from '../WebOffice';

@Component({
  components: {
    WebOffice
  }
})
export class IndexMixins extends Vue {
  fileId = '';
  fileType = '';
  fileInfo = '';
  visible = false;
  created() {
    this.fileId = this.$route.params['fileId'] as string;
    this.fileType = this.$route.query['fileType'] as string || 'fujian';
    this.fileInfo = this.$route.query['fileInfo'] as string || '';
    this.visible = true;
  }
}
