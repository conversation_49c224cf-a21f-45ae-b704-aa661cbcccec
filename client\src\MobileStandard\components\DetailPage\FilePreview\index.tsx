import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import { Component } from 'vue-property-decorator';
import styles from './file-preview.module.less';

@Component
export class FilePreview extends mixins(IndexMixins) {
    render() {
        return <div class={styles.webofice}><web-office
            fileId={this.fileId}
            fileType={this.fileType}
            fileInfo={this.fileInfo}
            visible={this.visible}
            urlParamteres={{ pageType: 'approval' }}
            hasButton={true}
        /></div>;
    }
}
