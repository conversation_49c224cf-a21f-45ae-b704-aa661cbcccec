import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { formInfoService } from './service';
import * as comps from '@/MobileStandard/components';
import * as helper from '@/MobileStandard/common/utils';
import lodash from 'lodash';
import { from } from 'rxjs';
import { InstanceBaseInfoDto } from '../BaseInfo/type';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import { subjectHelper } from '@/MobileStandard/common/utils';
import { authService } from '@/MobileStandard/services/auth';
import { Settings } from '@/MobileStandard/common/defines';

@Component
export class IndexMixins extends Vue {
  @Prop({ default: true }) editable!: boolean;
  @Prop() urlParamteres!: UrlParametersDto;
  regScript = /<script>.*?<\/script>/gis;
  instance!: any;
  baseInfo!: InstanceBaseInfoDto;
  valueChange = lodash.debounce(this.onValuesChange, 500);
  formData!: any;
  systemPar!: any;
  @Emit('hengping')
  onHengping(html: any) { }
  onValuesChange() {
    if (this.instance) {
      subjectHelper.getSubject('formInfo$').next(this.instance.value);
    }
  }
  public getValues(isValidate: boolean = true, actionCode: any = '') {
    return from(new Promise((resolve: (value: any) => void, reject: (value: void) => void) => {
      let newData = this.instance ? this.instance.value : null;
      newData = Object.assign(newData, this.systemPar);
      if (isValidate) {
        const boid = this.urlParamteres.pageType === 'start' ? this.urlParamteres.boid : this.baseInfo.boid;
        const number = this.urlParamteres.number;
        const processId = this.baseInfo.parentProcessId;
        const processVersion = this.baseInfo.processVersion;
        if (this.instance.submitCheck) {
          this.instance.submitCheck(boid, number, processId, processVersion).then((tips: any) => {
            if (tips && tips.length > 0) {
              this.$toast({
                type: 'fail',
                forbidClick: true,
                message: lodash.join(tips, '\n')
              });
              reject();
            } else {
              newData = this.instance ? this.instance.value : null;
              newData = Object.assign(newData, this.systemPar);
              resolve({
                isSuccess: true, data: newData,
                isTJHFZ: this.instance.tjhfz && (lodash.startsWith(actionCode, 'approve_') ||
                actionCode === 'approve') ? this.instance.tjhfz() : false
              });
            }
          });
        } else {
          resolve({
            isSuccess: true, data: newData,
            isTJHFZ: this.instance.tjhfz && (lodash.startsWith(actionCode, 'approve_') ||
                actionCode === 'approve') ? this.instance.tjhfz() : false
          });
        }
      } else {
        resolve({
          isSuccess: true, data: newData,
          isTJHFZ: this.instance.tjhfz && (lodash.startsWith(actionCode, 'approve_') ||
                actionCode === 'approve') ? this.instance.tjhfz() : false
        });
      }
    }));
  }
  getFieldRight() {
    const pt = this.urlParamteres.pageType ? this.urlParamteres.pageType : 'start';
    const params = {
      processId: this.baseInfo.parentProcessId,
      taskId: this.urlParamteres.taskId,
      number: this.urlParamteres.number,
      pt: pt,
      isIndependent: this.urlParamteres.isIndependent
    };
    formInfoService.getFieldRight(params).subscribe(data => {
      this.instance.setFieldRight(data);
    });
  }
  setInstanceValue(data: any) {
    if (this.instance && this.instance.setValue) {
      this.instance.setValue(data);
    } else {
      this.valueChange();
    }
  }
  getFormData() {
    if (this.urlParamteres.draftId &&
      !this.urlParamteres.number &&
      !this.urlParamteres.againStartNumber) {
      this.setInstanceValue(this.formData);
      // 读权限
      this.getFieldRight();
    } else if (this.urlParamteres.boid &&
      this.urlParamteres.btid &&
      this.urlParamteres.bsid &&
      this.urlParamteres.pageType === 'start') {
      formInfoService.getBusinessFormParams(this.urlParamteres.bsid,
        this.urlParamteres.btid,
        this.urlParamteres.boid).subscribe(data => {
          this.setInstanceValue(data);
          // 读权限
          this.getFieldRight();
        });
    } else if (this.urlParamteres.number || this.urlParamteres.againStartNumber) {
      const number = this.urlParamteres.number ?
        this.urlParamteres.number :
        this.urlParamteres.againStartNumber;
      formInfoService.getFormParams(number || '').subscribe(data => {
        this.setInstanceValue(data);
        // 读权限
        this.getFieldRight();
      });
    } else {
      this.valueChange();
      // 读权限
      this.getFieldRight();
    }
  }
  getTemplate() {
    formInfoService.setConfig().then(() => {
      const number = this.urlParamteres.number ?
        this.urlParamteres.number :
        this.urlParamteres.processVersionChange ? '' :
          this.urlParamteres.againStartNumber;
      formInfoService.getTemplate(this.urlParamteres.taskId || '',
        this.baseInfo.parentProcessId || '',
        number || '').subscribe(html => {
          // 解析页面中的脚本
          const scripts = html.match(this.regScript) as RegExpMatchArray;
          if (scripts && scripts.length > 0) {
            html = html.replace(this.regScript, '');
            eval(scripts[0].replace(/^<script>|<\/script>$/g, ''));
            // 动态创建页面组件
            if (scripts && scripts.length > 0) {
              html = html.replace(this.regScript, '');
              eval(scripts[0].replace(/^<script>|<\/script>$/g, ''));
            } else {
              (window as any).component = {};
            }
            // 动态创建页面组件
            const options = Object.assign(
              {
                template: html,
              },
              (window as any).component
            );
            Vue.component('custom-form', options);
            const comp = Vue.component('custom-form');
            comp.prototype.editable = this.editable;
            comp.prototype.user = authService.user;
            comp.prototype.instanceNumber = this.urlParamteres.number;
            comp.prototype.processId = this.urlParamteres.processId;
            comp.prototype.print = false;
            comp.prototype.lodash = lodash;
            comp.prototype.hengping = require('@/assets/images/btn_hengping.png');
            comp.prototype.uploadFileSize = Settings.UploadFileSize;
            comp.prototype.uploadFileAcceptSufixes = Settings.UploadFileAcceptSufixes;
            comp.prototype.valueChange = this.valueChange;
            comp.prototype.onhengping = this.onHengping;
            comp.prototype.urlParamteres = this.urlParamteres;
            Object.keys(helper).forEach(h => {
              comp.prototype[h] = (helper as any)[h];
            });
            this.$nextTick(() => {
              const instance = new comp({ components: { ...comps }, i18n: this.$i18n, router: this.$router });
              instance.$mount('#form-info-html');
              (window as any).instance = instance;
              this.instance = instance;
              // 读默认数据
              this.getFormData();
            });
          }
        });
    });
  }
  created() {
    if (this.urlParamteres.draftId &&
      !this.urlParamteres.number &&
      !this.urlParamteres.againStartNumber) {
      // 订阅草稿数据
      subjectHelper.getSubject('draftInfo$').subscribe((s: any) => {
        this.formData = s.data.formData;
      });
    }
    subjectHelper.getSubject('baseInfo$').subscribe((s: InstanceBaseInfoDto) => {
      if (!this.baseInfo || !this.baseInfo.processId) {
        this.baseInfo = s;
        this.getTemplate();
      }
    });
    if (this.urlParamteres.pageType === 'start') {
      subjectHelper.getSubject('systemPar$').subscribe((s: any) => {
        this.systemPar = s;
      });
    }
  }
  mounted() {
  }
}
