import { httpHelper } from '@/MobileStandard/common/utils';
import { Observable } from 'rxjs';
class FormInfoService {
    private config: any = {};
    getTemplate(taskId: string, processId: string, instanceId: string) {
        const url = '/api/form/v1/templates/mobile';
        return httpHelper.get(url, { params: { 'process-id': processId, 'task-id': taskId, 'instance-id': instanceId } });
    }

    getFieldRight(params: any): Observable<any> {
        const _url = `/api/process/v1/field-right`;
        return httpHelper.get(_url, { params: params });
    }

    getBusinessFormParams(bsid: string, btid: string, boid: string) {
        return httpHelper.get(`/api/process/endpoints/business-data/forms`, { params: { bsid, btid, boid } });
    }

    getFormParams(instanceId: string) {
        return httpHelper.get(`/api/process/v1/instances/${instanceId}/form-params`);
    }

    setConfig(): any {
        return new Promise((resolve: any, reject: any) => {
            const _url = '/api/formDesginConfig';
            return httpHelper.get(_url, undefined, { loading: false }).subscribe(data => {
                this.config = data;
                resolve();
            }, (error: any) => reject());
        });
    }
    getConfig(key: string) {
        if (this.config) {
            return this.config[key];
        }
        return '';
    }
}

export const formInfoService = new FormInfoService();
