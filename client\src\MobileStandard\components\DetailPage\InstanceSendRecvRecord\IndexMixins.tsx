import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { instanceSendRecvRecordService } from './service';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
@Component
export class IndexMixins extends Vue {
    @Prop({ default: false }) visible!: boolean;

    @Prop() urlParamteres!: UrlParametersDto;

    @Prop() instanceId!: string;

    list: any = [];

    created() {
    }
    mounted() {
        if (this.instanceId) {
            instanceSendRecvRecordService.getListByInstanceId(this.instanceId).subscribe((data: any) => {
                this.list = data;
            });
        }
    }
}
