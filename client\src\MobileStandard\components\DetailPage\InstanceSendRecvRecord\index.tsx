import { Component } from 'vue-property-decorator';
import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import styles from './instance-send-recv-record.module.less';
import lodash from 'lodash';

@Component
export class InstanceSendRecvRecord extends mixins(IndexMixins) {
    render() {
        return this.visible ?
            <instance-collapse-item title={this.$l.getLocale('fields.sendRecvRecord')} class={styles.collapse}>
                <van-list>
                {
                    lodash.map(this.list, (d: any) => {
                        return (
                            <div>
                                <van-row class={styles.title}>
                                    <van-col span='9'>{d.userName}</van-col>
                                    <van-col span='3'>
                                        {d.actionCode ? this.$l.getLocale(`buttons.${(d.actionCode || '').toLocaleLowerCase()}`) : ''}
                                    </van-col>
                                    <van-col span='12' class={styles.approvalDate}>{d.approvalDate}</van-col>
                                </van-row>
                                <van-row class={styles.content}>{d.content}</van-row>
                                <van-divider class={styles.divider}/>
                            </div>
                        );
                    })
                }
                </van-list>
            </instance-collapse-item>
         : '';
    }
}
