import { httpHelper } from '@/MobileStandard/common/utils';
import { Observable } from 'rxjs';

class InstanceSendRecvRecordService {
  getListByInstanceId(instanceId: string): Observable<any[]> {
    const _url = `/api/process/v1/instanceSendRecvRecord/getListByInstanceId?instanceId=${instanceId}`;
    return httpHelper.get(_url, {}, { loading: false });
  }
}

export const instanceSendRecvRecordService = new InstanceSendRecvRecordService();
