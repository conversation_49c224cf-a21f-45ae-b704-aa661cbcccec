import lodash from 'lodash';
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import styles_p from '../../page-bottom.module.less';
import { Dialog } from 'vant';
import { CommonApproveComment } from '@/MobileStandard/components/CommonApproveComment';
@Component({
  components: {
    CommonApproveComment
  }
})
export class Approve extends Vue {
  @Prop() icon!: string;
  @Prop() code!: string;
  @Prop() type!: string;
  @Prop() name!: string;
  @Prop() defaultComment!: string;
  @Prop() instanceNumber!: string;
  @Prop() approvalRecord!: any;

  private visible = false;
  private comment = '';
  private cssName = '';
  private checkedVal = [];
  private tyxgyj: any = [];
  private modalTitle = '';
  private modalCommentPlaceholder = '';
  @Emit('submit')
  Submit(actionCode: string, data: any) { }

  onOk() {
    if (this.code === 'approve_agreeopinion' && (!this.tyxgyj || this.tyxgyj.length === 0)) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: `请选择同意以下办理人员意见`
      });
    } else if (!this.comment) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: this.modalCommentPlaceholder
      });
    } else {
      this.Submit(this.code, { targetUsers: [], resolveComment: this.comment });
      this.visible = false;
    }
  }
  private onApproval() {
    // 用印按钮特殊出
    if (this.code === 'approve_seal') {
      Dialog.alert({
        message: '请到PC端进行用印操作，如有疑问请联系管理员',
      }).then(() => {
        // on close
      });
    } else {
      if (this.defaultComment) {
        this.comment = this.defaultComment;
      } else {
        switch (this.code) {
          case 'approve_agreeopinion':
          case 'approve':
            this.comment = '';
            break;
          default:
            this.comment = this.name;
            break;
        }
      }
      this.visible = true;
    }
  }
  private onAgreeopinionChange(checkedValue: any) {
    this.tyxgyj = [];
    lodash.forEach(checkedValue, (c: any) => {
      const arrs = c.split('|&&|');
      if (lodash.findIndex(this.tyxgyj, `"${arrs[2]}"`) === -1) {
        this.tyxgyj.push(`"${arrs[2]}"`);
      }
    });
    this.comment = this.tyxgyj.length > 0 ? `同意${lodash.join(this.tyxgyj, '，')}意见` : '';
  }
  created() {
    switch (this.code) {
      case 'approve_agreeorigin':
      case 'approve_agreeopinion':
      case 'approve_opinion':
      case 'approve_ignore':
        this.cssName = this.code;
        break;
      default:
        this.cssName = 'approve';
        break;
    }
    switch (this.code) {
      case 'approve':
        this.modalTitle = '签字意见';
        this.modalCommentPlaceholder = '请输入签字意见';
        break;
      default:
        this.modalTitle = '审批意见';
        this.modalCommentPlaceholder = '请输入审批意见';
        break;
    }
  }

  render() {
    return <div class={styles_p.action}>
      <div class={styles_p.button + ' ' + styles_p[this.cssName]}
        on-click={() => this.onApproval()}>
        <i class={`iconfont icon-${this.icon}`} />
        <span>{this.name}</span>
      </div>
      <van-action-sheet v-model={this.visible}
        round={false}
        class={styles_p.sheet}
        get-container='#approval'
      >
        <div class={styles_p.top}>
          <van-icon name='arrow-down' size='20'
            class={styles_p.closeIcon}
            on-click={() => this.visible = false}
          ></van-icon>
          <span>{this.name}</span>
        </div>
        <div class={styles_p.content}>
          {
            this.code === 'approve_agreeopinion' ?
              <div class={styles_p.block}>
                <span class={styles_p.title}>同意以下办理人员意见:</span>
                <van-checkbox-group
                  v-model={this.checkedVal}
                  on-change={this.onAgreeopinionChange}>
                  {
                    lodash.map(this.approvalRecord, (his: any) => {
                      return <van-checkbox shape='square' name={his.value}>{his.label}</van-checkbox>;
                    })
                  }
                </van-checkbox-group>
              </div> : ''
          }
          <div class={styles_p.block}>
            <span class={styles_p.title}>{this.modalTitle}</span>
            <van-field
              v-model={this.comment}
              rows='5'
              autosize
              label=''
              type='textarea'
              placeholder={this.modalCommentPlaceholder}
              maxlength={1000}
              show-word-limit
              class='structure_up_down'
            />
          </div>
          <div class={styles_p.block}>
            <common-approve-comment on-change={(value: any) => this.comment = value} />
          </div>
        </div>
        <div class={styles_p.bottom}>
          <div class={styles_p.submit} on-click={() => this.onOk()}>{this.$l.getLocale('buttons.submit')}</div>
        </div>
      </van-action-sheet>
    </div>;
  }
}
