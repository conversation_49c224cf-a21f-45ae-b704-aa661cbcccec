import { guidHelper } from '@/MobileStandard/common/utils';
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import styles_p from '../../page-bottom.module.less';

@Component
export class Cancel extends Vue {
  @Prop() icon!: string;
  @Prop() code!: string;
  @Prop() type!: string;
  @Prop() name!: string;
  @Prop() taskId!: string;
  @Prop() defaultComment!: string;
  @Prop() hasBranch!: boolean;
  @Prop() instanceNumber!: string;

  private comment = '';
  private allCount = 1000;
  private allowCount = this.allCount;
  private visible = false;

  @Emit('submit')
  Submit(actionCode: string, data: any) { }

  onOk() {
    if (!this.comment) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: `请输入${this.name}意见`
      });
    } else {
      if (this.taskId && this.taskId !== guidHelper.empty()) {
        this.Submit('cancel', { targetUsers: [], resolveComment: this.comment });
        this.visible = false;
      } else {
        this.Submit('cancel', { targetUsers: [], resolveComment: this.comment, isCallSystem: true });
        this.visible = false;
      }
    }
  }
  private onCancel() {
    if (this.defaultComment) {
      this.comment = this.defaultComment;
    } else {
      this.comment = this.name;
    }
    this.allowCount = this.allCount - this.comment.length;
    this.visible = true;
  }
  created() {
  }

  render() {
    return (
      <div class={styles_p.action}>
        <div class={styles_p.button + ' ' + styles_p[this.code]}
          on-click={() => this.onCancel()}>
          <i class={`iconfont icon-${this.icon}`} />
          <span>{this.name}</span>
        </div>
        <van-action-sheet v-model={this.visible}
          round={false}
          class={styles_p.sheet}
          get-container='#approval'
        >
          <div class={styles_p.top}>
            <van-icon name='arrow-down' size='20'
              class={styles_p.closeIcon}
              on-click={() => this.visible = false}
            ></van-icon>
            <span>{this.name}</span>
          </div>
          <div class={styles_p.content}>
            <div class={styles_p.block}>
              <span class={styles_p.title}>{this.$l.getLocale('fields.approvalComment')}</span>
              <van-field
                v-model={this.comment}
                rows='5'
                autosize
                label=''
                type='textarea'
                placeholder={this.$l.getLocale(['fields.please', 'fields.input', 'fields.approvalComment'])}
                maxlength={1000}
                show-word-limit
                class='structure_up_down'
              />
            </div>
          </div>
          <div class={styles_p.bottom}>
            <div class={styles_p.submit} on-click={() => this.onOk()}>{this.$l.getLocale('buttons.submit')}</div>
          </div>
        </van-action-sheet>
      </div>
    );
  }
}
