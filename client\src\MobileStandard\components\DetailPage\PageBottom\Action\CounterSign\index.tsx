import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import { ActionDataDto } from '../../types';
import styles_p from '../../page-bottom.module.less';
import lodash from 'lodash';
import { authService } from '@/MobileStandard/services/auth';
import { SeniorSelectUser } from '@/MobileStandard/components';
import { pageBottomService } from '../../service';

@Component({
  components: {
    SeniorSelectUser
  }
})
export class CounterSign extends Vue {
  @Prop() icon!: string;
  @Prop() code!: string;
  @Prop() type!: string;
  @Prop() name!: string;
  @Prop() defaultComment!: string;
  @Prop() taskId!: string;

  private signType: 'insert' | 'append' = 'insert';
  private data: ActionDataDto = {
    targetUsers: [],
    targetUserId: '',
    targetUserName: '',
    resolveComment: '',
    targetUserOrganizationPath: '',
    targetUserOrganizationPathText: '',
    isCallSystem: true
  };
  private visible = false;
  private userTransferInputVisible = false;
  private userTransferInputItems: any = [];
  private userAvatars: any = {};
  @Emit('submit')
  Submit(actionCode: string, data: any) { }

  onOk() {
    if (this.data.targetUsers.length === 0) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: `请选择${this.name}人员`
      });
    } else if (this.data.targetUsers.find((x: { UserId: string | undefined; }) => x.UserId === authService.user.id) != null) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: '本人不可以意见征询、委托给自己'
      });
    } else if (!this.data.resolveComment) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: `请输入${this.name}意见`
      });
    } else {
      this.Submit('counter-sign', { ...this.data, signType: this.signType });
      this.visible = false;
    }
  }
  private onCounterSign() {
    this.data.resolveComment = '';
    if (this.defaultComment) {
      this.data.resolveComment = this.defaultComment;
    }
    pageBottomService.getConsultState(this.taskId).subscribe((s: any) => {
      if (!s) {
        this.$toast({
          type: 'fail',
          forbidClick: true,
          message: this.$l.getLocale('tips.sign-limit')
        });
      } else {
        this.visible = true;
      }
    });
  }
  private onUserTransferInput(type: any, typeIndex: number) {
    this.userTransferInputVisible = true;
    this.userTransferInputItems = lodash.cloneDeep(lodash.map(this.data.targetUsers,
      (t: any) => {
        return {
          userId: t.UserId,
          userName: t.UserName,
          organizationIdPath: t.UserOrganizationPath,
          organizationNamePath: t.UserOrganizationPathText
        };
      }));
  }
  private onCounterSignUserChange(e: any) {
    this.data.targetUsers = [];
    if (e && e.length > 0) {
      this.data.targetUsers = lodash.map(e, (p: any) => {
        if (!this.userAvatars[p.userId]) {
          this.userAvatars[p.userId] = p.avatar;
        }
        return {
          UserId: p.userId,
          UserName: p.userName,
          OrganizationIdPath: p.organizationIdPath,
          OrganizationNamePath: p.organizationNamePath
        };
      });
    }
    this.userTransferInputVisible = false;
  }
  private onDel(index: number) {
    this.data.targetUsers.splice(index, 1);
  }

  created() {
  }

  render() {
    return (
      <div class={styles_p.action}>
        <div class={styles_p.button + ' ' + styles_p[this.code]}
          on-click={() => this.onCounterSign()}>
          <i class={`iconfont icon-${this.icon}`} />
          <span>{this.name}</span>
        </div>
        <van-action-sheet v-model={this.visible}
          round={false}
          class={styles_p.sheet}
          get-container='#approval'
        >
          <div class={styles_p.top}>
            <van-icon name='arrow-down' size='20'
              class={styles_p.closeIcon}
              on-click={() => this.visible = false}
            ></van-icon>
            <span>{this.name}</span>
          </div>
          <div class={styles_p.content}>
            <div class={styles_p.block}>
              <span class={styles_p.title}>{`${this.name}人员`}</span>
              <div class={styles_p.img}
                on-click={() => this.onUserTransferInput('ccUser', -1)}>
                <i class='iconfont icon-input_adduser'
                  style='color:#0e9266;font-size:15px;'></i>
              </div>
              <div class={styles_p.users}>
                {
                  lodash.map(this.data.targetUsers || [], (u: any, ui: number) => {
                    return <div class={styles_p.user}>
                      <div class={styles_p.avator}>
                        <img src={this.userAvatars[u.UserId] || require('../../../../../../assets/images/Avatar_default.png')} />
                        <div class={styles_p.del}
                          on-click={() => this.onDel(ui)}>
                          <i class='iconfont icon-pop_btn_close'
                            style='color:#be4848;font-size:12px;'></i>
                        </div>
                      </div>
                      <span>{u.UserName}</span>
                    </div>;
                  })
                }
              </div>
            </div>
            <div class={styles_p.block}>
              <span class={styles_p.title}>{`${this.name}意见`}</span>
              <van-field
                v-model={this.data.resolveComment}
                rows='5'
                autosize
                label=''
                type='textarea'
                placeholder={`请输入${this.name}意见`}
                maxlength={1000}
                show-word-limit
                class='structure_up_down'
              />
            </div>
          </div>
          <div class={styles_p.bottom}>
            <div class={styles_p.submit} on-click={() => this.onOk()}>{this.$l.getLocale('buttons.submit')}</div>
          </div>
          <senior-select-user
            visible={this.userTransferInputVisible}
            isMultiple={this.code !== 'counter-sign-simp'}
            isOnlyUser={true}
            userValue={lodash.map(this.userTransferInputItems, (m: any) => {
              return {
                value: m.userId,
                label: m.userName,
                organizationIdPath: m.organizationIdPath,
                organizationNamePath: m.organizationNamePath
              };
            })}
            on-cancel={() => this.userTransferInputVisible = false}
            on-ok={(item: any) => this.onCounterSignUserChange(lodash.map(item.userValue, (u: any) => {
              return {
                ...u,
                userId: u.value,
                userName: u.label
              };
            }))}
          />
        </van-action-sheet>
      </div>
    );
  }
}
