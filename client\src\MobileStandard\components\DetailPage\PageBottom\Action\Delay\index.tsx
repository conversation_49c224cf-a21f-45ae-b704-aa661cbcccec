import { Dialog } from 'vant';
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import styles_p from '../../page-bottom.module.less';
import { pageBottomService } from '../../service';

@Component
export class Delay extends Vue {
  @Prop() icon!: string;
  @Prop() code!: string;
  @Prop() name!: string;
  @Prop() defaultComment!: string;
  @Prop() instanceNumber!: string;
  @Prop() taskId!: string;

  private comment = '';
  @Emit('submit')
  Submit(actionCode: string, data: any) { }

  private onDelay() {
    pageBottomService.getDelayState(this.taskId).subscribe(s => {
      if (!s) {
        this.$toast({
          type: 'fail',
          forbidClick: true,
          message: this.$l.getLocale('tips.delay-limit')
        });
      } else {
        Dialog.confirm({
          title: '信息不足、延缓审批',
          message: '该审核环节时效增加1个工作日，请尽快处理！'
        })
          .then(() => {
            this.Submit(this.code, { resolveComment: this.comment });
          });
      }
    });
  }
  created() {
  }

  render() {
    return <div class={styles_p.action}>
      <div class={styles_p.button + ' ' + styles_p[this.code]}
        on-click={() => this.onDelay()}>
        <i class={`iconfont icon-${this.icon}`} />
        <span>{this.name}</span>
      </div>
    </div>;
  }
}
