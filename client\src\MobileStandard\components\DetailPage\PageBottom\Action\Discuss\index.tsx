import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import { ActionDataDto } from '../../types';
import styles_p from '../../page-bottom.module.less';

@Component
export class Discuss extends Vue {
  @Prop() icon!: string;
  @Prop() code!: string;
  @Prop() type!: string;
  @Prop() name!: string;
  @Prop() taskId!: string;
  @Prop() defaultComment!: string;
  @Prop() hasBranch!: boolean;

  private data: ActionDataDto = { targetUsers: [], targetUserId: '', resolveComment: '', isCallSystem: true };

  private allCount = 1000;
  private allowCount = this.allCount;
  private visible = false;

  @Emit('submit')
  Submit(actionCode: string, data: any) { }

  onOk() {
    if (!this.data.targetUserId) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: `请选择${this.name}人员`
      });
    } else if (!this.data.resolveComment) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: `请输入${this.name}意见`
      });
    } else {
      this.Submit('discuss', this.data);
      this.visible = false;
      /* actionService.discuss(this.taskId, this.data).subscribe(
        () => {
          this.$notify.success(`${this.$l.getLocale('instance.notice.discuss-success')}`);
        },
        () => {
        }
      ); */
    }
  }

  created() {
    this.data.resolveComment = this.defaultComment;
  }

  render() {
    return (
      <div class={styles_p.action}>
        <div class={styles_p.button + ' ' + styles_p[this.code]}>
          <i class={`iconfont icon-${this.icon}`} />
          <span>{this.name}</span>
        </div>
        <van-action-sheet v-model={this.visible}
          round={false}
          class={styles_p.sheet}
          get-container='#approval'
        >
          <div class={styles_p.top}>
            <van-icon name='arrow-down' size='20'
              class={styles_p.closeIcon}
              on-click={() => this.visible = false}
            ></van-icon>
            <span>{this.name}</span>
          </div>
          <div class={styles_p.content}>
            <div class={styles_p.block}>
              <span class={styles_p.title}>{this.$l.getLocale('fields.approvalComment')}</span>
              <van-field
                v-model={this.data.resolveComment}
                rows='5'
                autosize
                label=''
                type='textarea'
                placeholder={this.$l.getLocale(['fields.please', 'fields.input', 'fields.approvalComment'])}
                maxlength={1000}
                show-word-limit
                class='structure_up_down'
              />
            </div>
          </div>
          <div class={styles_p.bottom}>
            <div class={styles_p.submit} on-click={() => this.onOk()}>{this.$l.getLocale('buttons.submit')}</div>
          </div>
        </van-action-sheet>
      </div>
    );
  }
}
