import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import { ActionDataDto } from '../../types';
import styles_p from '../../page-bottom.module.less';
import lodash from 'lodash';
import { SeniorSelectUser } from '@/MobileStandard/components';
import { pageBottomService } from '../../service';

@Component({
  components: {
    SeniorSelectUser
  }
})
export class Notice extends Vue {
  @Prop() icon!: string;
  @Prop() code!: string;
  @Prop() type!: string;
  @Prop() name!: string;
  @Prop() defaultComment!: string;
  @Prop() taskId!: string;
  @Prop() instanceNumber!: string;

  private data: ActionDataDto = {
    targetUsers: [],
    taskId: this.taskId,
    instanceNumber: this.instanceNumber,
    isCallSystem: true
  };
  private visible = false;
  private userTransferInputVisible = false;
  private userTransferInputItems: any = [];
  private userAvatars: any = {};
  private historyData: any = [];
  private pagination = {
    total: 0,
    current: 0,
    pageSize: 5,
  };
  private loading = false;
  private finished = false;
  @Emit('submit')
  Submit(actionCode: string, data: any) { }

  onOk() {
    if (this.data.targetUsers.length === 0) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: `请选择${this.name}人员`
      });
    } else {
      this.Submit('notice', this.data);
      this.visible = false;
    }
  }

  private onNotice() {
    this.data.resolveComment = '';
    if (this.defaultComment) {
      this.data.resolveComment = this.defaultComment;
    }
    this.historyData = [];
    this.loading = false;
    this.finished = false;
    this.pagination.current = 0;
    this.pagination.total = 0;
    this.onLoad();
    this.visible = true;
  }
  private onLoad() {
    this.pagination.current += 1;
    const params = {
      'page-index': this.pagination.current,
      'page-size': this.pagination.pageSize,
      'is-all': false
    };
    pageBottomService.getCCHistory(this.instanceNumber, params).subscribe((s: any) => {
      this.historyData = [...this.historyData, ...s.items];
      this.pagination.total = s.total;
      this.loading = false;
      if (s.items.length === 0 || s.items.length < this.pagination.pageSize) {
        this.finished = true;
      }
    });
  }
  private onUserTransferInput(type: any, typeIndex: number) {
    this.userTransferInputVisible = true;
    this.userTransferInputItems = lodash.cloneDeep(lodash.map(this.data.targetUsers,
      (t: any) => {
        return {
          userId: t.UserId,
          userName: t.UserName,
          organizationIdPath: t.OrganizationIdPath,
          organizationNamePath: t.OrganizationNamePath
        };
      }));
  }
  private onCCUserChange(e: any) {
    this.data.targetUsers = [];
    if (e && e.length > 0) {
      this.data.targetUsers = lodash.map(e, (p: any) => {
        if (!this.userAvatars[p.userId]) {
          this.userAvatars[p.userId] = p.avatar;
        }
        return {
          UserId: p.userId,
          UserName: p.userName,
          OrganizationIdPath: p.organizationIdPath,
          OrganizationNamePath: p.organizationNamePath
        };
      });
    }
    this.userTransferInputVisible = false;
  }
  private onDel(index: number) {
    this.data.targetUsers.splice(index, 1);
  }
  created() {
  }

  render() {
    return (
      <div class={styles_p.action}>
        <div class={styles_p.button + ' ' + styles_p[this.code]}
          on-click={() => this.onNotice()}>
          <i class={`iconfont icon-${this.icon}`} />
          <span>{this.name}</span>
        </div>
        <van-action-sheet v-model={this.visible}
          round={false}
          class={styles_p.sheet}
          get-container='#approval'
        >
          <div class={styles_p.top}>
            <van-icon name='arrow-down' size='20'
              class={styles_p.closeIcon}
              on-click={() => this.visible = false}
            ></van-icon>
            <span>{this.name}</span>
          </div>
          <div class={styles_p.content}>
            <div class={styles_p.block}>
              <span class={styles_p.title}>{`${this.name}人员`}</span>
              <div class={styles_p.img}
                on-click={() => this.onUserTransferInput('ccUser', -1)}>
                <i class='iconfont icon-input_adduser'
                  style='color:#009a3e;font-size:15px;'></i>
              </div>
              <div class={styles_p.users}>
                {
                  lodash.map(this.data.targetUsers || [], (u: any, ui: number) => {
                    return <div class={styles_p.user}>
                      <div class={styles_p.avator}>
                        <img src={this.userAvatars[u.UserId] || require('../../../../../../assets/images/Avatar_default.png')} />
                        <div class={styles_p.del}
                          on-click={() => this.onDel(ui)}>
                          <i class='iconfont icon-pop_btn_close'
                            style='color:#be4848;font-size:12px;'></i>
                        </div>
                      </div>
                      <span>{u.UserName}</span>
                    </div>;
                  })
                }
              </div>
            </div>
            <div class={styles_p.block}>
              <span class={styles_p.title}>抄送历史记录</span>
              <div class={styles_p.cc_main_content}>
                {
                  this.historyData.length > 0 ? <van-list v-model={this.loading}
                    finished={this.finished}
                    on-load={this.onLoad}
                    finished-text={this.$l.getLocale('fields.noMore')}
                    offset={0}
                    immediate-check={false}
                    class='van-clearfix'>
                    {
                      this.historyData.map((d: any) => {
                        return <div class={styles_p.cc_example}>
                          <div class={styles_p.cc_content}>
                            <div class={styles_p.cc_row}>
                              <div style='width:50%'>{this.$l.getLocale('fields.ccPerson')}:
                                {(d.operatorType === 0 || (d.operatorType === null && !d.operatorName)) ? this.$l.getLocale('fields.systemCC')
                                  : d.operatorName}</div>
                              <div style='width:50%'>{this.$l.getLocale('fields.toReadPerson')}: {d.userName}</div>
                            </div>
                            <div class={styles_p.cc_row}>
                              <div>{this.$l.getLocale('fields.ccTime')}: {d.arriveTime}</div>
                            </div>
                          </div>
                        </div>;
                      })
                    }
                  </van-list> : <van-empty description={this.$l.getLocale('fields.emptyData')} />
                }
              </div>
            </div>
          </div>
          <div class={styles_p.bottom}>
            <div class={styles_p.submit} on-click={() => this.onOk()}>{this.$l.getLocale('buttons.submit')}</div>
          </div>
          <senior-select-user
            visible={this.userTransferInputVisible}
            isOnlyUser={true}
            userValue={lodash.map(this.userTransferInputItems, (m: any) => {
              return {
                value: m.userId,
                label: m.userName,
                organizationIdPath: m.organizationIdPath,
                organizationNamePath: m.organizationNamePath
              };
            })}
            on-cancel={() => this.userTransferInputVisible = false}
            on-ok={(item: any) => this.onCCUserChange(lodash.map(item.userValue, (u: any) => {
              return {
                ...u,
                userId: u.value,
                userName: u.label
              };
            }))}
          />
        </van-action-sheet>
      </div>
    );
  }
}
