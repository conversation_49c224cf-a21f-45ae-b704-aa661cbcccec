import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import styles_p from '../../page-bottom.module.less';
import { CommonApproveComment } from '@/MobileStandard/components/CommonApproveComment';
@Component({
  components: {
    CommonApproveComment
  }
})
export class Receive extends Vue {
  @Prop() icon!: string;
  @Prop() code!: string;
  @Prop() name!: string;
  @Prop() defaultComment!: string;

  private visible = false;
  private comment = '';
  private modalTitle = '';
  private modalCommentPlaceholder = '';
  @Emit('submit')
  Submit(actionCode: string, data: any) { }

  onOk() {
    if (!this.comment) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: this.modalCommentPlaceholder
      });
    } else {
      this.Submit(this.code, { comment: this.comment });
      this.visible = false;
    }
  }
  private onApproval() {
    this.visible = true;
  }
  created() {
    this.modalTitle = '签字意见';
    this.modalCommentPlaceholder = '请输入意见';
  }

  render() {
    return (
      <div class={styles_p.action}>
        <div class={styles_p.button + ' ' + styles_p[this.code]}
          on-click={() => this.onApproval()}>
          <i class={`iconfont icon-${this.icon}`} />
          <span>{this.name}</span>
        </div>
        <van-action-sheet v-model={this.visible}
        round={false}
        class={styles_p.sheet}
        get-container='#approval'
        >
          <div class={styles_p.top}>
            <van-icon name='arrow-down' size='20'
              class={styles_p.closeIcon}
              on-click={() => this.visible = false}
            ></van-icon>
            <span>{this.name}</span>
          </div>
          <div class={styles_p.content}>
            <div class={styles_p.block}>
              <span class={styles_p.title}>{this.modalTitle}</span>
              <van-field
                v-model={this.comment}
                rows='5'
                autosize
                label=''
                type='textarea'
                placeholder={this.modalCommentPlaceholder}
                maxlength={1000}
                show-word-limit
                class='structure_up_down'
              />
            </div>
            <div class={styles_p.block}>
              <common-approve-comment on-change={(value: any) => this.comment = value} />
            </div>
          </div>
          <div class={styles_p.bottom}>
            <div class={styles_p.submit} on-click={() => this.onOk()}>{this.$l.getLocale('buttons.submit')}</div>
          </div>
        </van-action-sheet>
      </div>
    );
  }
}
