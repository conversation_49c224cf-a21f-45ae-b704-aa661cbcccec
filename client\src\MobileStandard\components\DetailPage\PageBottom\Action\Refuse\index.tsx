import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import styles_p from '../../page-bottom.module.less';

@Component
export class Refuse extends Vue {
  @Prop() icon!: string;
  @Prop() code!: string;
  @Prop() type!: string;
  @Prop() name!: string;
  @Prop() defaultComment!: string;

  private comment = '';
  private visible = false;

  @Emit('submit')
  Submit(actionCode: string, data: any) { }

  onOk() {
    if (!this.comment) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: `请输入${this.name}原因`
      });
    } else {
      this.Submit('refuse', { resolveComment: this.comment });
      this.visible = false;
    }
  }
  private onRefuse() {
    if (this.defaultComment) {
      this.comment = this.defaultComment;
    } else {
      this.comment = this.name;
    }
    this.visible = true;
  }
  created() {
  }

  render() {
    return (
      <div class={styles_p.action}>
        <div class={styles_p.button + ' ' + styles_p[this.code]}
          on-click={() => this.onRefuse()}>
          <i class={`iconfont icon-${this.icon}`} />
          <span>{this.name}</span>
        </div>
        <van-action-sheet v-model={this.visible}
          round={false}
          class={styles_p.sheet}
          get-container='#approval'
        >
          <div class={styles_p.top}>
            <van-icon name='arrow-down' size='20'
              class={styles_p.closeIcon}
              on-click={() => this.visible = false}
            ></van-icon>
            <span>{this.name}</span>
          </div>
          <div class={styles_p.content}>
            <div class={styles_p.block}>
              <span class={styles_p.title}>{`${this.name}原因`}</span>
              <van-field
                v-model={this.comment}
                rows='5'
                autosize
                label=''
                type='textarea'
                placeholder={`请输入${this.name}原因`}
                maxlength={1000}
                show-word-limit
                class='structure_up_down'
              />
            </div>
          </div>
          <div class={styles_p.bottom}>
            <div class={styles_p.submit} on-click={() => this.onOk()}>{this.$l.getLocale('buttons.submit')}</div>
          </div>
        </van-action-sheet>
      </div>
    );
  }
}
