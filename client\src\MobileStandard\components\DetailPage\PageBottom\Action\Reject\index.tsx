import { Component, Vue, Prop, Emit } from 'vue-property-decorator';

import styles from './reject.module.less';
import { RejectTypeEnum, ActionDataDto } from '../../types';
import { rejectService } from './service';
import styles_p from '../../page-bottom.module.less';
import { Dialog } from 'vant';

@Component
export class Reject extends Vue {
  @Prop() icon!: string;
  @Prop() code!: string;
  @Prop() type!: string;
  @Prop() name!: string;
  @Prop() taskId!: string;
  @Prop() instanceNumber!: string;
  @Prop() defaultComment!: string;
  @Prop() hasBranch!: boolean;

  private rejectSteps!: any[];
  private rejectType = '1';
  private data: ActionDataDto = { targetUsers: [], targetActivityId: '', resolveComment: '', isCallSystem: true };
  private resolveType!: RejectTypeEnum;
  private isParallel = false;
  private parallelPath = '';
  private visible = false;
  private ownerName = '';

  @Emit('submit')
  Submit(actionCode: string, data: any) { }

  onOk() {
    if (!this.data.targetActivityId) {
      this.resolveType = this.rejectType === '1' ? RejectTypeEnum.rejectStartDirect : RejectTypeEnum.rejectStart;
    } else {
      this.resolveType = this.rejectType === '1' ? RejectTypeEnum.rejectActivityDirect : RejectTypeEnum.rejectActivity;
    }
    if (!this.data.resolveComment || !this.resolveType) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: '请选择退回类型，并填写退回说明'
      });
    } else {
      Dialog.confirm({
        title: this.$l.getLocale('fields.info'),
        message: this.$l.getLocale('tips.rejectConfirm')
      })
        .then(() => {
          this.Submit('reject', { ...this.data, rejectType: this.resolveType });
          this.visible = false;
        });
      /* actionService.reject(this.taskId, this.data, this.resolveType).subscribe(
        () => {
          this.$notify.success(`${this.$l.getLocale('instance.notice.reject-success')}`);
        },
        () => {
        }
      ); */
    }
  }

  created() {
    // console.log(this.baseInfo);
  }
  mounted() {
    // 详情页或退回到发起的
    const number = this.$route.params['number'] as string;
    const tid = this.$route.query['task-id'] as string;
    const token = this.$route.query['movitech_token'] as string;
    rejectService.getInstanceInfo(number, tid, token).subscribe(data => {
      console.log(data);
      this.ownerName = data.ownerName;
    });
  }
  private open() {
    this.rejectType = '1';
    this.data.resolveComment = '';
    if (this.defaultComment) {
      this.data.resolveComment = this.defaultComment;
    }
    const startText = this.$l.getLocale('fields.startPerson');
    rejectService.getParallelInfo(this.taskId).subscribe(data => {
      if (data) {
        this.isParallel = data.isParallel || false;
        this.parallelPath = data.parallelPath || '';
      }
    });
    rejectService.getRejectSteps(this.instanceNumber, this.taskId).subscribe(data => {
      this.rejectSteps = [{ label: startText, value: '', nodeType: 'start' }, ...data];
      this.$forceUpdate();
    });
    this.visible = true;
  }
  render() {
    return (
      <div class={styles_p.action}>
        <div class={styles_p.button + ' ' + styles_p[this.code]}
          on-click={() => this.open()}>
          <i class={`iconfont icon-${this.icon}`} />
          <span>{this.name}</span>
        </div>
        <van-action-sheet v-model={this.visible}
          round={false}
          class={styles_p.sheet}
          get-container='#approval'
        >
          <div class={styles_p.top}>
            <van-icon name='arrow-down' size='20'
              class={styles_p.closeIcon}
              on-click={() => this.visible = false}
            ></van-icon>
            <span>{this.name}</span>
          </div>
          <div class={styles_p.content}>
            <div class={styles_p.block}>
              <span class={styles_p.title}> {this.$l.getLocale('fields.rejectStep')}</span>
              <van-radio-group v-model={this.data.targetActivityId}>
                {(this.rejectSteps || []).map(m => {
                  return (
                    <van-radio name={m.value} disabled={
                      (m.value !== '' && !m.userName)
                      || (this.isParallel === true && !(m.isParallel === true && this.parallelPath.includes(m.parallelPath))
                        && m.value !== '')
                      || (this.isParallel !== true && m.isParallel === true)}
                      style={m.isParallel === true ? 'padding-left: 40px;' : ''}>
                      {m.label} {m.label === '发起人' ? this.ownerName : ''}
                      {m.value && !m.userName ? <span class='text-danger'> (空审批人)</span> : null}
                    </van-radio>
                  );
                })}
              </van-radio-group>
            </div>
            <div class={styles_p.block}>
              <span class={styles_p.title}>{this.$l.getLocale('fields.rejectType')}</span>
              <van-radio-group v-model={this.rejectType}>
                <van-radio name='1'>{this.$l.getLocale('fields.rejectDirect')}</van-radio>
                <van-radio name='2'>{this.$l.getLocale('fields.rejectNew')}</van-radio>
              </van-radio-group>
            </div>
            <div class={styles_p.block}>
              <span class={styles_p.title}>{this.$l.getLocale('fields.rejectComment')}</span>
              <van-field
                v-model={this.data.resolveComment}
                rows='5'
                autosize
                label=''
                type='textarea'
                placeholder={this.$l.getLocale(['fields.please', 'fields.input', 'fields.rejectComment'])}
                maxlength={1000}
                show-word-limit
                class='structure_up_down'
              />
            </div>
            <div class={styles_p.block}>
              <div>{this.$l.getLocale('tips.rejectInfo')}</div>
              <div> 1）{this.$l.getLocale('tips.rejectNew')}</div>
              <div> 2）{this.$l.getLocale('tips.rejectDirect')}</div>
            </div>
          </div>
          <div class={styles_p.bottom}>
            <div class={styles_p.submit} on-click={() => this.onOk()}>{this.$l.getLocale('buttons.submit')}</div>
          </div>
        </van-action-sheet>
      </div>
    );
  }
}
