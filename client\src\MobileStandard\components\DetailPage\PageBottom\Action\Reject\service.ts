import { httpHelper } from '@/MobileStandard/common/utils';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { ParallelInfoDto, InstanceBaseInfoDto } from '../../types';
// import { InstanceBaseInfoDto } from '../../type';

class RejectService {
  /**
   * 获取退回步骤
   */
  getRejectSteps(instanceNumber: string, taskId: string): Observable<any[]> {
    const url = `/api/process/v1/instances/${instanceNumber}/reject-steps/${taskId}/task`;
    return httpHelper.get(url).pipe(
      map(data =>
        data.map((d: any) => {
          return {
            label: d.name + ' ' + d.userName, value: d.activityId, userName: d.userName,
            isParallel: d.isParallel, parallelPath: d.parallelPath
          };
        })
      )
    );
  }

  getParallelStatus(taskId: string): Observable<any> {
    const url = `/api/process/v1/tasks/${taskId}/parallel-status`;
    return httpHelper.get(url);
  }

  getParallelInfo(taskId: string): Observable<ParallelInfoDto> {
    const url = `/api/process/v1/tasks/${taskId}/parallel-info`;
    return httpHelper.get(url);
  }

  getInstanceInfo(number: string, taskId: string, token: string): Observable<InstanceBaseInfoDto> {
    const _url = `/api/process/v1/instances/${number}/info`;
    return httpHelper.get(_url, { params: { 'task-id': taskId, 'token': token } });
  }
}

export const rejectService = new RejectService();
