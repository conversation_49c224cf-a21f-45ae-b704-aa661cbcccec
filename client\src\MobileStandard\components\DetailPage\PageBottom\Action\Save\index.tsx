import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import styles_p from '../../page-bottom.module.less';

@Component
export class Save extends Vue {
    @Prop() code!: string;
    @Prop() name!: string;

    @Emit('submit')
    Submit(actionCode: string, data: any) {}

    created() {
    }

    render() {
        return <div class={styles_p.action}>
            <div class={styles_p.button + ' ' + styles_p[this.code]}
                on-click={() => this.Submit(this.code, null)}>
                <span>{this.name}</span>
            </div>
        </div>;
    }
}
