import { Dialog } from 'vant';
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import styles_p from '../../page-bottom.module.less';

@Component
export class Urge extends Vue {
    @Prop() icon!: string;
    @Prop() code!: string;
    @Prop() name!: string;

    @Emit('submit')
    Submit(actionCode: string, data: any) { }

    onUrge() {
        this.$dialog.confirm({
            message: this.$l.getLocale('tips.areYouSureUrge'),
        }).then(() => {
            this.Submit('urge', null);
        }, () => {});
    }

    created() {
    }

    render() {
        return (
            <div class={styles_p.action}>
                <div class={styles_p.button + ' ' + styles_p[this.code]}
                    on-click={() => this.onUrge()}>
                    <i class={`iconfont icon-${this.icon}`} />
                    <span>{this.name}</span>
                </div>
            </div>
        );
    }
}
