import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import styles_p from '../../page-bottom.module.less';

@Component
export class Withdraw extends Vue {
    @Prop() icon!: string;
    @Prop() code!: string;
    @Prop() name!: string;

    @Emit('submit')
    Submit(actionCode: string, data: any) { }

    onWithdraw() {
        this.$dialog.confirm({
            message: this.$l.getLocale('tips.areYouSureWithdraw'),
        }).then(() => {
            this.Submit('withdraw', null);
        }, () => {});
    }

    created() {
    }

    render() {
        return (
            <div class={styles_p.action}>
                <div class={styles_p.button + ' ' + styles_p[this.code]}
                    on-click={() => this.onWithdraw()}>
                    <i class={`iconfont icon-${this.icon}`} />
                    <span>{this.name}</span>
                </div>
            </div>
        );
    }
}
