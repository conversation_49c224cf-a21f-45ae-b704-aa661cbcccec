import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import { Approve } from './Approve';
import { CounterSign } from './CounterSign';
import { Discuss } from './Discuss';
import { Handover } from './Handover';
import { Notice } from './Notice';
import { Refuse } from './Refuse';
import { Reject } from './Reject';
import { Cancel } from './Cancel';
import { Delay } from './Delay';
import { Urge } from './Urge';
import { Withdraw } from './Withdraw';
import { Start } from './Start';
import { Save } from './Save';
import { Delete } from './Delete';
import { Receive } from './Receive';

@Component({
  components: {
    Approve, CounterSign, Discuss, Handover,
    Notice, Refuse, Reject, Cancel, Delay, Urge, Withdraw,
    Start, Save, Delete, Receive
  }
})
export class InstanceAction extends Vue {
  @Prop() action!: 'handover' | 'counter-sign' | 'notice' | 'reject' |
    'discuss' | 'approve' | 'cancel' | 'refuse' | 're-start' | 'delay' |
    'urge' | 'withdraw' | 'start' | 'save' | 'delete' | 'receive';
  @Prop() code!: string;
  @Prop() name!: string;
  @Prop() defaultComment!: string;
  @Prop() taskId!: string;
  @Prop() instanceNumber!: string;
  @Prop() approvalRecord!: any;
  private icon = '';
  @Emit('action-submit')
  onActionSubmit(actionCode: string, actionData: any) { }
  created() {
    switch (this.code) {
      case 'approve_agreeopinion':
        this.icon = 'shenpi_icon_tongyixiugaiyijian';
        break;
      case 'approve_opinion':
        this.icon = 'shenpi_icon_woyouxiugaiyijian';
        break;
      case 'handover':
      case 'handover-simp':
        this.icon = 'shenpi_icon_weituo';
        break;
      case 'counter-sign':
      case 'counter-sign-simp':
        this.icon = 'icon_shenpibuzhou_yijianzhengxun';
        break;
      case 'approve_ignore':
        this.icon = 'shenpi_icon_hulve';
        break;
      case 'reject':
        this.icon = 'shenpi_icon_tuihuixiugai';
        break;
      case 'refuse':
        this.icon = 'shenpi_icon_butongyi';
        break;
      case 'notice':
        this.icon = 'icon_shenpixiangqing_chaosong';
        break;
      case 'cancel':
      case 'start':
      case 'save':
      case 'delete':
        this.icon = '';
        break;
      case 'delay':
        this.icon = 'wait-fill';
        break;
      case 'urge':
        this.icon = 'leftfont-17';
        break;
      case 'withdraw':
        this.icon = 'chehui';
        break;
      case 'receive':
        this.icon = 'shenpi_icon_tongyiyuanwenyijian';
        break;
      default:
        this.icon = 'shenpi_icon_tongyiyuanwenyijian';
        break;
    }
  }
  render() {
    if (this.action === 'approve') {
      return (
        <approve
          icon={this.icon}
          code={this.code}
          name={this.name}
          defaultComment={this.defaultComment}
          instanceNumber={this.instanceNumber}
          approvalRecord={this.approvalRecord}
          on-submit={this.onActionSubmit}>
        </approve>
      );
    } else if (this.action.startsWith('counter-sign')) {
      return (
        <counter-sign
          icon={this.icon}
          code={this.code}
          name={this.name}
          defaultComment={this.defaultComment}
          taskId={this.taskId}
          on-submit={this.onActionSubmit}>
        </counter-sign>
      );
    } else if (this.action === 'discuss') {
      return (
        <discuss
          icon={this.icon}
          code={this.code}
          name={this.name}
          defaultComment={this.defaultComment}
          on-submit={this.onActionSubmit}>
        </discuss>
      );
    } else if (this.action.startsWith('handover')) {
      return (
        <handover
          icon={this.icon}
          code={this.code}
          name={this.name}
          defaultComment={this.defaultComment}
          taskId={this.taskId}
          on-submit={this.onActionSubmit}>
        </handover>
      );
    } else if (this.action === 'notice') {
      return (
        <notice
          icon={this.icon}
          code={this.code}
          name={this.name}
          defaultComment={this.defaultComment}
          taskId={this.taskId}
          instanceNumber={this.instanceNumber}
          on-submit={this.onActionSubmit}>
        </notice>
      );
    } else if (this.action === 'refuse') {
      return (
        <refuse
          icon={this.icon}
          code={this.code}
          name={this.name}
          defaultComment={this.defaultComment}
          on-submit={this.onActionSubmit}>
        </refuse>
      );
    } else if (this.action === 'reject') {
      return (
        <reject
          icon={this.icon}
          code={this.code}
          name={this.name}
          defaultComment={this.defaultComment}
          taskId={this.taskId}
          instanceNumber={this.instanceNumber}
          on-submit={this.onActionSubmit}>
        </reject>
      );
    } else if (this.action === 'cancel') {
      return (
        <cancel
          icon={this.icon}
          code={this.code}
          name={this.name}
          defaultComment={this.defaultComment}
          taskId={this.taskId}
          instanceNumber={this.instanceNumber}
          on-submit={this.onActionSubmit}>
        </cancel>
      );
    } else if (this.action === 'delay') {
      return (
        <delay
          icon={this.icon}
          code={this.code}
          name={this.name}
          defaultComment={this.defaultComment}
          instanceNumber={this.instanceNumber}
          taskId={this.taskId}
          on-submit={this.onActionSubmit}>
        </delay>
      );
    } else if (this.action === 'urge') {
      return (
        <urge
          icon={this.icon}
          code={this.code}
          name={this.name}
          on-submit={this.onActionSubmit}>
        </urge>
      );
    } else if (this.action === 'withdraw') {
      return (
        <withdraw
          icon={this.icon}
          code={this.code}
          name={this.name}
          on-submit={this.onActionSubmit}>
        </withdraw>
      );
    } else if (this.action === 'start') {
      return (
        <start
          code={this.code}
          name={this.name}
          on-submit={this.onActionSubmit}>
        </start>
      );
    } else if (this.action === 'save') {
      return (
        <save
          code={this.code}
          name={this.name}
          on-submit={this.onActionSubmit}>
        </save>
      );
    } else if (this.action === 'delete') {
      return (
        <delete
          code={this.code}
          name={this.name}
          on-submit={this.onActionSubmit}>
        </delete>
      );
    } else if (this.action === 'receive') {
      return (
        <receive
          code={this.code}
          name={this.name}
          icon={this.icon}
          on-submit={this.onActionSubmit}>
        </receive>
      );
    } else {
      return ('');
    }
  }
}
