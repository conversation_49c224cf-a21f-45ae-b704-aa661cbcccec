import { authService } from '@/MobileStandard/services/auth';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { InstanceAction } from './Action';
import { pageBottomService } from './service';
import { ActionDto } from './types';
import styles from './page-bottom.module.less';
import lodash from 'lodash';
import { guidHelper, subjectHelper } from '@/MobileStandard/common/utils';
import { ApproveState } from '../BaseInfo/type';
import { TaskCommonStatusEnum } from '../ApprovalRecord/types';

@Component({
    components: {
        InstanceAction
    }
})
export class IndexMixins extends Vue {
    @Prop({ default: false }) isButtonHide!: boolean;
    @Prop({ default: false }) isDeductioning!: boolean;
    @Prop() comment!: string;
    @Prop() urlParamteres!: UrlParametersDto;
    userId!: string | undefined;
    actions: ActionDto[] = [];
    operable = false;
    showPopover = false;
    approvalRecord: any = [];
    baseInfo: any = {};
    @Emit('action-submit') ActionSubmit(actionCode: string, actionName: string, actionData: any) { }
    @Emit('has-action') HasAction(isHas: boolean) { }
    getActionCode(code: string): string {
        if (code && code.startsWith('approve')) {
            return 'approve';
        } else {
            return code;
        }
    }
    onAction(actionCode: string, actionName: string, actionData: any) {
        this.ActionSubmit(actionCode, actionName, actionData);
    }
    getButtonHtml(action: any) {
        return <instance-action
            code={action.code}
            action={this.getActionCode(action.code)}
            name={action.name}
            default-comment={this.comment}
            taskId={this.urlParamteres.taskId}
            instanceNumber={this.urlParamteres.number}
            approvalRecord={this.approvalRecord}
            key={guidHelper.generate()}
            on-action-submit={(actionCode: string, actionData: any) => this.onAction(action.code, action.name, actionData)}
        />;
    }
    getMoreButtonHtml(actions: any) {
        return actions.length > 0 ? <van-popover
            v-model={this.showPopover}
            trigger='click'
            placement='top-start'
            offset={[10, 16]}
            close-on-click-action={false}
            close-on-click-outside={false}
            class={styles.more_actions}>
            <div class={styles.more_buttons}>
                {
                    lodash.map(actions, (a: any) => {
                        return <instance-action
                            code={a.code}
                            action={this.getActionCode(a.code)}
                            name={a.name}
                            default-comment={this.comment}
                            taskId={this.urlParamteres.taskId}
                            instanceNumber={this.urlParamteres.number}
                            approvalRecord={this.approvalRecord}
                            key={guidHelper.generate()}
                            on-action-submit={(actionCode: string, actionData: any) => this.onAction(a.code, a.name, actionData)}
                        />;
                    })
                }
            </div>
            <template slot='reference'>
                <div class={styles.button_css}>
                    <div></div>
                    <div></div>
                    <div></div>
                </div>
            </template>
        </van-popover> : '';
    }
    created() {
        this.userId = authService.user.id;
        // 获取审批记录提取可同意意见
        subjectHelper.getSubject('approvalRecord$').subscribe((data: any) => {
            this.approvalRecord = [];
            lodash.forEach(data || [], (s: any) => {
                if (s.stepId && (s.resolverType === TaskCommonStatusEnum.done ||
                    s.resolverType === TaskCommonStatusEnum.skippedWhenSameApprover ||
                    lodash.startsWith(s.actionCode, 'approve_') ||
                    s.actionCode === 'approve')) {
                    const label = `${s.stepName}意见：${s.resolverUserName}`;
                    const value = `${s.stepId}|&&|${s.stepName}|&&|${s.resolverUserName}`;
                    if (lodash.findIndex(this.approvalRecord, (f: any) => f.value === value) === -1) {
                        this.approvalRecord.push({
                            label: label,
                            value: value
                        });
                    }
                }
            });
        });
        if (this.urlParamteres.pageType === 'my-processes' ||
            this.urlParamteres.pageType === 'done') {
            // 订阅流程基本信息
            subjectHelper.getSubject('baseInfo$').subscribe((s: any) => {
                // 流程处于审批中可催办和撤回
                console.log('s.status', s.status);
                console.log('ApproveState.processing', ApproveState.processing);
                if (s.status === ApproveState.processing) {
                    if (this.urlParamteres.pageType === 'my-processes' &&
                        lodash.toUpper(s.ownerId) === lodash.toUpper(authService.user.id) &&
                        this.urlParamteres.number) {
                        // this.actions.push({ code: 'urge', name: this.$l.getLocale('buttons.urging') });
                        this.actions.push({ code: 'withdraw', name: this.$l.getLocale('buttons.withdraw') });
                        this.actions.push({ code: 'notice', name: this.$l.getLocale('buttons.notice') });
                    } else if (this.urlParamteres.pageType === 'done' &&
                        this.urlParamteres.taskId) {
                        pageBottomService.canDoneWithdraw(this.urlParamteres.taskId || '').subscribe((data: any) => {
                            if (data) {
                                this.actions.push({ code: 'withdraw', name: this.$l.getLocale('buttons.withdraw') });
                                this.actions.push({ code: 'notice', name: this.$l.getLocale('buttons.notice') });
                            } else {
                                this.actions.push({ code: 'notice', name: this.$l.getLocale('buttons.notice') });
                            }
                        });
                    } else {
                        this.actions.push({ code: 'notice', name: this.$l.getLocale('buttons.notice') });
                    }
                } else {
                    this.actions.push({ code: 'notice', name: this.$l.getLocale('buttons.notice') });
                }
            });
        } else if (this.urlParamteres.pageType === 'todo' && this.urlParamteres.taskId && this.userId) {
            pageBottomService.getTaskActions(this.urlParamteres.taskId, this.userId).subscribe(s => {
                this.actions = [...s];
                this.actions.push({ code: 'notice', name: this.$l.getLocale('buttons.notice') });
                console.log(this.actions);
                if (this.actions && this.actions.length > 0) {
                    this.operable = true;
                    this.HasAction(true);
                } else {
                    this.HasAction(false);
                }
            });
        } else if (this.urlParamteres.pageType === 'todo' && this.urlParamteres.instanceSendRecvRecordId && this.userId) {
            pageBottomService.instanceSendRecvRecord_GetById(this.urlParamteres.instanceSendRecvRecordId).subscribe(data => {
                if (data && data.status === 0 && data.userId === this.userId) {
                    this.actions = [{code: 'receive', name: this.$l.getLocale('buttons.receive')}];
                    this.operable = true;
                    this.HasAction(true);
                }
            });
        } else if (this.urlParamteres.pageType === 'start') {
            this.HasAction(true);
            subjectHelper.getSubject('baseInfo$').subscribe((s: any) => {
                this.baseInfo = s;
                if (this.urlParamteres.number) {
                    this.actions.push({ code: 'start', name: this.$l.getLocale('buttons.reStart') });
                } else {
                    this.actions.push({ code: 'start', name: this.$l.getLocale('buttons.start') });
                }
                if ((this.baseInfo.status === 'rejected'
                    || (this.baseInfo.status === 'ready' &&
                        this.baseInfo.istatus === TaskCommonStatusEnum.interveneJumpStartNode)) &&
                    this.urlParamteres.number) {
                    this.actions.push({ code: 'cancel', name: this.$l.getLocale('buttons.void') });
                } else if (!this.baseInfo.status && !this.urlParamteres.number) {
                    if (!this.urlParamteres.bsid && !this.urlParamteres.btid && !this.urlParamteres.boid) {
                        this.actions.push({ code: 'save', name: this.$l.getLocale('buttons.saveDraft') });
                    }
                }
                if (this.urlParamteres.draftId) {
                    this.actions.push({ code: 'delete', name: this.$l.getLocale('buttons.delete') });
                }
            });
        } else {
            this.actions.push({ code: 'notice', name: this.$l.getLocale('buttons.notice') });
        }
    }
}
