import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import { Component } from 'vue-property-decorator';
import styles from './page-bottom.module.less';
import lodash from 'lodash';

@Component
export class PageBottom extends mixins(IndexMixins) {
  render() {
    return <div class={styles.buttons}>
      {
        this.isDeductioning ? <span class={styles.deductioning}>流程推演中，请稍等</span> :
          !this.isButtonHide && (this.actions || []).length > 0 ?
            <div class={(this.urlParamteres.pageType === 'start' ?
            styles.start_actions : styles.approval_actions) +
              ((this.actions || []).length > 2 ? ' ' + styles.more : '') +
              ((this.actions || []).length === 1 ? ' ' + styles.one : '')}>
              {
                (this.actions || []).length > 1 ?
                  [
                    this.getMoreButtonHtml(lodash.drop(this.actions, 2)),
                    this.getButtonHtml(this.actions[1]),
                    this.getButtonHtml(this.actions[0])
                  ] : this.getButtonHtml(this.actions[0])
              }
            </div>
            : ''
      }
    </div>;
  }
}
