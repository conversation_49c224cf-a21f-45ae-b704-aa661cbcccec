.buttons {
  z-index: 3;
  position: fixed;
  background: #ffffff;
  // height: 60px;
  box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.09);
  padding: 15px 12px 25px;
  box-sizing: border-box;
  bottom: 0px;
  left: 0px;
  width: 100%;
  text-align: center;
  line-height: 60px;
  .more_actions {
    width: 10%;
    height: 44px;
    line-height: 44px;
    padding: 0 10px;

    .button_css {
      div {
        display: inline-block;
        height: 8px;
        width: 8px;
        background: #009a3e;
        border-radius: 50%;
      }

      div:not(:last-child) {
        margin-right: 5px;
      }
    }
  }

  .deductioning {
    font-size: 14px;
    color: #0e9266;
  }

  .start_actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;

    .action {
      height: 44px;
      width: 48%;
      font-size: 15px;
      font-weight: bold;
      cursor: pointer;
      text-align: center;
      line-height: 44px;

      .button {
        border-radius: 4px;
        padding: 0 10px;
        height: 100%;
        overflow: hidden;
      }

      .save {
        background: rgba(14, 146, 102, 0.20);
        color: #00b576;
      }
  
      .cancel {
        background: rgba(14, 146, 102, 0.20);
        color: #00b576;
      }
  
      .start {
        background: #0e9266;
        color: #ffffff;
      }

      .delete {
        background: #f8ecec;
        color: #be4848;
      }
    }
  }

  .approval_actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;

    .action {
      height: 44px;
      line-height: 44px;
      width: 48%;

      .button {
        border-radius: 8px;
        box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.09);
        padding: 0 10px;
        height: 100%;
        overflow: hidden;

        i {
          margin-right: 5px;
        }
      }

      .approve_agreeorigin {
        background: #e6f4ef;
        color: #0e9266;
        border: 1px solid #0e9266;
      }

      .approve_agreeopinion {
        background: #fdf5ea;
        color: #f49f34;
        border: 1px solid #f49f34;
      }

      .approve_opinion {
        background: #f8ecec;
        color: #be4848;
        border: 1px solid #be4848;
      }

      .approve_ignore {
        background: #e6f4ef;
        color: #0e9266;
        border: 1px solid #0e9266;
      }

      .handover {
        background: #e6f4ef;
        color: #0e9266;
        border: 1px solid #0e9266;
      }

      .handover-simp {
        background: #e6f4ef;
        color: #0e9266;
        border: 1px solid #0e9266;
      }

      .counter-sign {
        background: #e6f4ef;
        color: #0e9266;
        border: 1px solid #0e9266;
      }

      .counter-sign-simp {
        background: #e6f4ef;
        color: #0e9266;
        border: 1px solid #0e9266;
      }

      .reject {
        background: #fdf5ea;
        color: #f49f34;
        border: 1px solid #f49f34;
      }

      .refuse {
        background: #f8ecec;
        color: #be4848;
        border: 1px solid #be4848;
      }

      .delay {
        background: #f8ecec;
        color: #be4848;
        border: 1px solid #be4848;
      }

      .approve {
        background: #e6f4ef;
        color: #0e9266;
        border: 1px solid #0e9266;
      }

      .notice {
        background: #f8ecec;
        color: #be4848;
        border: 1px solid #be4848;
      }
  
      .withdraw {
        background: #f8ecec;
        color: #be4848;
        border: 1px solid #be4848;
      }
  
      .urge {
        background: #fdf5ea;
        color: #f49f34;
        border: 1px solid #f49f34;
      }

      .receive {
        background: #e6f4ef;
        color: #0e9266;
        border: 1px solid #0e9266;
      }
    }
  }

  .more {
    .action {
      width: 38%;
    }
  }

  .one {
    .action {
      width: 100%;
    }
  }
}

.more_buttons {
  padding: 5px;
  min-width: 80px;

  .action {
    margin: 10px;

    .button {
      height: 100%;
      overflow: hidden;

      i {
        margin-right: 5px;
      }
    }

    .approve_agreeorigin {
      color: #0e9266;
    }

    .approve_agreeopinion {
      color: #f49f34;
    }

    .approve_opinion {
      color: #be4848;
    }

    .approve_ignore {
      color: #0e9266;
    }

    .handover {
      color: #0e9266;
    }

    .counter-sign {
      color: #0e9266;
    }

    .reject {
      color: #f49f34;
    }

    .refuse {
      color: #be4848;
    }

    .delay {
      color: #be4848;
    }

    .approve {
      color: #0e9266;
    }

    .notice {
      color: #be4848;
    }

    .withdraw {
      color: #be4848;
    }

    .urge {
      color: #f49f34;
    }

    .delete {
      color: #be4848;
    }

  }
}

.sheet {
  height: 100%;
  max-height: 100% !important;
  background-color: #F0F2F5 !important;
}

.top {
  width: 100%;
  background-color: #ffffff;
  text-align: center;
  vertical-align: middle;
  line-height: 45px;
  height: 45px;
  color: #333333;

  .closeIcon {
    position: absolute !important;
    left: 20px;
    top: 10px;
  }
}

.content {
  position: absolute;
  top: 45px;
  bottom: 60px;
  width: 100%;
  overflow: auto;
}

.block {
  margin-top: 10px;
  background-color: #ffffff;
  position: relative;

  .title {
    font-weight: bold;
    display: block;
    padding-bottom: 10px;
  }

  padding: 10px 16px;

  :global(.van-checkbox) {
    margin-bottom: 5px;
  }

  :global(.van-cell) {
    padding: 0px;
  }

  :global(.van-radio) {
    margin-bottom: 5px;
  }

  .img {
    width: 25px;
    height: 25px;
    background: #ffffff;
    border-radius: 50%;
    box-shadow: 2px 2px 13px 0px rgba(0, 0, 0, 0.11);
    line-height: 25px;
    text-align: center;
    position: absolute;
    top: 8px;
    right: 12px;
  }

  .users {
    min-height: 65px;
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;

    .user {
      min-width: 50px;
      width: 20%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 20px;

      .avator {
        width: 30px;
        height: 30px;
        position: relative;

        img {
          width: 30px;
          height: 30px;
          border-radius: 50%;
        }
      }


      span {
        display: block;
        font-size: 12px;
        color: #666666;
        overflow: hidden;
      }

      .del {
        position: absolute;
        border: 1px solid #ffffff;
        border-radius: 50%;
        top: -8px;
        right: -8px;
        background: #ffffff;
        height: 15px;
        width: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }

  .cc_main_content {
    padding: 5px 0;
    overflow: auto;
    background-color: #f0f2f5;
    height: 200px;
    .cc_example {
      margin: 0 10px 10px 10px;
      border-radius: 4px;
      background-color: #FFFFFF;
      padding: 5px;
      position: relative;
      display: flex;
      align-items: center;
      .cc_content {
        margin-left: 10px;
        flex: 1;
        background-color: #FFFFFF;
      }

      .cc_row {
        height: 33px;
        line-height: 33px;
        font-size: 15px;
        font-weight: 400;
        color: #999999;
        display: flex;
      }
    }
  }
}

.bottom {
  // height: 60px;
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: 100%;
  background-color: #ffffff;
  text-align: center;
  line-height: 60px;
  padding: 10px 0px 15px;

  .submit {
    display: inline-block;
    background-color: #009a3e;
    color: #ffffff;
    height: 44px;
    width: 120px;
    line-height: 44px;
    text-align: center;
    border-radius: 5px;
  }
}