import { httpHelper } from '@/MobileStandard/common/utils';
import { Observable } from 'rxjs';
import { ActionDto } from './types';
class PageBottomService {
    getTaskActions(taskId: string, userId: string): Observable<ActionDto[]> {
        const _url = `/api/engine/v1/tasks/${taskId}/actions`;
        return httpHelper.get(_url, { params: { 'user-id': userId } });
    }
    getHistoryInfo(instanceNumber: string) {
        return httpHelper.get('/api/engine/v2/instances/history-info',
            { params: { instanceNumber: instanceNumber } });
    }
    getCCHistory(number: string, formdata: any) {
        const _url = `/api/engine/v2/instances/${number}/ccrecord`;
        return httpHelper.get(_url, { params: formdata }, { loading: false });
    }
    getConsultState(taskId: string): Observable<any> {
        const url = `/api/engine/v1/tasks/${taskId}/consult-state`;
        return httpHelper.get(url);
    }
    getHandoverState(taskId: string): Observable<any> {
        const url = `/api/engine/v1/tasks/${taskId}/handover-state`;
        return httpHelper.get(url);
    }
    getDelayState(taskId: string): Observable<any> {
        const url = `/api/engine/v1/tasks/${taskId}/delay-state`;
        return httpHelper.get(url);
    }
    canDoneWithdraw(taskId: string): Observable<void> {
        const url = `/api/engine/v1/tasks/${taskId}/recall-state`;
        return httpHelper.get(url);
    }
    instanceSendRecvRecord_GetById(id: string) {
        const _url = `/api/process/v1/instanceSendRecvRecord/getById?id=${id}`;
        return httpHelper.get(_url, {}, { loading: false });
    }
}

export const pageBottomService = new PageBottomService();
