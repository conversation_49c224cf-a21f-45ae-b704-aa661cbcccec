export interface ActionDto {
    code: string;
    name: string;
}

export interface ActionDataDto {
    targetUsers: any;
    targetUserId?: string;
    targetUserName?: string;
    targetUserOrganizationPath?: string;
    targetUserOrganizationPathText?: string;
    params?: any;
    targetActivityId?: string;
    resolveComment?: string;
    instanceNumber?: string;
    taskId?: string;
    isCallSystem?: boolean;
}

export enum RejectTypeEnum {
rejectStart = '10',
rejectActivity = '11',
rejectStartDirect = '12',
rejectActivityDirect = '13',
}

export interface ParallelInfoDto {
isParallel?: boolean;
parallelPath?: string;
}

export interface InstanceBaseInfoDto {
    ownerName: string;
    source?: string;
    topic?: string;
    userId?: string;
    userName?: string;
    userAccount?: string;
    ownerId?: string;
    positionId?: string;
    positionName?: string;
    organizationId?: string;
    organizationPathText?: string;
    organizationPath?: string;
    startDate?: string;
    status?: string;
    istatus?: number;
    formEditable?: boolean;
    processId?: string;
    processName?: string;
    processVersion?: string;
    topBusinessTypeName?: string;
    authenticationMemberId?: string;
    btid?: string;
    boid?: string;
    canStartInvalidWithdraw?: boolean;
    actions?: InstanceActionDto[];
    authId?: string;
    instanceId?: string;
    parentProcessId?: string;
    category?: string;
}

export interface InstanceActionDto {
  code: string;
  name: string;
}
