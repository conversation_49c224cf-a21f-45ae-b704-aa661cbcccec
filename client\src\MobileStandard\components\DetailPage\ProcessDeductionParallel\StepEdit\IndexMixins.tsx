import { SeniorSelectUser } from '@/MobileStandard/components/SeniorSelectUser';
import { todoCenterService } from '@/MobileStandard/views/TodoCenter/service';
import lodash from 'lodash';
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({
    components: {
        SeniorSelectUser
    }
})
export class IndexMixins extends Vue {
    @Prop() step!: any;
    @Prop() visible!: boolean;
    copyStep: any = {};
    userTransferInputVisible = false;
    userTransferInputItems: any = {
        userType: '',
        userIndex: -1,
        items: []
    };
    userAvatars: any = {};
    @Watch('visible')
    visibleChange(newVal: any) {
        if (newVal) {
            this.copyStep = lodash.cloneDeep(this.step);
            console.log(this.copyStep);
            const ids: any = [];
            lodash.forEach(this.copyStep.approvers || [], (a: any) => {
                if (a.userId && !lodash.includes(ids, a.userId)) {
                    ids.push(a.userId);
                }
            });
            lodash.forEach(this.copyStep.carbonCopyUsers || [], (a: any) => {
                if (a.userId && !lodash.includes(ids, a.userId)) {
                    ids.push(a.userId);
                }
            });
            lodash.forEach(this.copyStep.stepResolvers || [], (a: any) => {
                lodash.forEach(a.users || [], (u: any) => {
                    if (u.userId && !lodash.includes(ids, u.userId)) {
                        ids.push(u.userId);
                    }
                });
            });
            lodash.forEach(this.copyStep.stepCCResolvers || [], (a: any) => {
                lodash.forEach(a.users || [], (u: any) => {
                    if (u.userId && !lodash.includes(ids, u.userId)) {
                        ids.push(u.userId);
                    }
                });
            });
            todoCenterService.getUserAvatar({ keys: ids, cacheType: 1 }).subscribe(avatars => {
                lodash.forEach(avatars, (d: any) => {
                    this.userAvatars[d.userId] = d.avatar;
                });
                this.$forceUpdate();
            });
        }
    }
    @Emit('cancel')
    cancel() { }
    @Emit('confirm')
    confirm(step: any) { }
    onDel(type: any, index: number, typeIndex: number) {
        if (type === 'new_user') {
            this.copyStep.approvers.splice(index, 1);
        } else if (type === 'user') {
            this.copyStep.stepResolvers[typeIndex].users.splice(index, 1);
        } else if (type === 'ccUser') {
            this.copyStep.stepCCResolvers[typeIndex].users.splice(index, 1);
        } else if (type === 'new_ccUser') {
            this.copyStep.carbonCopyUsers.splice(index, 1);
        }
    }
    onUserTransferInput(type: any, typeIndex: number) {
        this.userTransferInputVisible = true;
        if (type === 'new_user') {
            this.userTransferInputItems.items = lodash.cloneDeep(this.copyStep.approvers || []);
        } else if (type === 'user') {
            this.userTransferInputItems.typeIndex = typeIndex;
            this.userTransferInputItems.items = lodash.cloneDeep(this.copyStep.stepResolvers[typeIndex].users || []);
        } else if (type === 'ccUser') {
            this.userTransferInputItems.typeIndex = typeIndex;
            this.userTransferInputItems.items = lodash.cloneDeep(this.copyStep.stepCCResolvers[typeIndex].users || []);
        } else if (type === 'new_ccUser') {
            this.userTransferInputItems.items = lodash.cloneDeep(this.copyStep.carbonCopyUsers || []);
        }
        this.userTransferInputItems.userType = type;
    }
    onUserTransferInputOk(items: any) {
        this.userTransferInputVisible = false;
        if (this.userTransferInputItems.userType === 'new_user') {
            this.copyStep.approvers = [];
            lodash.forEach(items || [], (m: any) => {
                this.copyStep.approvers.push(m);
                if (!this.userAvatars[m.userId]) {
                    this.userAvatars[m.userId] = m.avatar;
                }
            });
        } else if (this.userTransferInputItems.userType === 'user') {
            this.copyStep.stepResolvers[this.userTransferInputItems.typeIndex].users = [];
            lodash.forEach(items || [], (m: any) => {
                this.copyStep.stepResolvers[this.userTransferInputItems.typeIndex].users.push(m);
                if (!this.userAvatars[m.userId]) {
                    this.userAvatars[m.userId] = m.avatar;
                }
            });
        } else if (this.userTransferInputItems.userType === 'ccUser') {
            this.copyStep.stepCCResolvers[this.userTransferInputItems.typeIndex].users = [];
            lodash.forEach(items || [], (m: any) => {
                this.copyStep.stepCCResolvers[this.userTransferInputItems.typeIndex].users.push(m);
                if (!this.userAvatars[m.userId]) {
                    this.userAvatars[m.userId] = m.avatar;
                }
            });
        } else if (this.userTransferInputItems.userType === 'new_ccUser') {
            this.copyStep.carbonCopyUsers = [];
            lodash.forEach(items || [], (m: any) => {
                this.copyStep.carbonCopyUsers.push(m);
                if (!this.userAvatars[m.userId]) {
                    this.userAvatars[m.userId] = m.avatar;
                }
            });
        }
        this.$forceUpdate();
    }
    onConfirmCheck(step: any) {
        console.log(step);
        let nodeApprovalCount = 0; // 节点审批人数
        if (step.newNode) {
            if (step.approvers !== null && step.approvers.length > 0) {
                nodeApprovalCount = step.approvers.length;
            }
        } else {
            if (step.stepResolvers && step.stepResolvers.length > 0) {
                lodash.forEach(step.stepResolvers, (s: any) => {
                    if (s.users && s.users.length > 0) {
                        nodeApprovalCount = nodeApprovalCount + s.users.length;
                    }
                });
            }
        }
        if (step.nodeMaxApprovalCount && nodeApprovalCount > step.nodeMaxApprovalCount) {
            this.$toast({
                type: 'fail',
                forbidClick: true,
                message: this.$l.getLocale('tips.node-approver-limited-message')
                    .replace('{0}', step.nodeMaxApprovalCount)
            });
            return false;
        }
        this.confirm(step);
    }
    get approvalUserCount() {
        if (this.copyStep.newNode) {
            return (this.copyStep.approvers || []).length;
        } else {
            let count = 0;
            lodash.forEach(this.copyStep.stepResolvers || [], (r: any) => {
                lodash.forEach(r.users, (u: any) => {
                    count += 1;
                });
            });
            return count;
        }
    }
    get approvalCCUserCount() {
        if (this.copyStep.newNode || !this.copyStep.stepCCResolvers) {
            return (this.copyStep.carbonCopyUsers || []).length;
        } else {
            let count = 0;
            lodash.forEach(this.copyStep.stepCCResolvers || [], (r: any) => {
                lodash.forEach(r.users, (u: any) => {
                    count += 1;
                });
            });
            return count;
        }
    }
    created() {
    }
}
