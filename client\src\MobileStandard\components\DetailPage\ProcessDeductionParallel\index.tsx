import { Component } from 'vue-property-decorator';
import styles from './process-deduction.module.less';
import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import lodash from 'lodash';

@Component
export class ProcessDeductionParallel extends mixins(IndexMixins) {
  render() {
    return <instance-collapse-item title={this.$l.getLocale('fields.approvalProcess')}
      extend-title={this.branchName}>
      <div class={styles.steps}>
        {
          lodash.map(this.previewSteps, (s: any, si: number) => {
            return si === 0 || si >= this.rejectStepIndex ? <div class={styles.step_content}>
              <ul class={styles.line}>
                <li class={styles.line_item}>
                  <div class={styles.line_item_tail}></div>
                  <div class={styles.line_item_head}>
                    <img src={require('../../../../assets/images/步骤状态_default.png')} />
                  </div>
                  <div class={styles.line_item_content}>
                    {s.isParallel ? (
                      <div class={styles.line_item_content_parastep}>
                        {
                          s.paralleSteps.map((e: any, parIndex: number) => {
                            return (
                              <div class={styles.line_item_content_parastep}>
                                {e.map((a: any, parStepIndex: number) => {
                                  return (
                                    <div class={styles.line_item_content_step} styles='padding-bottom:5px;'>
                                      <div class={styles.line_item_content_stepName}>
                                        {
                                          a.name
                                        }
                                        <div class={styles.editButtons}>
                                          {
                                            (this.getResolverEditable(a) &&
                                              this.canEditPreview(a, this.baseInfo)) ?
                                              <span class={styles.edit}
                                                on-click={() => this.onStepEdit(a, si, parIndex, parStepIndex)}>
                                                {
                                                  this.$l.getLocale('buttons.edit')
                                                }
                                              </span> : ''
                                          }
                                        </div>
                                      </div>
                                      <div class={styles.users}>
                                        {
                                          a.stepResolvers && a.stepResolvers.length > 0 ?
                                            lodash.map(a.stepResolvers || [], (sr: any) => {
                                              return lodash.map(sr.users || [], (u: any) => {
                                                return <div class={styles.user}>
                                                  <span class={styles.user_name}>
                                                    <user-card class={styles.user_name}
                                                      userInfo={{
                                                        userId: u.userId,
                                                        userName: u.userName,
                                                        post: u.fullPathPostion
                                                      }}
                                                      on-conversation={(userId: string) => this.conversation(userId)} />
                                                  </span>
                                                  {
                                                    sr.resolverName ? <span class={styles.user_post}>
                                                      {
                                                        `(${sr.resolverName})`
                                                      }
                                                    </span> : ''
                                                  }
                                                </div>;
                                              });
                                            }) :
                                            lodash.map(a.approvers || [], (sr: any) => {
                                              return <div class={styles.user}>
                                                <span class={styles.user_name}>
                                                  <user-card class={styles.user_name}
                                                    userInfo={{
                                                      userId: sr.userId,
                                                      userName: sr.userName,
                                                      post: sr.fullPathPostion
                                                    }}
                                                    on-conversation={(userId: string) => this.conversation(userId)} />
                                                </span>
                                              </div>;
                                            })
                                        }
                                      </div>
                                    </div>);
                                })}
                              </div>
                            );
                          })
                        }
                      </div>
                    ) : (
                      <div class={styles.line_item_content_step +
                        (!this.hasUser(s) && s.nodeType !== 'end' &&
                          s.nodeType !== 'start' ? ' ' + styles.empty : '')}>
                        <div class={styles.line_item_content_stepName}>
                          {
                            s.nodeType === 'start' ? '发起' : s.name
                          }
                          <div class={styles.editButtons}>
                            {
                              (s.newNode ||
                                (!s.newNode && this.getResolverEditable(s) &&
                                  this.canEditPreview(s, this.baseInfo))) ?
                                <span class={styles.edit}
                                  on-click={() => this.onStepEdit(s, si, 9999, 9999)}>
                                  {
                                    this.$l.getLocale('buttons.edit')
                                  }
                                </span> : ''
                            }
                            {
                              s.nodeType !== 'end' &&
                                s.nodeType !== 'start' &&
                                (s.newNode ||
                                  (s.nodeEditable &&
                                    this.canEditPreview(s, this.baseInfo))) ?
                                <span class={styles.delete}
                                  on-click={() => this.onStepDelete(s, si)}>
                                  {
                                    this.$l.getLocale('buttons.delete')
                                  }
                                </span> : ''
                            }
                          </div>
                        </div>
                        <div class={styles.users}>
                          {
                            s.stepResolvers && s.stepResolvers.length > 0 ?
                              lodash.map(s.stepResolvers || [], (sr: any) => {
                                return lodash.map(sr.users || [], (u: any) => {
                                  return <div class={styles.user}>
                                    <span class={styles.user_name}>
                                      <user-card class={styles.user_name}
                                        userInfo={{
                                          userId: u.userId,
                                          userName: u.userName,
                                          post: u.fullPathPostion
                                        }}
                                        on-conversation={(userId: string) => this.conversation(userId)} />
                                    </span>
                                    {
                                      s.nodeType !== 'start' && s.nodeType !== 'end' &&
                                        sr.resolverName ? <span class={styles.user_post}>
                                        {
                                          `(${sr.resolverName})`
                                        }
                                      </span> : ''
                                    }
                                  </div>;
                                });
                              }) :
                              lodash.map(s.approvers || [], (sr: any) => {
                                return <div class={styles.user}>
                                  <span class={styles.user_name}>
                                    <user-card class={styles.user_name}
                                      userInfo={{
                                        userId: sr.userId,
                                        userName: sr.userName,
                                        post: sr.fullPathPostion
                                      }}
                                      on-conversation={(userId: string) => this.conversation(userId)} />
                                  </span>
                                </div>;
                              })
                          }
                        </div>
                      </div>
                    )}

                    {
                      this.editable && s.nodeType !== 'end' &&
                        this.rejectStepIndex === -1 ?
                        <div class={styles.addStep} on-click={() => this.onAddStep(si, s)}>
                          <i class='iconfont icon-btn_addtabe' />
                          <div class={styles.addRemark}>{this.$l.getLocale('buttons.addStep')}</div>
                        </div> : ''
                    }
                  </div>
                </li>
              </ul>
            </div> : '';
          })
        }
      </div>
      <step-edit
        visible={this.stepEditVisible}
        step={this.editStep}
        on-cancel={() => { document.title = this.documentTitle; this.stepEditVisible = false; }}
        on-confirm={(step: any) => this.onStepEditConfim(step)}
      ></step-edit>
    </instance-collapse-item>;
  }
}
