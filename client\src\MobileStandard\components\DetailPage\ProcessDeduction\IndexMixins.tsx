import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator';
import debounce from 'lodash/debounce';
import lodash from 'lodash';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import { guidHelper, subjectHelper } from '@/MobileStandard/common/utils';
import { processService } from '@/MobileStandard/services/process';
import { processDeductionService } from './service';
import { ApproveState, InstanceBaseInfoDto } from '../BaseInfo/type';
import { PreviewStepActionDto, PreviewStepChangeDto, PreviewStepDto, StepChangeType } from './types';
import { StepEdit } from './StepEdit';
import { UserCard } from '../..';
import { RejectTypeEnum } from '../PageBottom/types';

@Component({
  components: {
    StepEdit,
    UserCard
  }
})
export class IndexMixins extends Vue {
  @Prop() urlParamteres!: UrlParametersDto;
  loading = true;
  baseInfo!: InstanceBaseInfoDto;
  formInfo!: any;
  defaultActions: PreviewStepActionDto[] = [];
  defaultPolicy: any = {};
  defaultBatchApproval = false;
  defaultFormEditable = false;
  keyParameter!: string[];
  previewSteps!: PreviewStepDto[];
  branchName = '';
  firstBranchName = '';
  branchNameExtra: any;
  cacheSteps: PreviewStepDto[] = [];
  cacheBaseInfo!: InstanceBaseInfoDto;
  cacheFormInfo!: any;
  editable = true;
  fetchSteps = debounce(() => this.onSearchSteps(), 500);
  systemPar!: any;
  cacheSystemPar!: any;
  stepEditVisible = false;
  editStep!: PreviewStepDto;
  editStepIndex = -1;
  fixedProcessUsers = false;
  processMaxApprovalCount = 0;
  defaultNodeMaxApprovalCount = 0;
  defaultNodeMaxConsultCount = 0;
  defaultNodeMaxHandoverCount = 0;
  defaultTimeoutAutoApproval = false;
  defaultAutoApprovalTime = 36;
  defaultDelayTime = 1;
  rejectStepId = '';
  rejectStepIndex = -1;
  documentTitle = '';
  getStepType = 0;
  draftSteps: any = [];
  isKeyWordsChange = false;
  isAllNode = true;
  @Emit('change')
  onChange(hasBranch: boolean, isDeductioning: boolean) { }
  @Emit('conversation')
  conversation(userId: string) { }
  onSearchSteps() {
    this.loading = false;
    if (this.baseInfo && this.formInfo && this.keyParameter && this.systemPar) {
      // 没推演过、用户发生改变、用户岗位发生改变、用户组织发生改变、关键参数信息发生改变
      let isDeduction = false;
      if (!this.previewSteps || !this.cacheBaseInfo || !this.cacheFormInfo) {
        isDeduction = true;
      } else if (this.keyParameter.length > 0 &&
        lodash.some(this.keyParameter, (k: any) => this.cacheFormInfo[k] !== this.formInfo[k])) {
        isDeduction = true;
        this.isKeyWordsChange = true;
      } else if (this.keyParameter.length > 0 &&
        lodash.some(this.keyParameter, (k: any) => {
          if (lodash.isArray(this.formInfo[k]) || lodash.isObject(this.formInfo[k])) {
            return JSON.stringify(this.cacheFormInfo[k]) !== JSON.stringify(this.formInfo[k]);
          } else {
            return this.cacheFormInfo[k] !== this.formInfo[k];
          }
        })) {
        isDeduction = true;
      }
      if (isDeduction) {
        this.previewSteps = [];
        this.loading = true;
        this.onChange(this.previewSteps && this.previewSteps.length > 0, true);
        if (this.urlParamteres.pageType === 'start' &&
          !this.urlParamteres.processVersionChange &&
          this.urlParamteres.againStartNumber &&
          (!this.cacheBaseInfo && !this.cacheFormInfo)) {
          this.cacheBaseInfo = lodash.cloneDeep(this.baseInfo);
          this.cacheFormInfo = lodash.cloneDeep(this.formInfo);
          processDeductionService.processStepsByInstanceNumber(this.urlParamteres.againStartNumber).subscribe((s: any) => {
            this.getStepType = 1;
            this.setSteps(s);
          });
        } else {
          // 直接退回根据number推演，其他的都根据authid推演
          const number = this.baseInfo.status === 'rejected' &&
            (this.baseInfo.istatus || 0).toString() === RejectTypeEnum.rejectStartDirect
            && !this.urlParamteres.authId ?
            this.urlParamteres.number : '';
          this.cacheBaseInfo = lodash.cloneDeep(this.baseInfo);
          this.cacheFormInfo = lodash.cloneDeep(this.formInfo);
          const params = {
            ...lodash.assign(lodash.cloneDeep(this.formInfo), lodash.cloneDeep(this.baseInfo)),
            isKeyWordsChange: this.isKeyWordsChange
          };
          processDeductionService.get(
            this.baseInfo.parentProcessId || '',
            number || '',
            this.urlParamteres.authId || '',
            params).subscribe((s: any) => {
              this.getStepType = 0;
              this.setSteps(s);
            });
        }
      }
    }
  }
  setSteps(s: any) {
    this.isAllNode = s.isAllNode;
    this.rejectStepIndex = -1;
    this.previewSteps = s.steps;
    if (this.previewSteps && this.previewSteps.length > 0) {
      lodash.forEach(this.previewSteps, (p: any) => p.name = p.nodeType === 'start' ? '发起' : p.name);
    }
    this.branchName = s.branchName;
    this.editable = s.editable;
    this.fixedProcessUsers = s.fixedProcessUsers;
    this.loading = false;
    this.processMaxApprovalCount = s.processMaxApprovalCount;
    this.rejectStepId = s.rejectStepId;
    // 退回 直接提交退回人
    if (this.baseInfo && this.baseInfo.status === ApproveState.rejected &&
      (this.baseInfo.istatus || 0).toString() === RejectTypeEnum.rejectStartDirect &&
      this.firstBranchName && this.firstBranchName !== this.branchName) {
      this.branchNameExtra = <span>流程分支条件发生变化，重新推演流程!</span>;
    } else {
      this.branchNameExtra = null;
      if (this.rejectStepId && this.baseInfo && this.baseInfo.status === ApproveState.rejected &&
        (this.baseInfo.istatus || 0).toString() === RejectTypeEnum.rejectStartDirect) {
        this.rejectStepIndex = lodash.findIndex(this.previewSteps, (p: any) => p.id === this.rejectStepId);
      }
    }
    if (!this.firstBranchName) {
      this.firstBranchName = this.branchName;
      // 从草稿里把审批人带过来
      if (!this.urlParamteres.number && this.draftSteps &&
        (!this.cacheSteps || this.cacheSteps.length === 0)) {
        lodash.forEach(this.previewSteps, (ps: any) => {
          // 找到草稿里的步骤
          const step = lodash.find(this.draftSteps, (d: any) => d.name === ps.name);
          if (ps.nodeType !== 'start' && ps.nodeType !== 'end' && step) {
            // 审批人
            if (ps.stepResolvers && ps.stepResolvers.length > 0 &&
              step.stepResolvers && step.stepResolvers.length > 0) {
              // 循环角色
              const czspr: any = [];
              lodash.forEach(ps.stepResolvers, (ss: any) => {
                const role = lodash.find(step.stepResolvers, (srl: any) => srl.resolverId === ss.resolverId);
                if (role) {
                  ss.users = role.users;
                }
                czspr.push(...ss.users);
              });
              ps.approvers = czspr;
            } else {
              ps.approvers = step.approvers;
            }

            // 抄送人
            ps.stepCCResolvers = step.stepCCResolvers;
            ps.carbonCopyUsers = step.carbonCopyUsers;
          }
        });
      }
    }
    this.cacheSteps = lodash.cloneDeep(this.previewSteps);
    this.onChange(this.previewSteps && this.previewSteps.length > 0, false);
    this.$forceUpdate();
  }
  findSystemPar() {
    if (this.baseInfo && this.formInfo) {
      const params = {
        templateId: this.baseInfo.parentProcessId,
        organizationId: this.baseInfo.organizationId,
        userId: this.baseInfo.userId,
        postId: this.baseInfo.positionId,
        instanceNumber: this.urlParamteres.number,
      };
      if (!this.cacheSystemPar || (this.cacheSystemPar &&
        this.cacheSystemPar.postId !== params.postId)) {
        this.cacheSystemPar = lodash.cloneDeep(params);
        processService.GetSystemPar(params).subscribe(rs => {
          const systemPar: any = {};
          if (rs) {
            rs.forEach((item: any) => {
              systemPar[item.label] = item.value;
            });
          }
          this.systemPar = systemPar;
          this.formInfo = Object.assign(this.formInfo, this.systemPar); // 不管什么情况，系统参数，都放入表单参数中，便于推演
          subjectHelper.getSubject('systemPar$').next(this.systemPar);
          this.fetchSteps();
        });
      }
    }
  }

  addStep(index: number, m: PreviewStepDto) {
    const preNode = this.previewSteps[index];
    const gid = guidHelper.generate();
    this.previewSteps.splice(index + 1, 0, {
      id: gid,
      name: `${this.$l.getLocale('fields.newStep')}`,
      actions: this.defaultActions,
      sourceNodeId: preNode.id,
      nodeEditable: true,
      resolverEditable: true,
      newNode: true,
      isParallel: false,
      paralleSteps: [],
      group: index,
      OldUsers: [],
      OldCCUsers: [],
      policy: this.defaultPolicy,
      batchApproval: this.defaultBatchApproval,
      formEditable: this.defaultFormEditable,
      approvers: [],
      stepResolvers: [],
      stepCCResolvers: [],
      nodeMaxApprovalCount: this.defaultNodeMaxApprovalCount,
      nodeMaxConsultCount: this.defaultNodeMaxConsultCount,
      nodeMaxHandoverCount: this.defaultNodeMaxHandoverCount,
      timeoutAutoApproval: this.defaultTimeoutAutoApproval,
      autoApprovalTime: this.defaultAutoApprovalTime,
      delayTime: this.defaultDelayTime,
    });
    if (this.previewSteps[index + 2]) {
      this.previewSteps[index + 2].sourceNodeId = gid;
    }
    this.$forceUpdate();
  }

  deleteStep(index: number) {
    this.previewSteps[index + 1].sourceNodeId = this.previewSteps[index - 1].id;
    this.previewSteps.splice(index, 1);
    this.$forceUpdate();
  }

  onStepChange(index: number, step: any) {
    this.previewSteps[index].approvers = step.approvers;
    this.previewSteps[index].stepResolvers = step.stepResolvers;
    this.previewSteps[index].carbonCopyUsers = step.carbonCopyUsers;
    this.previewSteps[index].stepCCResolvers = step.stepCCResolvers;
    this.previewSteps[index].name = step.name;
  }

  hasUser(step: any) {
    let result = true;
    if (step.newNode) {
      if ((step.approvers || []).length === 0) {
        result = false;
      }
    } else {
      lodash.forEach(step.stepResolvers || [], (r: any) => {
        if ((r.users || []).length === 0) {
          result = false;
        }
      });
    }
    return result;
  }

  public getValues(type: string) {
    const approvalCountMsgs: string[] = [];
    if (type === 'start') {
      // 检查是否有空
      const errorMsgs: string[] = [];
      const stepName: string[] = [];
      let isRepeat = false;
      let processApprovalCount = 0;
      lodash.map(this.previewSteps, (f: any) => {
        let nodeApprovalCount = 0;
        if (f.policy) {
          if (f.policy.emptyApproverType && f.policy.emptyApproverType.toString() === '-1') {
            let stepEmpty = false;
            let roleNames = '';
            if (f.newNode) {
              if (!(f.approvers && f.approvers.length > 0)) {
                stepEmpty = true;
              }
              if (stepEmpty) {
                errorMsgs.push(f.name);
              }
            } else {
              if (f.stepResolvers && f.stepResolvers.length > 0) {
                lodash.forEach(f.stepResolvers, (s: any) => {
                  if (!(s.users && s.users.length > 0)) {
                    if (s.resolverName) {
                      roleNames = roleNames === '' ? s.resolverName : `${roleNames}、${s.resolverName}`;
                    } else {
                      roleNames = roleNames === '' ? s.roleName : `${roleNames}、${s.roleName}`;
                    }
                    stepEmpty = true;
                  }
                });
                if (stepEmpty) {
                  errorMsgs.push(`${f.name}步骤: ${roleNames}`);
                }
              }
            }
          }
          // 流程最大审批人数
          if (f.newNode) {
            if (f.approvers && f.approvers.length > 0) {
              processApprovalCount = processApprovalCount + f.approvers.length;
              nodeApprovalCount = nodeApprovalCount + f.approvers.length;
            }
          } else {
            if (f.stepResolvers && f.stepResolvers.length > 0) {
              lodash.forEach(f.stepResolvers, (s: any) => {
                if (s.users && s.users.length > 0) {
                  processApprovalCount = processApprovalCount + s.users.length;
                  nodeApprovalCount = nodeApprovalCount + s.users.length;
                }
              });
            }
          }
        }
        if (!isRepeat && lodash.includes(stepName, f.name)) {
          isRepeat = true;
        } else if (!isRepeat && !lodash.includes(stepName, f.name)) {
          stepName.push(f.name);
        }
        // if (f.nodeMaxApprovalCount && f.nodeMaxApprovalCount > 0 && nodeApprovalCount > f.nodeMaxApprovalCount) {
        //   approvalCountMsgs.push(`${f.name}${this.$l.getLocale('tips.node-approver-limited')}${f.nodeMaxApprovalCount}`);
        // }
      });
      if (errorMsgs.length > 0 || isRepeat) {
        if (errorMsgs.length > 0) {
          errorMsgs.push(`${this.$l.getLocale('tips.start-steps-unhealthy')}`);
        }
        if (isRepeat) {
          errorMsgs.push(`${this.$l.getLocale('tips.start-steps-repeat')}`);
        }
        this.$toast({
          type: 'fail',
          forbidClick: true,
          message: lodash.join(errorMsgs, '\n')
        });
        return { isSuccess: false, data: null };
      }
      if (this.processMaxApprovalCount && this.processMaxApprovalCount > 0 && processApprovalCount > this.processMaxApprovalCount) {
        approvalCountMsgs.push(`${this.$l.getLocale('tips.process-approver-limited-message').replace('{0}', this.processMaxApprovalCount.toString())}`);
      }
    } else if (type === 'save') {
      const data = {
        steps: this.previewSteps,
        fixedProcessUsers: this.fixedProcessUsers,
        isKeyWordsChange: this.isKeyWordsChange,
        isAllNode: this.isAllNode
      };
      return { isSuccess: true, data: data };
    }
    // 重新组装需要的步骤
    let result: PreviewStepChangeDto[] = [];
    lodash.forEach(this.previewSteps, (f, i) => {
      let addResult!: PreviewStepChangeDto;
      const users: any = [];
      if (f.stepResolvers && f.stepResolvers.length > 0) {
        lodash.forEach(f.stepResolvers || [], (r: any) => {
          lodash.forEach(r.users, (u: any) => {
            users.push({
              userId: u.userId + '',
              userName: u.userName + '',
              resolverId: r.activityResolverId || '',
              resolverName: r.resolverName || '',
              activityResolverId: r.activityResolverId || '',
              fullPathPostion: u.fullPathPostion,
              expandData: u.expandData
            });
          });
        });
      } else {
        (f.approvers || []).map((m: any) => {
          users.push({
            userId: m.userId + '',
            userName: m.userName + '',
            fullPathPostion: m.fullPathPostion,
            expandData: m.expandData
          });
        });
      }
      const ccUsers: any = [];
      if (f.stepCCResolvers && f.stepCCResolvers.length > 0) {
        lodash.forEach(f.stepCCResolvers || [], (r: any) => {
          lodash.forEach(r.users, (u: any) => {
            ccUsers.push({
              userId: u.userId + '',
              userName: u.userName + '',
              resolverId: r.activityResolverId || '',
              resolverName: r.resolverName || '',
              activityResolverId: r.activityResolverId || '',
              fullPathPostion: u.fullPathPostion
            });
          });
        });
      } else {
        (f.carbonCopyUsers || []).map((m: any) => {
          ccUsers.push({
            userId: m.userId + '',
            userName: m.userName + '',
            fullPathPostion: m.fullPathPostion
          });
        });
      }
      addResult = {
        id: f.id,
        name: f.name,
        actions: f.actions,
        type: StepChangeType.normal,
        users: users,
        ccUsers: ccUsers,
        group: '',
        order: i
      };
      if (f.newNode) {
        addResult = {
          id: f.id,
          name: f.name,
          type: StepChangeType.add,
          sourceNodeId: f.sourceNodeId,
          sourceNodeName: f.name,
          actions: f.actions,
          users: (f.approvers || []).map(m => ({
            userId: m.userId + '',
            userName: m.userName + '',
            fullPathPostion: m.fullPathPostion
          })),
          ccUsers: (f.carbonCopyUsers || []).map(m => ({
            userId: m.userId + '',
            userName: m.userName + '',
            fullPathPostion: m.fullPathPostion
          })),
          group: i,
          policy: f.policy,
          batchApproval: f.batchApproval,
          formEditable: f.formEditable,
          order: i,
          nodeMaxApprovalCount: f.nodeMaxApprovalCount,
          nodeMaxConsultCount: f.nodeMaxConsultCount,
          nodeMaxHandoverCount: f.nodeMaxHandoverCount,
          timeoutAutoApproval: f.timeoutAutoApproval,
          autoApprovalTime: f.autoApprovalTime,
          delayTime: f.delayTime,
        };
      } else if (f.resolverEditable) {
        const cacheStep = this.cacheSteps.find(step => step.id === f.id);
        const cacheStepUsers = cacheStep ? (cacheStep.approvers || []).map((m: any) => {
          return { userId: m.userId, fullPathPostion: m.fullPathPostion };
        }) : [];
        const cacheStepCCUsers = cacheStep ? (cacheStep.carbonCopyUsers || []).map(m => {
          return { userId: m.userId, fullPathPostion: m.fullPathPostion };
        }) : [];
        const stepUsers = addResult.users || [];
        const stepCCUsers = addResult.ccUsers || [];
        if (cacheStep && !(stepUsers.length === cacheStepUsers.length &&
          stepUsers.every(e => lodash.findIndex(cacheStepUsers,
            (c: any) => c.userId === e.userId && c.fullPathPostion === e.fullPathPostion) > -1))) {
          const oldUsers: any = [];
          if (cacheStep.stepResolvers && cacheStep.stepResolvers.length > 0) {
            lodash.forEach(cacheStep.stepResolvers || [], (r: any) => {
              lodash.forEach(r.users, (u: any) => {
                oldUsers.push({
                  userId: u.userId + '',
                  userName: u.userName + '',
                  resolverId: r.activityResolverId || '',
                  resolverName: r.resolverName || '',
                  activityResolverId: r.activityResolverId || '',
                  fullPathPostion: u.fullPathPostion
                });
              });
            });
          } else {
            (cacheStep.approvers || []).map((m: any) => {
              oldUsers.push({
                userId: m.userId + '',
                userName: m.userName + '',
                fullPathPostion: m.fullPathPostion
              });
            });
          }
          addResult.OldUsers = oldUsers;
          addResult.type = StepChangeType.update;
        }

        if (cacheStep && !(stepCCUsers.length === cacheStepCCUsers.length &&
          stepCCUsers.every(e => lodash.findIndex(cacheStepCCUsers,
            (c: any) => c.userId === e.userId && c.fullPathPostion === e.fullPathPostion) > -1))) {
          const oldCCUsers: any = [];
          if (cacheStep.stepCCResolvers && cacheStep.stepCCResolvers.length > 0) {
            lodash.forEach(cacheStep.stepCCResolvers || [], (r: any) => {
              lodash.forEach(r.users, (u: any) => {
                oldCCUsers.push({
                  userId: u.userId + '',
                  userName: u.userName + '',
                  resolverId: r.activityResolverId || '',
                  resolverName: r.resolverName || '',
                  activityResolverId: r.activityResolverId || '',
                  fullPathPostion: u.fullPathPostion
                });
              });
            });
          } else {
            (cacheStep.carbonCopyUsers || []).map((m: any) => {
              oldCCUsers.push({
                userId: m.userId + '',
                userName: m.userName + '',
                fullPathPostion: m.fullPathPostion
              });
            });
          }
          addResult.OldCCUsers = oldCCUsers;
          addResult.type = StepChangeType.update;
        }
      }
      result.push(addResult);
    });
    // 删除数据处理
    this.cacheSteps.forEach((f, i) => {
      if (!f.newNode && !this.previewSteps.find(step => step.id === f.id)) {
        result.push({
          id: f.id,
          name: f.name,
          type: StepChangeType.delete,
          users: [],
          ccUsers: [],
          OldUsers: f.approvers,
          OldCCUsers: f.carbonCopyUsers,
          group: i,
        });
      }
    });

    // 如果是再次发起获取的推演步骤，全部改为修改状态
    if (this.getStepType === 1 || this.urlParamteres.draftId) {
      result = lodash.map(result, (r: any) => {
        return {
          ...r,
          type: r.type === StepChangeType.normal ? StepChangeType.update : r.type
        };
      });
    }
    const resultData = {
      steps: result,
      fixedProcessUsers: this.fixedProcessUsers,
      isKeyWordsChange: this.isKeyWordsChange,
      isAllNode: this.isAllNode
    };
    return { isSuccess: true, data: resultData, limitedMsg: approvalCountMsgs };
  }
  canEditPreview(s: any, baseInfo: any) {
    if (!this.urlParamteres.number) {
      return true;
    } else {
      return baseInfo && (baseInfo.status === ApproveState.ready ||
        (baseInfo.status === ApproveState.rejected &&
          (baseInfo.istatus || 0).toString() === RejectTypeEnum.rejectStart) ||
        (baseInfo.status === ApproveState.rejected &&
          (baseInfo.istatus || 0).toString() === RejectTypeEnum.rejectStartDirect &&
          this.rejectStepId !== s.id)
      );
    }
  }
  onStepDelete(step: any, si: number) {
    this.previewSteps.splice(si, 1);
    this.$forceUpdate();
  }
  onStepEdit(step: any, si: number) {
    this.documentTitle = document.title;
    document.title = '编辑审批人';
    this.stepEditVisible = true;
    this.editStepIndex = si;
    this.editStep = lodash.cloneDeep(step);
  }
  onStepEditConfim(step: any) {
    document.title = this.documentTitle;
    this.stepEditVisible = false;
    this.previewSteps[this.editStepIndex].approvers = step.approvers;
    this.previewSteps[this.editStepIndex].stepResolvers = step.stepResolvers;
    this.previewSteps[this.editStepIndex].carbonCopyUsers = step.carbonCopyUsers;
    this.previewSteps[this.editStepIndex].stepCCResolvers = step.stepCCResolvers;
    this.previewSteps[this.editStepIndex].name = step.name;
  }
  onAddStep(index: number, m: PreviewStepDto) {
    const preNode = this.previewSteps[index];
    const gid = guidHelper.generate();
    this.previewSteps.splice(index + 1, 0, {
      id: gid,
      name: '新步骤',
      actions: this.defaultActions,
      sourceNodeId: preNode.id,
      nodeEditable: true,
      resolverEditable: true,
      newNode: true,
      isParallel: false,
      paralleSteps: [],
      group: index,
      OldUsers: [],
      OldCCUsers: [],
      policy: this.defaultPolicy,
      batchApproval: this.defaultBatchApproval,
      formEditable: this.defaultFormEditable,
      approvers: [],
      stepResolvers: [],
      stepCCResolvers: [],
      nodeMaxApprovalCount: this.defaultNodeMaxApprovalCount,
      nodeMaxConsultCount: this.defaultNodeMaxConsultCount,
      nodeMaxHandoverCount: this.defaultNodeMaxHandoverCount,
      timeoutAutoApproval: this.defaultTimeoutAutoApproval,
      autoApprovalTime: this.defaultAutoApprovalTime,
      delayTime: this.defaultDelayTime,
    });
    if (this.previewSteps[index + 2]) {
      this.previewSteps[index + 2].sourceNodeId = gid;
    }
    this.$forceUpdate();
  }
  getResolverEditable(s: any) {
    if (s.newNode) {
      return true;
    } else if ((s.stepResolvers || []).length > 0) {
      let isEdit = false;
      lodash.forEach(s.stepResolvers || [], (r: any) => {
        if (r.resolverEditable) {
          isEdit = true;
          return false;
        }
      });
      return isEdit;
    } else {
      return s.resolverEditable;
    }
  }
  created() {
    subjectHelper.getSubject('draftInfo$').subscribe((s: any) => {
      this.draftSteps = s.data.previewData;
      this.isKeyWordsChange = s.data.isKeyWordsChange || false;
    });
    subjectHelper.getSubject('baseInfo$').subscribe((s: any) => {
      this.baseInfo = s;
      this.findSystemPar();
      const number = this.urlParamteres.againStartNumber && !this.urlParamteres.processVersionChange ?
        this.urlParamteres.againStartNumber : !this.urlParamteres.authId ?
          this.urlParamteres.number : '';
      const authId = this.urlParamteres.againStartNumber && !this.urlParamteres.processVersionChange ?
        '' : this.urlParamteres.authId;
      // 获取推演关键参数
      processService.getProcessMainParams(
        this.baseInfo.parentProcessId || '',
        number || '',
        authId || '').subscribe((key: any) => {
          this.keyParameter = [];
          if (key && key.length > 0) {
            const items = key.filter((d: any) => d.keyParameterType === 1 || d.keyParameterType === 2
              || d.keyParameterType === 5 || d.keyParameterType === 6 || d.keyParameterType === 7);
            if (items && items.length > 0) {
              this.keyParameter = items.map((m: any) => m.keyParameter);
            }
          }
          this.fetchSteps();
        });
    });
    subjectHelper.getSubject('formInfo$').subscribe((s: any) => {
      this.formInfo = s;
      this.findSystemPar();
      this.fetchSteps();
    });
    this.onChange(this.previewSteps && this.previewSteps.length > 0, false);
    processDeductionService.getStepDefaultSetting().subscribe(m => {
      this.defaultActions = JSON.parse(m.stepActions);
      this.defaultPolicy.concurrentExpression = m.concurrentExpression;
      this.defaultPolicy.concurrentType = m.concurrentType;
      this.defaultPolicy.emptyApproverExpression = m.emptyApproverExpression;
      this.defaultPolicy.emptyApproverType = m.emptyApproverType.toString();
      this.defaultPolicy.participatingExpression = m.participatingExpression;
      this.defaultPolicy.participatingType = m.participatingType;
      this.defaultPolicy.sameStartApproverType = m.sameStartApproverType;
      this.defaultPolicy.sameApproverType = m.sameApproverType === '1' ? '2' : '0';
      this.defaultPolicy.limitApprover = m.limitApprover === 'true' ? 1 : 0;
      this.defaultBatchApproval = m.batchApproval;
      this.defaultFormEditable = m.formEditable;
      this.defaultNodeMaxApprovalCount = m.nodeMaxApprovalCount;
      this.defaultNodeMaxConsultCount = m.nodeMaxConsultCount;
      this.defaultNodeMaxHandoverCount = m.nodeMaxHandoverCount;
      this.defaultTimeoutAutoApproval = m.timeoutAutoApproval;
      this.defaultAutoApprovalTime = m.autoApprovalTime;
      this.defaultDelayTime = m.delayTime;
    });
  }
}
