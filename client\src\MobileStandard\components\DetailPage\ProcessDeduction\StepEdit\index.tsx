
import lodash from 'lodash';
import { mixins } from 'vue-class-component';
import { Component } from 'vue-property-decorator';
import styles from './step-edit.module.less';
import { IndexMixins } from './IndexMixins';

@Component
export class StepEdit extends mixins(IndexMixins) {
    render() {
        return (
            <van-action-sheet v-model={this.visible}
                round={false}
                on-click-overlay={this.cancel}
                class={styles.action_sheet}
            >
                <div class={styles.main_content}>
                    <div class={styles.step_name}>
                        <van-field
                            v-model={this.copyStep.name}
                            placeholder='请输入步骤'
                            label='步骤：'
                            readonly={!this.copyStep.newNode}
                        />
                    </div>
                    <div class={styles.content}>
                        <div class={styles.content_approval}>
                            <div class={styles.approval_desc}>
                                <div class={styles.spr}>
                                    <span class={styles.t}>审批人</span>
                                    <span class={styles.t_ex}>
                                        (已选
                                        <span class={styles.t_ex_c}>
                                            {
                                                ` ${this.approvalUserCount} `
                                            }
                                        </span>
                                        人)
                                    </span>
                                </div>
                                {
                                    this.copyStep.newNode ?
                                        <div class={styles.img}
                                            on-click={() => this.onUserTransferInput('new_user', -1)}>
                                            <i class='iconfont icon-input_adduser'
                                                style='color:#009a3e;font-size:15px;'></i>
                                        </div> : ''
                                }
                            </div>
                            {
                                this.copyStep.newNode ? <div class={styles.users}>
                                    {
                                        lodash.map(this.copyStep.approvers || [], (u: any, ui: number) => {
                                            return <div class={styles.user}>
                                                <div class={styles.avator}>
                                                    <img src={this.userAvatars[u.userId] || require('../../../../../assets/images/Avatar_default.png')} />
                                                    <div class={styles.del}
                                                        on-click={() => this.onDel('new_user', ui, -1)}>
                                                        <img src={require('../../../../../assets/images/btn_shanchu.png')} />
                                                    </div>
                                                </div>
                                                <span>{u.userName}</span>
                                            </div>;
                                        })}</div> : lodash.map(this.copyStep.stepResolvers || [], (u: any, ui: number) => {
                                            return [<div class={styles.resolver}>
                                                <span>{u.resolverType === 0 && !u.resolverName ? '办理人' : u.resolverName}</span>
                                                {u.resolverEditable ? <div class={styles.img}
                                                    on-click={() => this.onUserTransferInput('user', ui)}>
                                                    <i class='iconfont icon-input_adduser' style='color:#009a3e;font-size:15px;'></i>
                                                </div> : ''}
                                            </div>,
                                            <div class={styles.users}>
                                                {
                                                    lodash.map(u.users || [], (uu: any, uui: number) => {
                                                        return <div class={styles.user}>
                                                            <div class={styles.avator}>
                                                                <img src={this.userAvatars[uu.userId] || require('../../../../../assets/images/Avatar_default.png')} />
                                                                {uu.resolverEditable ? <div class={styles.del}
                                                                    on-click={() => this.onDel('user', uui, ui)}>
                                                                    <img src={require('../../../../../assets/images/btn_shanchu.png')} />
                                                                </div> : ''}
                                                            </div>
                                                            <span>{uu.userName}</span>
                                                        </div>;
                                                    })
                                                }
                                            </div>];
                                        })
                            }
                        </div>
                        <div class={styles.content_approval}>
                            <div class={styles.approval_desc}>
                                <div class={styles.spr_cc}>
                                    <span class={styles.t}>抄送人</span>
                                    <span class={styles.t_ex}>
                                        (已选
                                        <span class={styles.t_ex_c}>
                                            {
                                                ` ${this.approvalCCUserCount} `
                                            }
                                        </span>
                                        人)
                                    </span>
                                </div>
                                {
                                    this.copyStep.newNode || !this.copyStep.stepCCResolvers ?
                                        <div class={styles.img}
                                            on-click={() => this.onUserTransferInput('new_ccUser', -1)}>
                                            <i class='iconfont icon-input_adduser'
                                                style='color:#009a3e;font-size:15px;'></i>
                                        </div> : ''
                                }
                            </div>
                            {
                                this.copyStep.newNode || !this.copyStep.stepCCResolvers ? <div class={styles.users}>
                                    {
                                        lodash.map(this.copyStep.carbonCopyUsers || [], (u: any, ui: number) => {
                                            return <div class={styles.user}>
                                                <div class={styles.avator}>
                                                    <img src={this.userAvatars[u.userId] || require('../../../../../assets/images/Avatar_default.png')} />
                                                    <div class={styles.del}
                                                        on-click={() => this.onDel('new_ccUser', ui, -1)}>
                                                        <img src={require('../../../../../assets/images/btn_shanchu.png')} />
                                                    </div>
                                                </div>
                                                <span>{u.userName}</span>
                                            </div>;
                                        })}</div> : lodash.map(this.copyStep.stepCCResolvers || [], (u: any, ui: number) => {
                                            return [<div class={styles.resolver}>
                                                <span>{u.resolverType === 0 && !u.resolverName ? '抄送人' : u.resolverName}</span>
                                                <div class={styles.img}
                                                    on-click={() => this.onUserTransferInput('ccUser', ui)}>
                                                    <i class='iconfont icon-input_adduser'
                                                        style='color:#009a3e;font-size:15px;'></i>
                                                </div>
                                            </div>,
                                            <div class={styles.users}>
                                                {
                                                    lodash.map(u.users || [], (uu: any, uui: number) => {
                                                        return <div class={styles.user}>
                                                            <div class={styles.avator}>
                                                                <img src={this.userAvatars[uu.userId] || require('../../../../../assets/images/Avatar_default.png')} />
                                                                <div class={styles.del}
                                                                    on-click={() => this.onDel('ccUser', uui, ui)}>
                                                                    <img src={require('../../../../../assets/images/btn_shanchu.png')} />
                                                                </div>
                                                            </div>
                                                            <span>{uu.userName}</span>
                                                        </div>;
                                                    })
                                                }
                                            </div>];
                                        })
                            }
                        </div>
                    </div>
                </div>
                <div class={styles.bottom}>
                    <div class={styles.button_cancel}
                        on-click={() => this.cancel()}>{this.$l.getLocale('buttons.cancel')}</div>
                    <div class={styles.button_ok}
                        on-click={() => this.onConfirmCheck(this.copyStep)}>{this.$l.getLocale('buttons.ok')}</div>
                </div>
                <user-transfer-input
                    visible={this.userTransferInputVisible}
                    multiple={true}
                    items={this.userTransferInputItems.items}
                    on-close={() => this.userTransferInputVisible = false}
                    on-ok={(items: any) => this.onUserTransferInputOk(items)}
                />
            </van-action-sheet>
        );
    }
}
