.action_sheet {
    height: 100%;
    max-height: 100% !important;

    :global(.van-action-sheet__content) {
        background: #f0f2f5;
    }

    .main_content {
        overflow: auto;
        height: calc(~'100% - 60px');

        .step_name {
            background: #ffffff;
            margin-bottom: 10px;

            :global(.van-field__label) {
                width: auto;
                margin-right: 0px;
                color: #999999;
            }
            // .step_name_desc {
            //     color: #999999;
            //     font-size: 15px;
            //     letter-spacing: 0.51px;
            // }

            // .step_name_title {
            //     color: #333333;
            //     font-size: 15px;
            //     letter-spacing: 0.51px;
            // }
        }

        .content {

            .content_approval {
                background: #ffffff;
                padding: 0 12px;
                margin-bottom: 10px;

                .approval_desc {
                    height: 65px;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;

                    .spr, .spr_cc {
                        position: relative;
                    }

                    .spr::before {
                        position: absolute;
                        left: -5px;
                        color: #ee0a24;
                        font-size: 12px;
                        content: '*';
                        top: 5px;
                    }

                    .img {
                        width: 25px;
                        height: 25px;
                        background: #ffffff;
                        border-radius: 50%;
                        box-shadow: 2px 2px 13px 0px rgba(0, 0, 0, 0.11);
                        line-height: 25px;
                        text-align: center;
                    }

                    .t {
                        color: #333333;
                        font-size: 15px;
                        font-weight: bold;
                        position: relative;
                    }

                    .t_ex {
                        color: #333333;
                        font-size: 12px;

                        .t_ex_c {
                            color: #be4848;
                        }
                    }
                }

                .resolver {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    .img {
                        width: 25px;
                        height: 25px;
                        background: #ffffff;
                        border-radius: 50%;
                        box-shadow: 2px 2px 13px 0px rgba(0, 0, 0, 0.11);
                        line-height: 25px;
                        text-align: center;
                    }
                }
                .users {
                    min-height: 65px;
                    display: flex;
                    flex-wrap: wrap;

                    .user {
                        min-width: 50px;
                        width: 20%;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        margin-bottom: 20px;

                        .avator {
                            width: 30px;
                            height: 30px;
                            position: relative;

                            img {
                                width: 30px;
                                height: 30px;
                                border-radius: 50%;
                            }
                        }


                        span {
                            display: block;
                            font-size: 12px;
                            color: #666666;
                            overflow: hidden;
                        }

                        .del {
                            position: absolute;
                            border: 1px solid #ffffff;
                            border-radius: 50%;
                            top: -8px;
                            right: -8px;
                            background: #ffffff;
                            height: 15px;
                            width: 15px;
                            display: flex;
                            align-items: center;
                            justify-content: center;

                            img {
                                width: 16px;
                                height: 16px;
                            }
                        }
                    }
                }
            }
        }
    }

    .bottom {
        width: 100%;
        // height: 60px;
        position: fixed;
        bottom: 0px;
        left: 0px;
        background: #ffffff;
        display: flex;
        align-items: center;
        padding: 0 12px;
        box-sizing: border-box;
        color: #009a3e;
        font-size: 15px;
        font-weight: bold;
        padding: 10px 0px 15px;
        .button_cancel {
            width: 50%;
            height: 44px;
            text-align: center;
            line-height: 44px;
            cursor: pointer;
        }

        .button_ok {
            background-color: #009a3e;
            color: #ffffff;
            width: 50%;
            height: 44px;
            border-radius: 4px;
            text-align: center;
            box-shadow: 0px -1px 1px 0px rgba(0, 0, 0, 0.09);
            line-height: 44px;
            cursor: pointer;
        }
    }
}