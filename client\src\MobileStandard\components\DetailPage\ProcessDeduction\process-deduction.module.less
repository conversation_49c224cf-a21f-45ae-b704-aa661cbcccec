.steps {
    padding: 10px 0;

    .step_content {
        .line {
            .line_item {
                position: relative;

                .line_item_tail {
                    position: absolute;
                    border-left: 1px solid #ecedef;
                    height: 100%;
                    left: 12px;
                }

                .line_item_head {
                    position: absolute;
                    margin-top: 7px;

                    img {
                        width: 25px;
                        height: 25px;
                    }
                }

                .line_item_content {
                    margin-left: 40px;
                    padding-bottom: 10px;

                    .line_item_content_step {
                        border: 1px solid #ecedef;
                        border-radius: 4px;

                        .line_item_content_stepName {
                            position: relative;
                            height: 40px;
                            font-size: 12px;
                            color: #45575e;
                            line-height: 40px;
                            background: #f0f2f5;
                            padding: 0 12px;
                            border-radius: 4px;
                            display: flex;
                            justify-content: space-between;

                            .editButtons {
                                .edit {
                                    font-size: 12px;
                                    color: #009a3e;
                                    cursor: pointer;
                                }

                                .delete {
                                    font-size: 12px;
                                    color: #009a3e;
                                    cursor: pointer;
                                    margin-left: 5px;
                                }
                            }
                        }

                        .line_item_content_stepName::before {
                            position: absolute;
                            left: -9px;
                            top: 11px;
                            content: '';
                            display: inline-block;
                            width: 15px;
                            height: 15px;
                            background: #f0f2f5;
                            -webkit-transform: rotate(45deg);
                            -ms-transform: rotate(45deg);
                            transform: rotate(45deg);
                            border-left: 1px solid #ecedef;
                            border-bottom: 1px solid #ecedef;
                        }

                        .users {
                            padding: 0 12px;

                            .user {
                                height: 40px;
                                display: flex;
                                align-items: center;

                                .user_name {
                                    font-size: 14px;
                                    color: #333333;
                                    font-weight: bold;
                                }

                                .user_post {
                                    margin-left: 10px;
                                    font-size: 12px;
                                    color: #999999;
                                }
                            }

                            .user:not(:last-child) {
                                border-bottom: 1px solid #ecedef;
                            }
                        }
                    }

                    .empty {
                        border: 1px solid #ffa500;

                        .line_item_content_stepName::before {
                            position: absolute;
                            left: -9px;
                            top: 11px;
                            content: '';
                            display: inline-block;
                            width: 15px;
                            height: 15px;
                            background: #f0f2f5;
                            -webkit-transform: rotate(45deg);
                            -ms-transform: rotate(45deg);
                            transform: rotate(45deg);
                            border-left: 1px solid #ffa500;
                            border-bottom: 1px solid #ffa500;
                        }
                    }

                    .addStep {
                        margin-top: 10px;
                        line-height: 20px;
                        i{
                            font-size: 15px;
                            color: #009a3e;
                            position: absolute;
                            left: 5px;
                        }
                        .addRemark{
                            color: #009a3e;
                        }
                    
                    }
                }
            }
        }
    }

    .step_content:first-child {
        .line_item_tail {
            margin-top: 20px;
        }
    }

    .step_content:last-child {
        .line_item_tail {
            height: 20px !important;
        }
    }
}