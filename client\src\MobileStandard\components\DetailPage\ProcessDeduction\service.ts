import { httpHelper } from '@/MobileStandard/common/utils';
import { authService } from '@/MobileStandard/services/auth';
import { commonService } from '@/MobileStandard/services/common';
import { Observable, of } from 'rxjs';
import { map } from 'rxjs/operators';
import { PreviewStepDto, PreviewStepActionDto } from './types';

class ProcessDeductionService {
  private actions: PreviewStepActionDto[] = [];

  get(
    id: string,
    instanceId: string,
    authId: string,
    data: any,
    btid: string = ''
  ): Observable<{
    editable: boolean;
    steps: PreviewStepDto[];
    branchName: string;
  }> {
    const _url = `/api/engine/v1/process-steps`;
    return httpHelper.post(
      _url,
      data,
      {
        params: {
          'process-id': id,
          'user-id': data.userId ? data.userId : authService.user.id + '',
          'instance-number': instanceId,
          'btid': btid,
          'auth-id': authId,
        },
      },
      { loading: false }
    );
  }

  getDefaultActions(): Observable<PreviewStepActionDto[]> {
    if (this.actions.length > 0) {
      return of(this.actions);
    }

    return commonService.getDictionaries(['bpm-instance-actions']).pipe(
      map((m: any) => {
        this.actions = m.filter((f: any) => f.parameter === 'checked').map((v: any) => ({ name: v.label, code: v.value }));
        return this.actions;
      })
    );
  }

  getFormParams(instanceId: string) {
    return httpHelper.get(`/api/process/v1/instances/${instanceId}/form-params`, undefined, { loading: false });
  }

  /**
   * 获取流程的默认配置数据
   */
   getStepDefaultSetting(): Observable<any> {
    const _url = `/api/process/v1/process-default-setting/step`;
    return httpHelper.post(_url);
  }

  /**
   * 再次发起获取流程推演
   */
   processStepsByInstanceNumber(instanceNumber: string): Observable<any> {
    const _url = `/api/engine/v1/process-steps-by-instance-number?user-id=${authService.user.id}&instance-number=${instanceNumber}`;
    return httpHelper.post(_url);
  }
}

export const processDeductionService = new ProcessDeductionService();
