export interface PreviewStepDto {
  OldUsers: any;
  OldCCUsers: any;
  // test: any[];
  isParallel: boolean;
  // map(item: any);
  id: string;
  name: string;
  sourceNodeId?: string;
  // approvers?: PreviewStepUserDto[];
  approvers: PreviewStepUserDto[];
  // stepResolvers?: PreviewStepResolverDto[] | [];
  stepResolvers: PreviewStepResolverDto[];
  stepCCResolvers: PreviewStepResolverDto[];
  carbonCopyUsers?: PreviewStepUserDto[];
  resolverEditable: boolean;
  nodeEditable: boolean;
  nodeType?: string;
  actions?: PreviewStepActionDto[];
  newNode: boolean;
  policy: PreviewStepPolicy;
  paralleSteps?: any;
  group: any;  // 增加批次
  formEditable?: boolean;
  batchApproval?: boolean;
  nodeMaxApprovalCount?: number;
  nodeMaxConsultCount?: number;
  nodeMaxHandoverCount?: number;
  timeoutAutoApproval?: boolean;
  autoApprovalTime?: number;
  delayTime?: number;
  warningLight?: number;
}

export interface PreviewStepPolicy {
  participatingType: number;
  participatingExpression?: string;
  concurrentType: number;
  concurrentExpression?: string;
  sameStartApproverType: number;
  sameApproverType: number;
  sameApproverExpression?: string;
  emptyApproverType: string;
  emptyApproverExpression?: string;
  limitApprover: number;
  datum?: string;
}

export interface PreviewStepUserDto {
  userId: string;
  userName: string;
  resolverId?: string;
  resolverName?: string;
  activityResolverId?: string;
  fullPathPostion?: string;
}

export interface PreviewStepResolverDto {
  roleName: string;
  resolverId: string;
  resolverName: string;
  resolverType: number;
  activityResolverId: string;
  users: any[];
  resolverEditable: boolean;
}

export interface PreviewStepActionDto {
  code: string;
  name: string;
}

export interface PreviewDto {
  editable: boolean;
  steps: PreviewStepDto[];
  cacheData?: any;
  keyParameter?: string[];
}

export interface PreviewStepChangeDto {
  type: StepChangeType;
  id: string;
  name: string;
  sourceNodeId?: string;
  sourceNodeName?: string;
  users: PreviewStepUserDto[];
  ccUsers?: PreviewStepUserDto[];
  actions?: PreviewStepActionDto[];
  group: any;
  OldUsers?: PreviewStepUserDto[];
  OldCCUsers?: PreviewStepUserDto[];
  policy?: PreviewStepPolicy;
  batchApproval?: boolean;
  formEditable?: boolean;
  order?: number;
  nodeMaxApprovalCount?: number;
  nodeMaxConsultCount?: number;
  nodeMaxHandoverCount?: number;
  timeoutAutoApproval?: boolean;
  autoApprovalTime?: number;
  delayTime?: number;
  warningLight?: number; // 红黄绿灯设置
}

export enum StepChangeType {
  add,
  delete,
  update,
  normal
}
