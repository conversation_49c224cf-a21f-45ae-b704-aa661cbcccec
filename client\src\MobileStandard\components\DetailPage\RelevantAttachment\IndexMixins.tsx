import { Settings } from '@/MobileStandard/common/defines';
import { guid<PERSON><PERSON><PERSON>, link<PERSON><PERSON>per, subjectHelper } from '@/MobileStandard/common/utils';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import lodash from 'lodash';
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { relevantAttachmentService } from './service';
import { AttachmentDto } from './types';

@Component
export class IndexMixins extends Vue {
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: true }) editable!: boolean;
  @Prop() urlParamteres!: UrlParametersDto;
  fileList: any = [];
  cacheFiles = '';
  getSuffix(name: string) {
    return name.slice(name.lastIndexOf('.') + 1).toLowerCase();
  }
  beforeRead(e: any) {
    let result = true;
    if (Array.isArray(e)) {
      e.map((ee: any) => {
        if (!this.suffixIncludes(ee)) {
          result = false;
        }
      });
    } else {
      if (!this.suffixIncludes(e)) {
        result = false;
      }
    }
    if (!result) {
      this.$toast({ type: 'fail', overlay: true, forbidClick: true, message: '上传附件格式错误' });
    }
    if (result) {
      if (Array.isArray(e)) {
        e.map((ee: any) => {
          if (ee.size > (Settings.UploadFileSize * 1024 * 1024)) {
            result = false;
          }
        });
      } else {
        if (e.size > (Settings.UploadFileSize * 1024 * 1024)) {
          result = false;
        }
      }
      if (!result) {
        this.$toast({ type: 'fail', overlay: true, forbidClick: true, message: `上传附件大小不能超过${Settings.UploadFileSize}MB` });
      }
    }

    return result;
  }
  suffixIncludes(e: any) {
    const suffix = this.getSuffix(e.name);
    if (!Settings.UploadFileAcceptSufixes.includes(suffix)) {
      return false;
    }
    return true;
  }
  afterRead(e: any) {
    if (Array.isArray(e)) {
      e.forEach((f: any) => {
        f.file['id'] = guidHelper.generate();
        f.status = 'uploading';
        f.message = this.$l.getLocale('tips.uploading');
        this.upload(f);
      });
    } else {
      e.file['id'] = guidHelper.generate();
      e.status = 'uploading';
      e.message = this.$l.getLocale('tips.uploading');
      this.upload(e);
    }
  }
  upload(e: any) {
    const formData = new FormData();
    formData.append('file', e.file);
    relevantAttachmentService.upload(formData).subscribe((data: any) => {
      if (data && data[0]) {
        e.message = '';
        e.status = '';
        this.fileList.push({
          id: data[0].documentId,
          name: e.file.name,
          uploadTime: data[0].uploadTime,
          uploadUserName: data[0].uploadUserName,
          size: data[0].size,
          operationType: 1,
          isNew: true
        });
      } else {
        e.status = 'failed';
        e.message = this.$l.getLocale('tips.uploadFail');
      }
    });
  }
  onDeleteFile(id: string) {
    const deletefile = this.fileList.find((x: any) => x.id === id);
    if (deletefile != null) {
      deletefile.operationType = -1;
      this.$forceUpdate();
    }
  }
  onPreview(item: any) {
    relevantAttachmentService.previewDocument(item.id, item.name).subscribe((res: any) => {
      let url = '';
      if (res === 'wpsurl') {
        url = `/file-preview/${item.id}`;
      } else {
        url = '/' + lodash.trimStart(lodash.trimStart(res, '//'), '/');
      }
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = window.location.href.replace(this.$route.fullPath, url);
      console.log(link.href);
      link.setAttribute('dowload', item.name);
      document.body.appendChild(link);
      link.click();
    });
  }
  getFileIcon(item: any) {
    let name = '';
    const extension = item.name.split('.').pop();
    switch (extension) {
      case 'pdf':
        name = 'pdf';
        break;
      case 'jpg':
        name = 'jpg';
        break;
      case 'png':
        name = 'png';
        break;
      case 'bmp':
        name = 'bmp';
        break;
      case 'doc':
      case 'docx':
        name = 'world';
        break;
      case 'xls':
      case 'xlsx':
        name = 'excel';
        break;
      case 'ppt':
        name = 'ppt';
        break;
      case 'text':
      case 'txt':
        name = 'txt';
        break;
      case 'rar':
      case 'zip':
      case 'arj':
        name = 'yasuobao';
        break;
      default:
        name = 'yasuobao';
        break;
    }
    return require(`../../../../assets/images/${name}.png`);
  }
  getFileSize(size: any) {
    if (size === 0) {
      return '0B';
    }
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(size) / Math.log(k));
    return (size / Math.pow(k, i)).toPrecision(3) + sizes[i];
  }
  public getValues(): AttachmentDto[] {
    return JSON.stringify(this.fileList) !== this.cacheFiles ? this.fileList : [];
  }
  created() {
    // 草稿打开从草稿获取办理意见
    if (this.urlParamteres.pageType === 'start' && this.urlParamteres.draftId &&
      !this.urlParamteres.number && !this.urlParamteres.againStartNumber) {
      subjectHelper.getSubject('draftInfo$').subscribe((s: any) => {
        this.fileList = s.data.instanceAttachments || [];
      });
    }
  }
  mounted() {
    if (this.urlParamteres.number || this.urlParamteres.againStartNumber) {
      const number = this.urlParamteres.number || this.urlParamteres.againStartNumber;
      relevantAttachmentService.get(number || '').subscribe(data => {
        const newData = (data || []).map((m: any, i: number) => {
          return {
            ...m,
            isNew: true,
            operationType: this.urlParamteres.pageType === 'start' &&
              this.urlParamteres.againStartNumber && !this.urlParamteres.number ? 1 : m.operationType
          };
        });
        this.fileList = [...newData];
        this.cacheFiles = JSON.stringify(this.fileList);
      });
    }
  }
}
