import { Component } from 'vue-property-decorator';
import styles from './relevant-attachment.module.less';
import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import lodash from 'lodash';
import { dateHelper } from '@/MobileStandard/common/utils/date-helper';
import { authService } from '@/MobileStandard/services/auth';
import { Settings } from '@/MobileStandard/common/defines';

@Component
export class RelevantAttachment extends mixins(IndexMixins) {
  render() {
    return this.visible ?
      <instance-collapse-item title={this.$l.getLocale('fields.attachmentInfo')}>
        {
          lodash.map(lodash.filter(this.fileList, (ff: any) => ff.operationType !== -1), (f: any) => {
            return <div class={styles.attachment}>
              <div class={styles.icon}>
                <van-icon
                  name={this.getFileIcon(f)}
                  size='38'
                />
              </div>
              <div class={styles.content}>
                <span class={styles.name} on-click={() => this.onPreview(f)}>{f.name}</span>
                <div class={styles.second_row}>
                  <span class={styles.user_name}>{f.uploadUserName + ' | ' + dateHelper.dateFormat(f.uploadTime)}</span>
                  {/* <span class={styles.file_size}>{this.getFileSize(f.size)}</span> */}
                </div>
              </div>
              {
                !this.editable || (f.uploadUserId && f.uploadUserId !== authService.user.id) ? '' :
                  <div class={styles.buttons}>
                    <div class={styles.delete}
                      on-click={() => this.onDeleteFile(f.id)}>
                      <i class='iconfont icon-btn_deletelist'
                        style='color:#666666;font-size:13px;'></i>
                    </div>
                  </div>
              }
            </div>;
          })
        }
        {
          this.editable ?
            <div class={styles.upload_area}>
              <van-uploader
                multiple
                accept=''
                preview-full-image={false}
                before-read={(e: any) => this.beforeRead(e)}
                after-read={(e: any) => this.afterRead(e)}
                class={styles.uploader}
              >
                <img src={require(`../../../../assets/images/btn_addpic.png`)} />
              </van-uploader>
            </div> : ''
        }
      </instance-collapse-item> : '';
  }
}
