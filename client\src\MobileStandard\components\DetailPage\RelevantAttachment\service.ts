import { httpHelper } from '@/MobileStandard/common/utils';
import { Observable } from 'rxjs';

class RelevantAttachmentService {
  upload(formData: any) {
    const _url = `/api/process/v1/documents/upload`;
    return httpHelper.post(_url, formData);
  }
  get(number: string): Observable<any> {
    const _url = `/api/process/v1/instances/${number}/attachments`;
    return httpHelper.get(_url);
  }
  downDocument(id: string, name: string): Observable<any> {
    const _url = `/api/process/v1/documents/download/${id}`;
    return httpHelper.get(_url, {}, { loading: false });
  }
  previewDocument(id: string, name: string): Observable<any> {
    const _url = `/api/process/v1/documents/preview/${id}`;
    return httpHelper.get(_url, {}, { loading: false });
  }
}

export const relevantAttachmentService = new RelevantAttachmentService();
