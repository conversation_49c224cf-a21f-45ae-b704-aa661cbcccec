import { subjectHelper, toCasedStyleObject } from '@/MobileStandard/common/utils';
import { dateHelper } from '@/MobileStandard/common/utils/date-helper';
import { removeWatermark, setWaterMark } from '@/MobileStandard/common/waterMarks';
import { authService } from '@/MobileStandard/services/auth';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import lodash from 'lodash';
import { Component, Prop, Vue } from 'vue-property-decorator';
import { relevantProcessService } from './service';
import { RelationInstanceDto } from './types';
@Component
export class IndexMixins extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop({ default: true }) editable!: boolean;
    @Prop() urlParamteres!: UrlParametersDto;
    realtionInstanceVisible = false;
    searchStr = '';
    searchLb = '1';
    searchFqsj = [dateHelper.dateFormat(new Date(new Date().setFullYear((new Date()).getFullYear() - 1))),
    dateHelper.dateFormat(new Date())];
    filterVisible = false;
    selectRelationInstance: any = [];
    data: any = [];
    dataCount = 0;
    paging: any = {
        pageSize: 10,
        pageIndex: 1
    };
    refreshing = false;
    loading = false;
    finished = false;
    fqshVisible = false;
    currentDate: any = null;
    currentDateLx: any = '';
    minDate = new Date(dateHelper.momentAdd(dateHelper.currentTime, -5, 'years', 'YYYY/MM/DD'));
    maxDate = new Date(dateHelper.momentAdd(dateHelper.currentTime, 5, 'years', 'YYYY/MM/DD'));
    rInstances: any = [];
    instanceVisible = false;
    instanceUrl = '';
    onRealtionInstanceShow() {
        this.realtionInstanceVisible = true;
        this.searchStr = '';
        this.searchLb = '1';
        this.currentDateLx = '';
        this.search();
    }
    onRealtionInstanceClose() {
        this.realtionInstanceVisible = false;
    }
    onFilter() {
        this.filterVisible = !this.filterVisible;
    }
    search() {
        this.paging.pageIndex = 1;
        this.data = [];
        this.dataCount = 0;
        this.loading = true;
        this.finished = false;
        this.selectRelationInstance = lodash.map(this.rInstances, (m: any) => m.id);
        this.onLoadData();
    }
    onRelationOk() {
        // debugger
        if (this.selectRelationInstance && this.selectRelationInstance.length > 0) {
            lodash.forEach(this.selectRelationInstance, (s: any) => {
                const instance = this.rInstances.find((x: any) => x.id === s);
                // console.log(this.rInstances);
                // console.log(instance);
                // console.log(this.data);
                if (!instance || instance.operationType === -1) {
                    const sourceInstance = lodash.find(this.data, (d: any) => d.id === s);
                    if (sourceInstance && (sourceInstance.operationType === -1 || sourceInstance.operationType === undefined)) {
                        this.rInstances.push({ ...sourceInstance, isNew: true });
                    }
                }
            });
        }
        this.realtionInstanceVisible = !this.realtionInstanceVisible;
    }
    onRelationCancel() {
        this.realtionInstanceVisible = !this.realtionInstanceVisible;
    }
    onRefresh() {
        this.paging.pageIndex = 1;
        this.loading = true;
        this.finished = false;
        this.onLoadData();
    }
    onListLoading() {
        if (this.data.length > 0) {
            this.paging.pageIndex += 1;
        }
        this.onLoadData();
    }
    onLoadData() {
        const params = {
            'page-size': this.paging.pageSize,
            'page-index': this.paging.pageIndex,
            'kw': this.searchStr,
            'start-date': this.searchFqsj[0],
            'end-date': this.searchFqsj[1],
            'type': this.searchLb
        };
        relevantProcessService.getRelationInstances(toCasedStyleObject(params)).subscribe(data => {
            if (this.refreshing) {
                this.data = [];
                this.dataCount = 0;
                this.refreshing = false;
            }
            this.loading = false;
            this.data.push(...(data.items || []));
            this.$forceUpdate();
            this.dataCount = data.total;
            if (this.data.length === 0 || this.data.length >= data.total) {
                this.finished = true;
            }
        });
    }
    onInstanceChecked(checked: boolean, id: any) {
        if (checked && !lodash.includes(this.selectRelationInstance, id)) {
            this.selectRelationInstance.push(id);
        } else if (!checked && lodash.includes(this.selectRelationInstance, id)) {
            const index = lodash.findIndex(this.selectRelationInstance, (d: any) => d === id);
            this.selectRelationInstance.splice(index, 1);
        }
    }
    onLbChange(lb: any) {
        this.searchLb = this.searchLb === lb ? '' : lb;
    }
    onFqsjClick(lb: any) {
        this.currentDateLx = lb;
        if (lb === 'kssj' && this.searchFqsj[0]) {
            this.currentDate = new Date(dateHelper.dateFormat(this.searchFqsj[0], 'YYYY/MM/DD'));
        } else if (lb === 'jssj' && this.searchFqsj[1]) {
            this.currentDate = new Date(dateHelper.dateFormat(this.searchFqsj[1], 'YYYY/MM/DD'));
        } else {
            this.currentDate = new Date();
        }
        this.fqshVisible = true;
    }
    onFqsjConfigm(value: any) {
        const date = dateHelper.dateFormat(value);
        if (this.currentDateLx === 'kssj') {
            this.searchFqsj[0] = date;
        } else if (this.currentDateLx === 'jssj') {
            this.searchFqsj[1] = date;
        }
        this.fqshVisible = false;
    }
    onDeleteRelation(id: string) {
        const instance = this.rInstances.find((x: any) => x.id === id);
        // console.log(instance);
        if (instance != null) {
            instance.operationType = -1;
            this.rInstances.splice(0, 1);
            this.$forceUpdate();
        }
        // console.log(this.rInstances);
        // debugger
    }
    onInstanceShow(f: any) {
        if (f.dataSorce === 'BPM') {
            const symbol = f.mobileUrl.indexOf('?') > -1 ? '&' : '?';
            this.instanceUrl = `${f.mobileUrl}${symbol}movitech_token=${f.token}`;
            removeWatermark();
            this.instanceVisible = true;
        } else if (f.mobileUrl) {
            window.location.href = f.mobileUrl;
        }
    }
    onInstanceClose() {
        this.instanceVisible = false;
        setTimeout(() => {
            if (authService.user.name && authService.user.account) {
                setWaterMark((authService.user.name + ':' + authService.user.account), dateHelper.dateFormat(authService.user.currentTime));
            }
        });
    }
    public getValues(): RelationInstanceDto[] {
        return lodash.filter(this.rInstances, (ff: any) => ff.operationType !== -1);
    }
    mounted() {
        if (this.urlParamteres.number || this.urlParamteres.againStartNumber) {
            const number = this.urlParamteres.number || this.urlParamteres.againStartNumber;
            relevantProcessService.get(number || '').subscribe(data => {
                this.rInstances = data || [];
            });
        }
    }
    created() {
        // 草稿打开从草稿获取办理意见
        if (this.urlParamteres.pageType === 'start' && this.urlParamteres.draftId &&
            !this.urlParamteres.number && !this.urlParamteres.againStartNumber) {
            subjectHelper.getSubject('draftInfo$').subscribe((s: any) => {
                this.rInstances = s.data.relationInstances || [];
            });
        }
    }
}
