import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import { Component } from 'vue-property-decorator';
import styles from './relevant-process.module.less';
import lodash from 'lodash';
import { dateHelper } from '@/MobileStandard/common/utils/date-helper';

@Component
export class RelevantProcess extends mixins(IndexMixins) {
  render() {
    return this.visible ?
      <instance-collapse-item title={this.$l.getLocale('fields.relatedProcesses')}>
        {
          lodash.map(lodash.filter(this.rInstances, (ff: any) => ff.operationType !== -1), (f: any) => {
            return <div class={styles.instances}>
              <div class={styles.icon}>
                <van-icon
                  name={require(`../../../../assets/images/xglc.png`)}
                  size='38'
                />
              </div>
              <div class={styles.content}>
                <span class={styles.name}
                  on-click={() => this.onInstanceShow(f)}
                >{f.topic}</span>
                <div class={styles.second_row}>
                  <span class={styles.user_name}>{f.userName + ' | ' + dateHelper.dateFormat(f.startTime)}</span>
                  <span class={styles.file_size} style='word-break:break-all'>{`${this.$l.getLocale('fields.number')}:` + f.number}</span>
                </div>
              </div>
              {
                this.urlParamteres.pageType === 'start' ?
                  <div class={styles.buttons}>
                    <div class={styles.delete}
                      on-click={() => this.onDeleteRelation(f.id)}>
                      <i class='iconfont icon-btn_deletelist'
                        style='color:#666666;font-size:13px;'></i>
                    </div>
                  </div> : ''
              }
            </div>;
          })
        }
        {
          this.editable ?
            [
              <div class={styles.select_area_wrapper}>
                <img src={require(`../../../../assets/images/btn_addpic.png`)}
                  on-click={() => this.onRealtionInstanceShow()} />
              </div>,
              <van-action-sheet v-model={this.realtionInstanceVisible}
                round={false}
                on-click-overlay={this.onRealtionInstanceClose}
                class={styles.relation_action_sheet}
              >
                <div class={styles.tool_bar}>
                  <form action='/'
                    class={styles.form}>
                    <van-search
                      v-model={this.searchStr}
                      placeholder={this.$l.getLocale('placeholders.instanceRelationSearch')}
                      background='#ffffff'
                      shape='round'
                      clearable
                      on-search={() => this.search()}
                      on-clear={() => this.search()}
                    />
                  </form>
                  <div class={styles.filter + (this.filterVisible ? ' ' + styles.active : '')}
                    on-click={() => this.onFilter()}>
                    <van-icon name='filter-o' color='#607384'></van-icon>
                    <span>{this.$l.getLocale('buttons.filter')}</span>
                  </div>
                  <van-overlay show={this.filterVisible} class={styles.filterOverlay}>
                    <div class={styles.search_expand}>
                      <span class={styles.search_label}>{this.$l.getLocale('fields.category')}</span>
                      <div class={styles.serach_lb}>
                        <div class={this.searchLb === '1' ? styles.active : ''}
                          on-click={() => this.onLbChange('1')}>{this.$l.getLocale('fields.myStart')}</div>
                        <div class={this.searchLb === '2' ? styles.active : ''}
                          on-click={() => this.onLbChange('2')}>{this.$l.getLocale('fields.ccToMe')}</div>
                        <div class={this.searchLb === '3' ? styles.active : ''}
                          on-click={() => this.onLbChange('3')}>{this.$l.getLocale('fields.myApproval')}</div>
                      </div>
                      <span class={styles.search_label}>申请时间</span>
                      <div class={styles.serach_fqsj}>
                        <div class={styles.fqsj}
                          on-click={() => this.onFqsjClick('kssj')}>
                          {
                            this.searchFqsj[0]
                          }
                        </div>
                        ~
                        <div class={styles.fqsj}
                          on-click={() => this.onFqsjClick('jssj')}>
                          {
                            this.searchFqsj[1]
                          }
                        </div>
                      </div>
                      <van-popup v-model={this.fqshVisible}
                        position='bottom'>
                        <van-datetime-picker
                          v-model={this.currentDate}
                          type='date'
                          title={this.$l.getLocale('fields.startTime')}
                          min-date={this.minDate}
                          max-date={this.maxDate}
                          on-confirm={this.onFqsjConfigm}
                          on-cancel={() => this.fqshVisible = false}
                        />
                      </van-popup>
                    </div>
                    <div class={styles.buttons}>
                      <div class={styles.cancel} on-click={() => this.filterVisible = false}>
                        {this.$l.getLocale('buttons.cancel')}
                      </div>
                      <div class={styles.confim} on-click={() => { this.filterVisible = false; this.search(); }}>
                        {this.$l.getLocale('buttons.confirm')}
                      </div>
                    </div>
                  </van-overlay>
                </div>
                <div class={styles.content}>
                  <div class={styles.tool_info}>
                    {
                      this.$l.getLocale('fields.t') + ' '
                    }
                    <span class={styles.relation_count}>{this.dataCount}</span>
                    {
                      ' ' + this.$l.getLocale('fields.g') + this.$l.getLocale('fields.relatedProcesses')
                    }
                  </div>
                  <div class={styles.instance_content}>
                    <van-pull-refresh
                      v-model={this.refreshing}
                      on-refresh={this.onRefresh}
                    >
                      <van-list
                        v-model={this.loading}
                        finished={this.finished}
                        finished-text={this.$l.getLocale('fields.noMore')}
                        offset={0}
                        immediate-check={false}
                        on-load={this.onListLoading}
                      >
                        {
                          lodash.map(this.data, (d: any) => {
                            return <relation-instance
                              instance={d}
                              defaultChecked={lodash.includes(this.selectRelationInstance, d.id)}
                              on-change={(checked: boolean, id: any) =>
                                this.onInstanceChecked(checked, id)}
                            />;
                          })
                        }
                      </van-list>
                    </van-pull-refresh>
                  </div>
                </div>
                <div class={styles.bottom}>
                  <span class={styles.font}>
                    {this.$l.getLocale('fields.selected2')}
                    {' ' + this.selectRelationInstance.length + ' '}
                    {this.$l.getLocale('fields.g')}
                  </span>
                  <div class={styles.button_cancel}
                    on-click={() => this.onRelationCancel()}>{this.$l.getLocale('buttons.cancel')}</div>
                  <div class={styles.button_ok}
                    on-click={() => this.onRelationOk()}>{this.$l.getLocale('buttons.ok')}</div>
                </div>
              </van-action-sheet>
            ] : ''
        }
        {
          this.instanceVisible ?
            <van-action-sheet v-model={this.instanceVisible}
              round={false}
              on-click-overlay={this.onInstanceClose}
              class={styles.instance_view_sheet}
            >
              <div class={styles.top}>
                <van-icon name='arrow-down' size='20'
                  class={styles.closeIcon}
                  on-click={this.onInstanceClose}
                ></van-icon>
                <span>关联流程</span>
              </div>
              <div class={styles.content}>
                <iframe src={this.instanceUrl}
                  style='width:100%;height:100%;border:0;' />
              </div>
            </van-action-sheet> : ''
        }

      </instance-collapse-item> : '';
  }
}
