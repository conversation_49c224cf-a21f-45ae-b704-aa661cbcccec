.instances {
    margin-top: 10px;
    display: flex;
    .content {
        flex: 1;

        .name {
            font-size: 15px;
            word-break: break-all;
            white-space: break-spaces;
            color: #333333;
            cursor: pointer;
        }

        .user_name,
        .file_size {
            width: 50%;
            display: inline-block;
            font-size: 12px;
            color: #999999;
        }
    }

    .buttons {
        margin-left: 8px;

        .delete {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #ffffff;
            border: 1px solid #cccccc;
            text-align: center;
            line-height: 20px;
        }
    }
}

.select_area_wrapper {
    margin: 10px 0;
    display: inline-block;

    img {
        width: 50px;
        height: 50px;
    }
}

.relation_action_sheet {
    height: 100%;
    max-height: 100% !important;

    :global(.van-action-sheet__content) {
        background: #f0f2f5;
    }

    .tool_bar {
        width: 100%;
        // height: 60px;
        display: flex;
        align-items: center;
        background: #ffffff;
        // padding: 0 12px;
        box-sizing: border-box;
        padding: 10px 12px 15px;
        .form {
            flex: 1;
            margin-right: 5px;

            :global(.van-search__content) {
                background: #f5f5f5;

                :global(.van-icon-search) {
                    font-size: 12px;
                }

                :global(.van-field__control) {
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400px;
                }
            }
        }

        .filter {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            cursor: pointer;
        }

        .active {

            i,
            span {
                color: #009a3e !important;
            }
        }

        .filterOverlay {
            top: 60px;

            .search_expand {
                background: #ffffff;
                padding: 0 12px;

                .search_label {
                    font-size: 16px;
                    font-weight: bold;
                    height: 18px;
                    line-height: 18px;
                    display: block;
                }

                .serach_lb {
                    display: flex;
                    justify-content: space-between;
                    padding: 10px 0;

                    div {
                        width: 30%;
                        background: #f9fafa;
                        height: 36px;
                        border-radius: 4px;
                        line-height: 36px;
                        text-align: center;
                        cursor: pointer;
                        font-size: 15px;
                    }

                    .active {
                        // background: rgba(14, 146, 102, 0.08);
                        background: #e0e2e5;
                        color: #009a3e;
                        font-weight: bold;
                    }
                }

                .serach_fqsj {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 10px 0;

                    .fqsj {
                        width: 40%;
                        height: 30px;
                        font-size: 15px;
                        text-align: center;
                        line-height: 30px;
                        border: 1px solid #e6e6e6;
                        border-radius: 4px;
                        cursor: pointer;
                    }
                }
            }

            .buttons {
                height: 50px;
                line-height: 50px;
                background: #ffffff;

                .cancel {
                    width: 50%;
                    color: #009a3e;
                    font-size: 15px;
                    font-weight: bold;
                    display: inline-block;
                    text-align: center;
                    box-shadow: 0px 1px 0px 0px #e6e6e6 inset;
                }

                .confim {
                    width: 50%;
                    background: #009a3e;
                    color: #ffffff;
                    font-size: 15px;
                    font-weight: bold;
                    box-shadow: 0px -2px 4px 0px rgba(0, 0, 0, 0.09);
                    display: inline-block;
                    text-align: center;
                }
            }
        }
    }

    .content {
        width: 100%;
        height: calc(~'100% - 150px');
        overflow: auto;
        padding: 0 12px;
        box-sizing: border-box;

        .tool_info {
            width: 100%;
            height: 40px;
            font-size: 12px;
            color: #999999;
            line-height: 40px;

            .relation_count {
                color: #009a3e;
            }
        }
    }

    .bottom {
        width: 100%;
        height: 60px;
        position: fixed;
        bottom: 0px;
        left: 0px;
        background: #ffffff;
        display: flex;
        align-items: center;

        .font {
            margin-left: 12px;
            flex: 1;
            color: #009a3e;
            font-size: 15px;
            font-weight: bold;
        }

        .button_cancel {
            margin-right: 10px;
            background-color: #ffffff;
            color: #009a3e;
            width: 100px;
            height: 44px;
            border-radius: 4px;
            text-align: center;
            box-shadow: 0px 0px 1px 1px rgba(0, 0, 0, 0.09);
            line-height: 44px;
        }

        .button_ok {
            margin-right: 12px;
            background-color: #009a3e;
            color: #ffffff;
            width: 100px;
            height: 44px;
            border-radius: 4px;
            text-align: center;
            box-shadow: 0px -1px 1px 0px rgba(0, 0, 0, 0.09);
            line-height: 44px;
        }
    }
}

.instances:last-child {
    padding-bottom: 10px;
}

.instance_view_sheet {
    height: 100%;
    max-height: 100% !important;

    :global(.van-action-sheet__content) {
        background: #f0f2f5;
    }

    .top {
        width: 100%;
        background-color: #ffffff;
        text-align: center;
        vertical-align: middle;
        line-height: 45px;
        height: 45px;
        color: #333333;

        .closeIcon {
            position: absolute !important;
            left: 20px;
            top: 10px;
        }
    }

    .content {
        position: absolute;
        top: 45px;
        bottom: 0px;
        width: 100%;
        overflow: auto;
    }
}