import { httpHelper } from '@/MobileStandard/common/utils';
import { Observable } from 'rxjs';

class RelevantProcessService {
  get(number: string): Observable<any[]> {
    const _url = `/api/process/v1/instances/${number}/relations`;
    return httpHelper.get(_url);
}
getRelationInstances(query: any): Observable<{ total: number; items: any }> {
    const _url = `/api/process/v1/relative-instances`;
    return httpHelper.get(_url, { params: query });
}
}

export const relevantProcessService = new RelevantProcessService();
