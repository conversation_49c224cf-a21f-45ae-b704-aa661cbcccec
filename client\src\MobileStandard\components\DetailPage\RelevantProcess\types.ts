export interface RelationInstanceDto {
  id: number;
  number: string;
  userName: string;
  topic: string;
  startDate: string;
  organization: string;
  activityName?: string;
  addUserId?: string;
  addUserName?: string;
  addTime?: string;
  isNew?: boolean;
  dataSorce: string;
  pcUrl: string;
  mobileUrl: string;
}

export class RelationInstanceSearchDto {
  constructor(
    public number?: string,
    public topic?: string,
    public organizationId?: number,
    public organizationPath?: string,
    public categoryId?: string,
    public processId?: string,
    public startDate?: string,
    public endDate?: string,
    public userId?: string,
    public type?: string
  ) {}
}
