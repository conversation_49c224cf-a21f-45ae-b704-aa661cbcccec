import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import { webOfficeService } from './service';
import OpenSDK from '../../../../assets/webOffice/open-jssdk-v0.0.6.es.js';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import { encryptHelper } from '@/MobileStandard/common/utils/encrypt-helper';
import { authService } from '@/MobileStandard/services/auth';
@Component
export class IndexMixins extends Vue {
    @Prop() fileId!: string;
    @Prop() fileType!: string;
    @Prop() fileInfo!: string;
    @Prop() visible!: boolean;
    @Prop() hasButton!: boolean;
    @Prop() urlParamteres!: UrlParametersDto;
    private instance: any = null;
    @Watch('visible')
    visibleChange(newVal: any, oldVal: any) {
        this.lodashWebOffice();
    }
    @Watch('hasButton')
    hasButtonChange(newVal: any, oldVal: any) {
        this.officeDestroy();
        this.lodashWebOffice();
    }
    @Emit('rename')
    renameChange(rename: string) { }
    lodashWebOffice() {
        const newFileType = this.fileType ? this.fileType : 'gongwen';
        const pageType = (this.urlParamteres.pageType === 'start' ||
            this.urlParamteres.pageType === 'todo') && this.hasButton ?
            this.urlParamteres.pageType : 'approval';
        if (this.visible && this.fileId && !this.instance) {
            webOfficeService.getDocPreviewUrl(this.fileId,
                pageType,
                newFileType,
                this.fileInfo,
                this.urlParamteres.taskId || '').subscribe(link => {
                if (link) {
                    this.initOffice(link, pageType);
                }
            });
        }
    }
    private initOffice(link: string, pageType: string) {
        const refreshToken = () => {
            return Promise.resolve({
                token: encryptHelper.encryptByEnAES(authService.user.account || ''),
                timeout: 10 * 60 * 1000,
            });
        };
        this.instance = OpenSDK.config({
            url: link,
            mount: document.querySelector('#officeIframe'),
            refreshToken: refreshToken
        });
        this.instance.setToken({
            token: encryptHelper.encryptByEnAES(authService.user.account || ''),
            timeout: 10 * 60 * 1000
        });
        this.instance.ApiEvent.AddApiEventListener('fileNameChange', (data: any) => {
            this.renameChange(data.fileName);
        });
        this.example(pageType);
    }
    private async example(pageType: any) {
        await this.instance.ready();
        // 审批页面打开的为最终状态
        const app = this.instance.Application;
        if (pageType === 'approval') {
            const View = await app.ActiveDocument.ActiveWindow.View;
            View.RevisionsFilter.View = 0;
            View.ShowRevisionsAndComments = false;
        }

        // 设置为分页模式
        await app.ActiveDocument.SwitchTypoMode(false);
    }
    public officeDestroy() {
        if (this.instance) {
            const div = document.getElementById('officeIframe');
            if (div && div.firstChild) {
                div.removeChild(div.firstChild);
            }
            this.instance.destroy();
            this.instance = null;
        }
    }
    created() {
        this.officeDestroy();
        this.lodashWebOffice();
    }
    mounted() {
    }
}
