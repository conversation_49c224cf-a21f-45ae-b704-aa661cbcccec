import { httpHelper } from '@/MobileStandard/common/utils';

class WebOfficeService {
  getDocPreviewUrl(fileId: string, pageType: string, fileType: string, fileInfo: string, taskId: string) {
    const _url = `/api/process/v1/webOffice/link/${fileId}?pageType=${pageType}&fileType=${fileType}&fileInfo=${fileInfo ? encodeURIComponent(fileInfo) : fileInfo}&taskId=${taskId}`;
    return httpHelper.get(_url, undefined, { loading: false });
  }
}

export const webOfficeService = new WebOfficeService();
