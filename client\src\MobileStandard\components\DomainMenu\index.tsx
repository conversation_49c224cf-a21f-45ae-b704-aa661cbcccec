import { Component, Vue, Emit, Prop, Model } from 'vue-property-decorator';
import styles from './domain-menu.module.less';

@Component
export class DomainMenu extends Vue {
    @Prop() value!: string;
    @Prop() options!: any[];
    private popupShow = false;
    @Emit('change')
    valueChange(value: string) { }
    private get text() {
        if (this.value) {
            const opt = (this.options || []).find((f: any) => f.code === this.value);
            return opt ? opt.title : '';
        }
        return '';
    }
    private get textIndex() {
        if (this.value) {
            const optIndex = (this.options || []).findIndex((f: any) => f.code === this.value);
            return optIndex > -1 ? optIndex : 0;
        }
        return '';
    }
    private get columns() {
        return (this.options || []).map((m: any) => {
            return {
                text: m.title
            };
        });
    }
    private onClick() {
        this.popupShow = true;
    }
    private onConfirm(value: any, index: number) {
        this.valueChange(this.options[index].code);
        this.popupShow = false;
    }
    created() {
    }
    render() {
        return (
            <div class={'van-search ' + styles.menu}>
                <div class='van-search__content van-search__content--round'
                    on-click={() => this.onClick()}>
                    <div class='van-cell van-cell--borderless van-field'>
                        <div class='van-field__left-icon'>
                            <i class='van-icon van-icon-location-o'></i>
                        </div>
                        <div class='van-cell__value van-cell__value--alone van-field__value'>
                            <div class={'van-field__body ' + styles.font}>
                                {
                                    this.text
                                }
                            </div>
                        </div>
                    </div>
                </div>
                <van-popup v-model={this.popupShow}
                    position='bottom'
                    round>
                    <van-picker
                        show-toolbar
                        columns={this.columns}
                        default-index={this.textIndex}
                        on-confirm={this.onConfirm}
                        on-cancel={() => this.popupShow = false}
                    />
                </van-popup>
            </div>
        );
    }
}
