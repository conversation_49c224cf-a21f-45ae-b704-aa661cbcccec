import { Component, Emit, Prop, Vue } from 'vue-property-decorator';

@Component
export class IndexMixins extends Vue {
    @Prop() title!: string;
    @Prop() extendTitle!: string;
    @Prop({ default: true }) expand!: boolean;
    @Prop({ default: true }) emptyDomHide!: boolean;
    @Prop({ default: false }) cardModel!: boolean;
    isOpen = true;
    created() {
        this.isOpen = this.expand;
    }
}
