import { mixins } from 'vue-class-component';
import { Component } from 'vue-property-decorator';
import styles from './instance-collapse-item.module.less';
import { IndexMixins } from './IndexMixins';

@Component
export class InstanceCollapseItem extends mixins(IndexMixins) {
    render() {
        return (this.$slots.default || (!this.emptyDomHide && !this.$slots.default) ?
            <div class={styles.group}>
                <div class={styles.title_content} on-click={() => this.isOpen = this.cardModel ?
                    this.isOpen : !this.isOpen}>
                    <div class={styles.title}>
                        <span class={styles.base_title}>
                            {this.title}
                        </span>
                        {
                            this.extendTitle ? <span class={styles.extend_title}>{`(${this.extendTitle})`}</span> : ''
                        }
                    </div>
                    {
                        this.cardModel ? '' :
                            <van-icon name={this.isOpen ? 'arrow-up' : 'arrow-down'}
                                color='#999999' />
                    }
                </div>
                {
                    this.isOpen ? <div class={styles.content}>
                        {
                            this.$slots.default
                        }
                    </div> : ''
                }
            </div> : ''
        );
    }
}
