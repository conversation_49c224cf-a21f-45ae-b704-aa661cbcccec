.group {
    min-height: 50px;
    border-radius: 4px;
    background: #ffffff;
    position: relative;
    box-shadow: 0px 0.5px 0px 0px #e8e8e8;

    .title_content {
        min-height: 50px;
        display: flex;
        align-items: center;
        padding: 0 12px;
        box-shadow: 0px 0.5px 0px 0px #e8e8e8;

        .title {
            flex: 1;
            display: flex;
            align-items: center;

            .base_title {
                font-size: 16px;
                font-weight: bold;
                color: #444444;
            }

            .extend_title {
                font-size: 12px;
                color: #333333;
                margin-left: 10px;
            }
        }

        &:before {
            width: 3px;
            height: 16px;
            background-color: #009a3e;
            content: ' ';
            border-radius: 3px;
            position: absolute;
            left: 0px;
        }
    }

    .content {
        padding: 1px 12px 0 12px;
    }
}

.group:not(:last-child) {
    margin-bottom: 10px;
}
