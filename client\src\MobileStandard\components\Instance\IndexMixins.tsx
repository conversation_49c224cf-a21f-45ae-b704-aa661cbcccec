import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import { diffDay_Seconds, diffHours_Seconds } from '@/MobileStandard/common/utils/date-helper';
import lodash from 'lodash';

@Component
export class IndexMixins extends Vue {
    @Prop() tabActive!: string;
    @Prop() instance!: any;
    @Prop({ default: false }) isBatch!: boolean;
    @Prop({ default: false }) defaultChecked!: boolean;
    isChecked = false;
    @Watch('defaultChecked')
    defaultCheckedChange(newVal: any) {
        this.isChecked = newVal ? true : false;
    }
    @Emit('change')
    change(checked: boolean, id: any) {
    }
    @Emit('click')
    click(instance: any) {}
    trafficLightsFormat(overDateMinute: any) {
        let timeFormatStr = '';
        const time = lodash.round(overDateMinute) * 60;
        if (overDateMinute < 1) {
            timeFormatStr = '1min';
        } else if (diffHours_Seconds(time) < 1) {
            timeFormatStr = `${overDateMinute}min`;
        } else if (diffHours_Seconds(time) <= 48) {
            timeFormatStr = `${lodash.round(diffHours_Seconds(time))}h`;
        } else {
            timeFormatStr = `${lodash.round(diffDay_Seconds(time))}d`;
        }
        return timeFormatStr ? `${this.$l.getLocale('fields.surplus')} ${timeFormatStr}` : '';
    }
    created() {
    }
}
