import { Component } from 'vue-property-decorator';
import styles from './instance.module.less';
import avatarDefault from '@/assets/images/Avatar_default.png';
import { dateHelper } from '@/MobileStandard/common/utils/date-helper';
import lodash from 'lodash';
import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';

@Component
export class Instance extends mixins(IndexMixins) {
    render() {
        return (
            <div class={styles.instance_content}>
                {
                    this.isBatch ?
                        <van-checkbox v-model={this.isChecked} class={styles.checkbox}
                            disabled={!this.instance.isBatchApprove}
                            checked-color='#0e9266'
                            on-change={(checked: boolean) => this.change(checked, this.instance.id)} />
                        : ''
                }
                <div class={styles.instance_info + (this.isBatch ? ' ' + styles.batch_instance_info : '')}
                    on-click={() => this.click(this.instance)}>
                    <div class={styles.topic + (this.tabActive === 'dbl' || this.tabActive === 'csw' ?
                        !this.instance.isRead ? ' ' + styles.noRead : '' : '')}>
                        {
                            this.instance.businessTitle
                        }
                        {
                            this.tabActive === 'dbl' && this.instance.specialLabel ?
                                lodash.map(this.instance.specialLabel.split(';'), (m: any) => {
                                    return <div class={styles.specialLabel}>
                                        {
                                            this.$l.getLocale(`fields.${m}`)
                                        }
                                    </div>;
                                })
                                : ''
                        }
                        {/* {
                            this.tabActive === 'ybl' && this.instance.isOverDateTime ?
                                <div class={styles.overtime}>
                                    {
                                        this.$l.getLocale('fields.overtime')
                                    }
                                </div> : ''
                        }
                        {
                            this.tabActive === 'dbl' && this.instance.isOverDateTime ?
                                <div class={styles.overtime}>
                                    {
                                        this.$l.getLocale('fields.overtime')
                                    }
                                </div> : ''
                        } */}
                        {
                            !this.instance.tripartiteViewMobileUrl ?
                                <div class={styles.pc}>
                                    <img src={require('@/assets/images/pc_o.png')} />
                                </div> : ''
                        }
                    </div>
                    <div class={styles.extend1}>
                        <div class={styles.extend_group}>
                            <i class='iconfont icon-app_icon_bianhao'
                                style='color:#e0e0e0;'></i>
                            <span>{this.$l.getLocale('fields.number')}：</span>
                            <span class={styles.businessNumber}>{this.instance.businessNumber}</span>
                        </div>
                        <div class={styles.extend_group}>
                            <i class='iconfont icon-app_icon_fenlei'
                                style='color:#e0e0e0;'></i>
                            <span>{this.$l.getLocale('fields.class')}：</span>
                            <span>
                                {
                                    this.instance.groupName ?
                                        this.instance.groupName : '其它'
                                }
                            </span>
                        </div>
                    </div>
                    <div class={styles.extend2}>
                        <img src={this.instance.avatar ? this.instance.avatar : avatarDefault} />
                        <span class={styles.user_name}>
                            {
                                this.instance.startUserName
                            }
                        </span>
                        <span class={styles.date}>
                            {
                                `|  ${dateHelper.dateFormat(this.instance.startTime)}`
                            }
                        </span>
                        {
                            this.tabActive === 'dbl' && this.instance.overDateMinute && this.instance.overDateMinute > 0 ?
                                <div class={styles.tips}>
                                    <div class={this.instance.trafficLights ?
                                        ' ' + styles[`tips_${lodash.toLower(this.instance.trafficLights)}`] :
                                        ''}>
                                        <span>{this.instance.trafficLights ?
                                            this.trafficLightsFormat(this.instance.overDateMinute) : ''}</span>
                                    </div>
                                </div> : ''
                        }
                        {
                            (this.tabActive === 'brfq' || this.tabActive === 'ybl' || this.tabActive === 'csw')
                                && this.instance.businessStatusName ?
                                <div class={styles.status + ' ' + styles[this.instance.businessStatusCode]}>
                                    <div class={styles.prefix + ' ' + styles[this.instance.businessStatusCode + '_prefix']} />
                                    {
                                        this.instance.businessStatusName
                                    }
                                </div> : ''
                        }
                    </div>
                </div>
            </div>
        );
    }
}
