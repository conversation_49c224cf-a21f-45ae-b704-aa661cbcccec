.instance_content {
    background: #ffffff;
    border-radius: 4px;
    padding: 12px;
    display: flex;
    align-items: center;

    .checkbox {
        padding-right: 8px;
        width: 20px;
    }

    .instance_info {
        flex: 1;
        width: 100%;
    }

    .batch_instance_info {
        width: calc(~'100% - 28px');
    }

    .topic {
        font-size: 15px;
        color: #333333;
        // font-weight: bold;
        min-height: 21px;

        .overtime {
            margin-left: 5px;
            background: #be4848;
            color: #ffffff;
            border-radius: 4px;
            display: inline-block;
            font-size: 12px;
            font-weight: 400;
            text-align: center;
            width: 30px;
        }

        .specialLabel {
            margin-left: 5px;
            border: 1px solid #ff6600;
            color: #ff6600;
            border-radius: 4px;
            display: inline-block;
            font-size: 12px;
            font-weight: 400;
            text-align: center;
            min-width: 30px;
        }

        .pc {
            margin-left: 5px;
            display: inline-block;
            vertical-align: middle;
        }
    }

    .noRead {
        color: #be4848;
    }

    .extend1 {
        display: inline-block;
        // display: flex;
        // align-items: center;
        width: 100%;
        // min-height: 30px;
        // line-height: 30px;
        line-height: 16px;

        .extend_group {
            width: 50%;
            display: flex;
            // align-items: center;
            float: left;
            padding: 5px 0;

            i {
                margin-right: 5px;
            }

            span {
                font-size: 12px;
                color: #999999;
                font-weight: 400;
                display: inline-block;
                // white-space: nowrap;
                // overflow: hidden;
            }

            span:last-child {
                flex: 1;
                // word-break: break-all;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .businessNumber {
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: normal !important;
                white-space: nowrap !important;
            }
        }
    }

    .extend2 {
        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
        height: 20px;
        line-height: 20px;

        img {
            height: 20px;
            width: 20px;
            margin-right: 5px;
            border-radius: 50%;
        }

        .user_name {
            color: #666666;
        }

        .date {
            margin-left: 5px;
            color: #999999;
        }

        .tips {
            flex: 1;
            text-align: right;
            height: 18px;
        }

        .tips_yellow {
            min-width: 65px;
            border-radius: 1px;
            color: #d59000;
            background-color: rgba(213, 144, 0, 0.1);
            font-size: 12px;
            text-align: center;
            display: inline-block;
        }

        .tips_red {
            min-width: 65px;
            border-radius: 1px;
            color: #be4848;
            background-color: rgba(190, 72, 72, 0.1);
            font-size: 12px;
            text-align: center;
            display: inline-block;
        }

        .tips_green {
            min-width: 65px;
            border-radius: 1px;
            color: #0e9266;
            background-color: rgba(14, 146, 102, 0.1);
            font-size: 12px;
            text-align: center;
            display: inline-block;
        }

        .status {
            border-radius: 5px;
            padding: 0 5px;
            display: inline-block;
            position: absolute;
            right: 12px;
            display: flex;
            align-items: center;
        }

        .prefix {
            height: 4px;
            width: 4px;
            border-radius: 50%;
            margin-right: 5px;
        }
    }

    .extend3 {
        height: 30px;
        line-height: 30px;
    }
}

.instance_content:not(:first-child) {
    margin-top: 10px;
}

@process-start: #2998fe;
@process-ready: #333;
@process-processing: #ee7d42;
@process-approved: #25bf5d;
@process-refused: #c04c4c;
@process-canceled: #919bb5;
@process-rejected: #333;
@process-exception: #ee0a24;
@process-todo: #9c9c9c;

.tagColorIcon(@tag-color) {
    background-color: @tag-color;
}

.tagColor(@tag-color) {
    color: @tag-color !important;
    background-color: tint(@tag-color, 80%);
}

.start_prefix {
    .tagColorIcon(@process-start);
}

.ready_prefix {
    .tagColorIcon(@process-ready);
}

.processing_prefix {
    .tagColorIcon(@process-processing);
}

.approved_prefix {
    .tagColorIcon(@process-approved);
}

.refused_prefix {
    .tagColorIcon(@process-refused);
}

.canceled_prefix {
    .tagColorIcon(@process-canceled);
}

.rejected_prefix {
    .tagColorIcon(@process-rejected);
}

.todoIcon {
    .tagColorIcon(@process-todo);
}

.start {
    .tagColor(@process-start);
}

.ready {
    .tagColor(@process-ready);
}

.processing {
    .tagColor(@process-processing);
}

.approved {
    .tagColor(@process-approved);
}

.refused {
    .tagColor(@process-refused);
}

.canceled {
    .tagColor(@process-canceled);
}

.rejected {
    .tagColor(@process-rejected);
}

.todo {
    .tagColor(@process-todo);
}