import { Component } from 'vue-property-decorator';
import styles from './main-menu.module.less';
import todo from '@/assets/images/daibanzhongxin.png';
import todo2 from '@/assets/images/<EMAIL>';
import map from '@/assets/images/faqiliucheng.png';
import map2 from '@/assets/images/<EMAIL>';
import { getComponentFromProp } from '@/MobileStandard/common/utils';
import { IndexMixins } from './IndexMixins';
import { mixins } from 'vue-class-component';

@Component
export class MainMenu extends mixins(IndexMixins) {
  render() {
    const contentDom = getComponentFromProp(this, 'content');
    return (
      <div class={styles.main}>
        <div class={styles.content + (this.isBatch ? ' ' + styles.content_batch : '')}>
          {
            contentDom
          }
        </div>
        {
          this.isBatch ?
            <div class={styles.batch_opt_buttons}>
              <div class={styles.opt_button + ' ' + styles.agree}
                on-click={() => this.agree()}
              >{this.$l.getLocale('buttons.batchAgree')}</div>
            </div>
            :
            <van-tabbar
              v-model={this.active}
              active-color='#2165D9'
              inactive-color='#cccccc'>
              <van-tabbar-item name='todo-center'
                replace
                to='/todo'>
                <span>{this.$l.getLocale('fields.todoCenter')}</span>
                <template slot='icon'>
                  <img src={this.active === 'todo-center' ? todo2 : todo} />
                </template>
              </van-tabbar-item>
              <van-tabbar-item name='process-map'
                replace
                to='/map'>
                <span>{this.$l.getLocale('fields.startProcess')}</span>
                <template slot='icon'>
                  <img src={this.active === 'process-map' ? map2 : map} />
                </template>
              </van-tabbar-item>
            </van-tabbar>
        }
      </div>
    );
  }
}
