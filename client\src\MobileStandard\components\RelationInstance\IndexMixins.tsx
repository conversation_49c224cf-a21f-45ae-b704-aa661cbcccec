import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';

@Component
export class IndexMixins extends Vue {
    @Prop() instance!: any;
    @Prop({ default: false }) defaultChecked!: boolean;
    isChecked = false;
    @Watch('defaultChecked')
    defaultCheckedChange(newVal: any) {
        this.isChecked = newVal ? true : false;
    }
    @Emit('change')
    change(checked: boolean, id: any) {
    }
    @Emit('click')
    click(instance: any) {}
    created() {
    }
}
