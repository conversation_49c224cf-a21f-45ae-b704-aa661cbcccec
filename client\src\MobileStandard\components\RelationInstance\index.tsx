import { Component } from 'vue-property-decorator';
import styles from './instance.module.less';
import avatarDefault from '@/assets/images/Avatar_default.png';
import { dateHelper } from '@/MobileStandard/common/utils/date-helper';
import lodash from 'lodash';
import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';

@Component
export class RelationInstance extends mixins(IndexMixins) {
    render() {
        return (
            <div class={styles.instance_content}>
                <van-checkbox v-model={this.isChecked} class={styles.checkbox}
                    checked-color='#0e9266'
                    on-change={(checked: boolean) => this.change(checked, this.instance.id)} />
                <div class={styles.instance_info}
                    on-click={() => this.click(this.instance)}>
                    <div class={styles.topic}>
                        {
                            this.instance.topic
                        }
                    </div>
                    <div class={styles.extend1}>
                        <div class={styles.extend_group}>
                            <i class='iconfont icon-app_icon_bian<PERSON>'
                                style='color:#e0e0e0;'></i>
                            <span>{this.$l.getLocale('fields.number')}：</span>
                            <span class={styles.businessNumber}>{this.instance.number}</span>
                        </div>
                        <div class={styles.extend_group}>
                            <i class='iconfont icon-app_icon_fenlei'
                                style='color:#e0e0e0;'></i>
                            <span>{this.$l.getLocale('fields.class')}：</span>
                            <span>
                                {
                                    this.instance.groupName ?
                                        this.instance.groupName : '其它'
                                }
                            </span>
                        </div>
                    </div>
                    <div class={styles.extend2}>
                        <img src={this.instance.avatar ? this.instance.avatar : avatarDefault} />
                        <span class={styles.user_name}>
                            {
                                this.instance.userName
                            }
                        </span>
                        <span class={styles.date}>
                            {
                                `|  ${dateHelper.dateFormat(this.instance.startTime)}`
                            }
                        </span>
                    </div>
                </div>
            </div>
        );
    }
}
