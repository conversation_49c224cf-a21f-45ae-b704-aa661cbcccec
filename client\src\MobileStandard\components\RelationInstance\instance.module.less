.instance_content {
    background: #ffffff;
    border-radius: 4px;
    padding: 12px;
    display: flex;
    align-items: center;

    .checkbox {
        padding-right: 8px;
    }

    .instance_info {
        flex: 1;
        width: calc(~'100% - 28px');
    }

    .topic {
        font-size: 15px;
        color: #333333;
        font-weight: bold;
        min-height: 21px;
    }

    .extend1 {
        display: inline-block;
        width: 100%;
        line-height: 16px;

        .extend_group {
            width: 50%;
            display: flex;
            float: left;
            padding: 5px 0;
            i {
                margin-right: 5px;
            }

            span {
                font-size: 12px;
                color: #999999;
                font-weight: 400;
                display: inline-block;
            }

            span:last-child {
                flex: 1;
                word-break: break-all;
                white-space: pre-wrap;
            }

            .businessNumber {
                text-overflow: ellipsis;
                overflow: hidden;
                word-break: normal !important;
                white-space: normal !important;
            }
        }
    }

    .extend2 {
        display: flex;
        align-items: center;
        font-size: 12px;
        font-weight: 400;
        height: 20px;
        line-height: 20px;

        img {
            height: 20px;
            width: 20px;
            margin-right: 5px;
            border-radius: 50%;
        }

        .user_name {
            color: #666666;
        }

        .date {
            margin-left: 5px;
            color: #999999;
        }
    }
}

.instance_content:not(:first-child) {
    margin-top: 10px;
}
