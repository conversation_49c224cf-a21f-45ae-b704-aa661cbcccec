import { guid<PERSON><PERSON>per } from '@/MobileStandard/common/utils';
import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator';
import E from 'wangeditor';
import { richTextService } from './service';
import UploadFileMenu from './uploadFileMenu';
@Component
export class RichText extends Vue {
    @Prop() value!: any;
    @Prop() height!: any;
    @Prop() id!: any;
    @Prop() disabled!: boolean;
    private editor: any;
    @Watch('value') onValueChange(value: any, oldValue: any) {
        if (value) {
            if (this.editor === null || this.editor === undefined) {
                this.initRender();
            } else if (value !== this.editor.txt.html()) {
                this.editor.txt.html(this.value);
            }
        }
    }
    @Watch('disabled') onDisabledChange(value: any, oldValue: any) {
        if (this.editor !== null && this.editor !== undefined) {
            if (value) {
                this.editor.enable();
            } else {
                this.editor.disable();
            }
        }
    }
    @Emit('change')
    onChange(value: any) {
    }
    // private onUploadFile(e: any) {
    //     const file = e.target.files[0];
    //     richTextService.upload(file).subscribe((s: any) => {
    //         if (s.fileId) {
    //             const html = `<a href=\"${s.fileUrl}\" target=\"_blank\"><span>${s.fileName}</span></a>`;
    //             this.editor.cmd.do('insertHTML', html);
    //         }
    //         const obj: any = document.getElementById(`file${this.editor.id}`);
    //         obj.value = '';
    //     });
    // }
    private initRender() {
        this.$nextTick(() => {
            this.editor = new E(`.div${this.id}`);
            this.editor.id = `${this.id}`;
            this.editor.config.zIndex = 1;
            this.editor.config.showMenuTooltips = false;
            if (this.height) {
                this.editor.config.height = this.height;
            }
            this.editor.config.menus = [
                'head',
                'bold',
                'fontSize',
                'fontName',
                'italic',
                'underline',
                'indent',
                'lineHeight',
                'foreColor',
                'table',
                'undo',
                'redo'
            ];
            // this.editor.config.uploadImgMaxLength = 1;
            // this.editor.config.uploadImgServer = '/api/platform/v1/documents/form-files-upload';
            // this.editor.config.uploadImgHooks = {
            //     customInsert: (insertImgFn: any, result: any) => {
            //         insertImgFn(result.fileUrl.replace('view', 'doc/download'));
            //     }
            // };
            this.editor.config.showLinkImg = false;
            const that = this;
            this.editor.config.onchange = (newHtml: any) => {
                that.onChange(newHtml);
            };
            this.editor.config.onchangeTimeout = 500;
            this.editor.config.placeholder = '';
            // const menuKey = `uploadFile${this.id}`;
            // this.editor.menus.extend(menuKey, UploadFileMenu);
            // this.editor.config.menus = this.editor.config.menus.concat(menuKey);
            this.editor.create();
            if (this.disabled) {
                this.editor.disable();
            }
            this.editor.txt.html(this.value);
        });
    }
    beforeDestroy() {
        // 销毁编辑器
        this.editor.destroy();
        this.editor = null;
    }
    created(): void {
        this.initRender();
    }
    render() {
        return (
            <div style='width:100%;'>
                <div class={`div${this.id}`}></div>
                {/* <div style='display:none;'>
                    <input id={`file${this.id}`} type='file' on-change={this.onUploadFile} />
                </div> */}
            </div>
        );
    }
}
