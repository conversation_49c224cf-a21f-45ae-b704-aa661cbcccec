import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator';
import styles from './senior-select-user-input.module.less';
import lodash from 'lodash';
import { SeniorSelectUser } from '../SeniorSelectUser';
import { guidHelper } from '@/MobileStandard/common/utils';

@Component({
    components: {
        SeniorSelectUser
    }
})
export class SeniorSelectUserInput extends Vue {
    @Prop() value!: any[] | undefined;
    @Prop() userValue!: any[] | undefined;
    @Prop({ default: true }) isEdit!: boolean;
    @Prop({ default: true }) isMultiple!: boolean;
    @Prop({ default: false }) isOnlyUser!: boolean;
    @Prop() placeholder!: string;
    @Prop({ default: true }) isExpand!: boolean;
    private copyValue: any[] = [];
    private copyUserValue: any[] = [];
    private modalVisible = false;
    private baseShowHeight = 60;
    private expand = false;
    private showExpandBut = false;
    private tId = guidHelper.generate();
    @Watch('userValue')
    userValueWatch(newVal: any, oldVal: any) {
        this.loadData(this.value, this.userValue);
    }
    @Watch('value')
    valueWatch(newVal: any, oldVal: any) {
        this.loadData(this.value, this.userValue);
    }
    @Emit('change')
    valueEmit(value: any) { }
    @Emit('userChange')
    userValueEmit(userValue: any) { }
    private onModalOk(item: any) {
        this.loadData(item.value, item.userValue);
        const newUserValue = lodash.map(item.userValue, (a: any) => {
            return {
                userId: a.value,
                userName: a.label,
                organizationIdPath: a.organizationIdPath || '',
                organizationNamePath: a.organizationNamePath || '',
                positionName: a.positionName || '',
                userLoginId: a.account || '',
                email: a.email || '',
                mobilePhone: a.mobilePhone || ''
            };
        });
        this.valueEmit(item.value);
        this.userValueEmit(newUserValue);
        this.modalVisible = false;
    }
    private loadData(value: any, userValue: any) {
        this.copyUserValue = lodash.map(userValue || [], (a: any) => {
            return {
                value: a.userId || a.value || '',
                label: a.userName || a.label || '',
                organizationIdPath: a.organizationIdPath || '',
                organizationNamePath: a.organizationNamePath || '',
                positionName: a.positionName || '',
                account: a.userLoginId || a.account || '',
                email: a.email || '',
                mobilePhone: a.mobilePhone || ''
            };
        });
        this.copyValue = value || lodash.map(this.copyUserValue, (cuv: any) => {
            return {
                ...cuv,
                type: 'user'
            };
        });
        if (!this.isEdit && this.isExpand) {
            this.$nextTick(() => {
                const offsetHeight = (this.$refs[this.tId] as any).offsetHeight;
                if (offsetHeight > this.baseShowHeight) {
                    this.showExpandBut = true;
                    this.expand = false;
                }
            });
        }
    }
    private getZDZZRY(a: any, b: any) {
        return lodash.filter(b, (c: any) => c.organizationIdPath && lodash.startsWith(c.organizationIdPath, a.fullPathCode));
    }
    created() {
        this.loadData(this.value, this.userValue);
    }
    render() {
        return (
            <div>
                <div class={styles.senioruserinput}>
                    <div
                        class={styles.contenteditable +
                            (' ' + (this.showExpandBut && !this.expand ? styles.webkit : styles.block))}
                        placeholder={this.placeholder}
                        ref={this.tId}
                    >
                        {
                            lodash.map(this.copyValue, (m: any, i: any) => {
                                let content: any;
                                if (m.type === 'user') {
                                    content = <span>{m.label}</span>;
                                } else if (m.type === 'org') {
                                    const users = this.getZDZZRY(m, this.copyUserValue);
                                    content = <span>{m.label}{users.length > 0 ? `(${users.length})` : ''}</span>;
                                }
                                return <div class={styles.content}>
                                    {content}
                                </div>;
                            })
                        }
                    </div>
                    {
                        this.isEdit ? <van-icon name='user-o' class={styles.icon}
                            on-click={() => this.modalVisible = true} /> : ''
                    }
                    {
                        this.modalVisible ? <senior-select-user
                            visible={this.modalVisible}
                            value={this.copyValue}
                            userValue={this.copyUserValue}
                            on-cancel={() => this.modalVisible = false}
                            on-ok={(item: any) => this.onModalOk(item)}
                        ></senior-select-user> : ''
                    }
                </div>
                {
                    this.showExpandBut ? <span
                        class={styles.expandbut}
                        on-click={() => this.expand = !this.expand}
                    >{this.expand ? '收起' : '展开'}</span> : ''
                }
                <van-uploader
                    v-model={this.value}
                    style='display:none'
                />
            </div>
        );
    }
}
