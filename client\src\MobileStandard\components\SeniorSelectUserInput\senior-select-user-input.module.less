.senioruserinput {
    display: flex;
    align-items: baseline;

    .contenteditable {
        margin-right: 5px;

        .content {
            display: inline-block;
            margin-left: 12px;
        }

    }

    .contenteditable:empty::before {
        content: attr(placeholder);
        color: #bbb;
    }

    .icon {
        color: #009a3e !important;
    }
}

.expandbut {
    cursor: pointer;
    color: #009a3e !important;
    float: right;
    margin-left: 10px;
}

.webkit {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

.block {
    display: inline;
}
