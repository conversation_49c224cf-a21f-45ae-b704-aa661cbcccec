import { Component, Vue, Prop, Emit, Watch } from 'vue-property-decorator';
import styles from './senior-select-user.module.less';
import lodash from 'lodash';
import { userTransferInputService } from '../UserTransferInput/service';
import { authService } from '@/MobileStandard/services/auth';

@Component
export class SeniorSelectUser extends Vue {
  @Prop() value!: any;
  @Prop() userValue!: any;
  @Prop({ default: false }) visible!: boolean;
  @Prop({ default: 900 }) widthModal!: number;
  @Prop({ default: 1 }) defaultWorkingState!: number;
  @Prop({ default: 1 }) defaultInvalidState!: number;
  @Prop({ default: true }) isMultiple!: boolean;
  @Prop({ default: false }) isOnlyUser!: boolean;
  private activeKey = '1';
  private searchKw = '';
  private searchRylb: any = [];
  private zzjg: any = [];
  private zjlxr: any = [];
  private nbfz: any = [];
  private cacherylb: any = [];
  private cachezzlb: any = [];
  private rightData: any = [];
  private allUsersChecked = false;
  @Watch('visible')
  visibleWatch(newVal: any, oldVal: any) {
    if (newVal) {
      this.cacherylb = lodash.cloneDeep(this.userValue);
      this.rightData = lodash.cloneDeep(this.value || lodash.map(this.userValue, (cuv: any) => {
        return {
          ...cuv,
          type: 'user'
        };
      }));
    }
  }
  @Emit('cancel')
  cancel() { }
  @Emit('ok')
  ok() {
    const users = this.getZZRY;
    return {
      value: this.isOnlyUser ? lodash.map(users, (u: any) => {
        return {
          ...u,
          type: 'user'
        };
      }) : this.rightData,
      userValue: this.getZZRY
    };
  }
  private setZZLB(a: any) {
    this.cachezzlb.push(...lodash.map(lodash.filter(a, (b: any) => lodash.findIndex(this.cachezzlb,
      (c: any) => c.value === b.value) === -1),
      (d: any) => {
        return {
          value: d.value,
          label: d.label,
          fullPathCode: d.fullPathCode
        };
      }));
  }
  private setRYLB(a: any) {
    this.cacherylb.push(...lodash.map(lodash.filter(a, (b: any) => lodash.findIndex(this.cacherylb,
      (c: any) => c.value === b.value) === -1),
      (d: any) => {
        return {
          value: d.value,
          label: d.label,
          organizationIdPath: d.organizationIdPath,
          positionName: d.positionName,
          organizationNamePath: d.organizationNamePath,
          account: d.account,
          email: d.email,
          mobilePhone: d.mobilePhone
        };
      }));
  }
  private changeActiveKey(a: any) {
    this.zzjg = [];
    switch (a) {
      case '1':
        // 获取组织结构
        if (this.zzjg.length === 0) {
          userTransferInputService.getDepartmentTreeUser(null,
            this.defaultInvalidState, this.defaultWorkingState, this.allUsersChecked).subscribe((b: any) => {
            this.zzjg = lodash.map(b.organizations || [], (c: any) => {
              return {
                ...c,
                isLeaf: c.childCount === 0 && (c.userCount === 0 || !c.userCount) ? true : false,
                isRoot: true,
                isExpand: false,
                type: 'org'
              };
            });
            this.setZZLB(this.zzjg);
            lodash.forEach(this.zzjg, (d: any) => {
              if (!d.isLeaf) {
                this.loadDepartmentTreeUser(d);
              }
            });
          });
        }
        break;
      case '2':
        if (this.nbfz.length === 0) {
          userTransferInputService.getUserGroup({ isAll: true }).subscribe(x => {
            lodash.forEach(x.items, (e: any) => {
              const users = lodash.map(e.groupUsers, (g: any) => {
                return {
                  value: g.userId,
                  label: g.userName,
                  organizationIdPath: g.organizationIdPath,
                  positionName: g.positionName,
                  organizationNamePath: g.organizationNamePath,
                  account: g.account,
                  email: g.email,
                  mobilePhone: g.mobilePhone,
                  isLeaf: true,
                  type: 'user'
                };
              });
              this.nbfz.push({
                label: e.groupName,
                value: e.groupId,
                isLeaf: e.groupUsers === 0 ? true : false,
                isRoot: true,
                isExpand: false,
                type: 'group',
                children: users
              });
              this.setRYLB(users);
            });
          });
        }
        break;
      case '3':
        if (this.zjlxr.length === 0) {
          const params = {
            'user-id': authService.user.id,
            'contact-user-status': '1',
            'page-size': 100,
            'page-index': 1,
          };
          userTransferInputService.getLaestContact(params).subscribe((rs: any) => {
            this.zjlxr = lodash.map(rs.items, (r: any) => {
              return {
                value: r.contactUserId,
                label: r.contactUserName,
                organizationIdPath: r.orgIdPath,
                positionName: r.positionName,
                organizationNamePath: r.orgNamePath,
                account: r.contactUserLoginId,
                email: r.email,
                mobilePhone: r.mobilePhone,
                isLeaf: true,
                type: 'user'
              };
            });
            this.setRYLB(this.zjlxr);
          });
        }
        break;
    }
  }
  private loadDepartmentTreeUser(a: any) {
    if (a.children) {
      a.isExpand = !a.isExpand;
      this.zzjg = [...this.zzjg];
      return;
    }

    userTransferInputService.getDepartmentTreeUser(a.value,
      this.defaultInvalidState,
      this.defaultWorkingState,
      this.allUsersChecked).subscribe((b: any) => {
        const zz = lodash.map(b.organizations || [], (c: any) => {
          return {
            ...c,
            isLeaf: c.childCount === 0 && (c.userCount === 0 || !c.userCount) ? true : false,
            isRoot: false,
            isExpand: false,
            type: 'org'
          };
        });
        const zzry = lodash.map(b.users || [], (c: any) => {
          return {
            ...c,
            isLeaf: true,
            type: 'user'
          };
        });
        a.children = [...zz, ...zzry];
        a.isExpand = true;
        this.zzjg = [...this.zzjg];
        this.setZZLB(zz);
        this.setRYLB(zzry);
      });
  }
  private get getZZRY() {
    const users: any = [];
    lodash.forEach(this.rightData, (a: any) => {
      if (a.type === 'user' && lodash.findIndex(users, (b: any) => b.value === a.value) === -1) {
        users.push(a);
      } else if (a.type === 'org') {
        const cachry = lodash.filter(this.cacherylb, (c: any) => c.organizationIdPath &&
          lodash.startsWith(c.organizationIdPath, a.fullPathCode) &&
          lodash.findIndex(users, (d: any) => d.value === c.value) === -1);
        users.push(...cachry);
      }
    });
    return users;
  }
  private onLeftSearch() {
    if (this.searchKw) {
      userTransferInputService.getNewUserList(this.searchKw, this.defaultInvalidState,
        this.defaultWorkingState, this.allUsersChecked).subscribe(data => {
          console.log(data);
          const users = lodash.map(data, (b: any) => {
            return {
              value: b.value,
              label: b.label,
              organizationIdPath: b.organizationIdPath,
              positionName: b.positionName,
              organizationNamePath: b.organizationNamePath,
              account: b.account,
              email: b.email,
              mobilePhone: b.mobilePhone,
              isLeaf: true,
              type: 'user'
            };
          });
          this.searchRylb = users;
          this.setRYLB(users);
        });
    } else {
      this.searchRylb = [];
    }
  }
  private onImportChecked() {
    this.rightData = [];
  }
  private onExpand(a: any) {
    this.loadDepartmentTreeUser(a);
  }
  private onRowCheck(a: any, b: any, c: any) {
    if (!c) {
      const index = lodash.findIndex(this.rightData, (d: any) => d.value === b.value && d.type === b.type);
      if (index > -1) {
        this.rightData.splice(index, 1);
      } else {
        if (!this.isMultiple) {
          this.rightData = [];
        }
        if (b.type === 'org') {
          this.rightData.push({
            type: 'org',
            value: b.value,
            label: b.label,
            fullPathCode: b.fullPathCode
          });
          userTransferInputService.getUserListByOrg2(b.value, this.defaultInvalidState,
            this.defaultWorkingState).subscribe(e => {
              this.setRYLB(e);
            });
        } else {
          this.rightData.push({
            type: 'user',
            value: b.value,
            label: b.label,
            organizationIdPath: b.organizationIdPath,
            positionName: b.positionName,
            organizationNamePath: b.organizationNamePath,
            account: b.account,
            email: b.email,
            mobilePhone: b.mobilePhone
          });
        }
      }
    }
  }
  private onRowAllCheck(a: any, b: any, c: any) {
    lodash.forEach(b, (d: any) => {
      const index = lodash.findIndex(this.rightData, (e: any) => e.value === d.value && e.type === d.type);
      if (index > -1 && c) {
        this.rightData.splice(index, 1);
      } else if (index === -1 && !c) {
        this.rightData.push({
          type: 'user',
          value: d.value,
          label: d.label,
          organizationIdPath: d.organizationIdPath,
          positionName: d.positionName,
          organizationNamePath: d.organizationNamePath,
          account: d.account,
          email: d.email,
          mobilePhone: d.mobilePhone
        });
      }
      console.log(this.rightData, b);
    });
  }
  private getZZJGHtml(a: any, b: any, c: any, d: any): any {
    let isAddSelectAll = false;
    return lodash.map(a, (e: any) => {
      if (e.type === 'org') {
        const checkbox_disabled = lodash.findIndex(d, (r: any) => r.type === 'org' &&
          r.value !== e.value &&
          lodash.startsWith(e.fullPathCode, r.fullPathCode)) > -1 ? true : false;
        const checkbox_value = lodash.findIndex(d, (r: any) => (r.value === e.value && r.type === e.type) ||
          (r.type === 'org' && r.value !== e.value && lodash.startsWith(e.fullPathCode, r.fullPathCode))) > -1 ? true : false;
        return [<div class={styles.row}>
          <div class={styles.rowexpand}
            style={{ 'margin-left': `${c * 10}px` }}
            on-click={() => this.onExpand(e)}>
            {
              !e.isLeaf ? <van-icon name={e.isExpand ? 'minus' : 'plus'} /> : ''
            }
          </div>
          <div class={styles.rowprefix}>
            <i class={`iconfont ${e.isRoot ? 'icon-shouye' : 'icon-wenjianjia'}`} />
          </div>
          <span class={styles.rowlabel + (e.isLeaf ? ' ' + styles.rowlabelhascheckbox : '')}
          >{e.label}</span>
          {
            !e.isLeaf && this.isMultiple ?
              <van-checkbox class={styles.rowcheckbox}
                disabled={checkbox_disabled}
                value={checkbox_value}
                on-click={(event: any) => this.onRowCheck(event, e, checkbox_disabled)} /> :
              ''
          }
        </div>, e.isExpand && e.children ? this.getZZJGHtml(e.children, a, c + 1, d) : ''];
      } else if (e.type === 'user') {
        const checkbox_disabled = lodash.findIndex(d, (r: any) => r.type === 'org' &&
          r.value !== e.value &&
          lodash.startsWith(e.organizationIdPath, r.fullPathCode)) > -1 ? true : false;
        const checkbox_value = lodash.findIndex(d, (r: any) => (r.value === e.value && r.type === e.type) ||
          (r.type === 'org' && r.value !== e.value && lodash.startsWith(e.organizationIdPath, r.fullPathCode))) > -1 ? true : false;
        const bz = <div class={styles.row}>
          <van-checkbox class={styles.rowexpand}
            style={{ 'margin-left': '15px' }}
            disabled={checkbox_disabled}
            value={checkbox_value}
            on-click={(event: any) => this.onRowCheck(event, e, checkbox_disabled)} />
          <div class={styles.rowuser}>
            {
              e.label.slice(-2)
            }
          </div>
          <span class={styles.rowlabel}
          >{e.label}</span>
        </div>;
        if (!checkbox_disabled && !isAddSelectAll && this.isMultiple) {
          isAddSelectAll = true;
          const users = lodash.filter(a, (ta: any) => ta.type === 'user');
          const userCheckds = lodash.filter(users, (ud: any) => lodash.findIndex(d,
            (td: any) => td.value === ud.value && td.type === 'user') > -1);
          const selectAllValue = users.length === userCheckds.length;
          // 增加全选
          return [
            <div class={styles.row}>
              <van-checkbox class={styles.rowexpand}
                style={{ 'margin-left': '15px' }}
                value={selectAllValue}
                on-click={(event: any) => this.onRowAllCheck(event, users, selectAllValue)} />
              <span class={styles.rowlabel}
              >全选</span>
            </div>, bz
          ];
        } else {
          return bz;
        }
      } else if (e.type === 'group') {
        const checkbox_disabled = lodash.findIndex(d, (r: any) => r.type === 'org' &&
          r.value !== e.value &&
          lodash.startsWith(e.fullPathCode, r.fullPathCode)) > -1 ? true : false;
        const checkbox_value = lodash.findIndex(d, (r: any) => (r.value === e.value && r.type === e.type) ||
          (r.type === 'org' && r.value !== e.value && lodash.startsWith(e.fullPathCode, r.fullPathCode))) > -1 ? true : false;
        return [<div class={styles.row}>
          <div class={styles.rowexpand}
            style={{ 'margin-left': `${c * 10}px` }}
            on-click={() => this.onExpand(e)}>
            {
              !e.isLeaf ? <van-icon name={e.isExpand ? 'minus' : 'plus'} /> : ''
            }
          </div>
          <div class={styles.rowprefix}>
            <i class={`iconfont ${e.isRoot ? 'icon-shouye' : 'icon-wenjianjia'}`} />
          </div>
          <span class={styles.rowlabel + (e.isLeaf ? ' ' + styles.rowlabelhascheckbox : '')}
          >{e.label}</span>
        </div>, e.isExpand && e.children ? this.getZZJGHtml(e.children, a, c + 1, d) : ''];
      }
    });
  }
  private onAllUsers(e: any) {
    this.onImportChecked();
    this.allUsersChecked = !this.allUsersChecked;
    if (this.activeKey === '1') {
      this.changeActiveKey('1');
    }
  }
  created() {
    if (this.visible) {
      this.cacherylb = lodash.cloneDeep(this.userValue || []);
      this.rightData = lodash.cloneDeep(this.value || lodash.map(this.userValue || [], (cuv: any) => {
        return {
          ...cuv,
          type: 'user'
        };
      }));
    }
    this.changeActiveKey('1');
  }
  render() {
    return (
      <div>
        <van-action-sheet value={this.visible}
          on-click-overlay={this.cancel}
          round={false}
          class={styles.sheet}
        >
          <van-tabs v-model={this.activeKey}
            on-change={(name: any, title: any) => this.changeActiveKey(name)}>
            <van-tab title='组织结构' name='1'></van-tab>
            <van-tab title='内部分组' name='2'></van-tab>
            <van-tab title='最近联系人' name='3'></van-tab>
          </van-tabs>
          {
            this.activeKey === '1' ? [
              <div class={styles.top}>
                <form action='/'>
                  <van-search
                    v-model={this.searchKw}
                    shape='round'
                    placeholder={this.$l.getLocale('placeholders.searchKw')}
                    on-search={() => this.onLeftSearch()}
                    on-clear={() => this.onLeftSearch()}
                  />
                </form>
              </div>,
              <div class={styles.content}>
                {
                  this.getZZJGHtml((this.searchRylb.length > 0 ? this.searchRylb : this.zzjg), null, 1, this.rightData)
                }
              </div>
            ] : ''
          }
          {
            this.activeKey === '2' ? <div class={styles.content + ' ' + styles.nosearch}>
              {
                this.getZZJGHtml(this.nbfz, null, 1, this.rightData)
              }
            </div> : ''
          }
          {
            this.activeKey === '3' ? <div class={styles.content + ' ' + styles.nosearch}>
              {
                this.getZZJGHtml(this.zjlxr, null, 1, this.rightData)
              }
            </div> : ''
          }
          <div class={styles.bottom}>
            <div class={styles.allUsers}>
              <van-checkbox shape='square'
              value={this.allUsersChecked}
              on-click={(e: any) => this.onAllUsers(e)}
              >公司全体（含董监事）</van-checkbox>
            </div>
          <div class={styles.buttons}>
            <div class={styles.checked}>
              <span>已选{this.getZZRY.length > 0 ? `(${this.getZZRY.length})` : ''}</span>
            </div>
            <van-button type='default'
              class={styles.button + ' ' + styles.clear}
              on-click={this.onImportChecked}>清除</van-button>
            <van-button type='default'
              class={styles.button + ' ' + styles.ok}
              on-click={this.ok}>确定</van-button>
          </div>
          </div>
        </van-action-sheet>
      </div>
    );
  }
}
