.sheet {
  height: 100%;
  max-height: 100% !important;
  text-align: left;

  :global(.van-action-sheet__content) {
    background: #ffffff;
  }

  :global(.van-tabs) {
    border-bottom: 1px solid #ebedf0;
  }

  :global(.van-tabs--line .van-tabs__wrap) {
    height: 40px;
  }

  :global(.van-tabs__nav--line) {
    padding-bottom: 0px;
  }

  :global(.van-tab__text--ellipsis) {
    font-size: inherit !important;
    font-weight: normal !important;
  }

  :global(.van-tabs__line) {
    bottom: 0px;
    background-color: #009a3e;
  }
}

.top {
  width: 100%;
  border-bottom: 1px solid #ebedf0;

  :global(.van-search) {
    padding: 10px 5px;
  }

  :global(.van-search__content) {
    :global(.van-icon-search) {
      font-size: 12px;
    }

    :global(.van-field__control) {
      font-size: 12px;
      color: #999999;
      font-weight: 400px;
    }
  }
}

.content {
  width: 100%;
  overflow: auto;
  overflow-x: hidden;
  position: absolute;
  top: 95px;
  bottom: 115px;

  .row {
    height: 40px;
    line-height: 40px;
    border-bottom: 1px solid #ebedf0;
    display: flex;
    align-items: center;

    .rowexpand {
      flex: 0 0 20px;
      text-align: center;
    }

    .rowprefix {
      flex: 0 0 20px;
      text-align: center;
    }

    .rowlabel {
      margin-left: 10px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      padding-right: 10px;
    }

    .rowlabelhascheckbox {
      padding-right: 35px;
    }

    .rowcheckbox {
      position: absolute;
      right: 10px;
    }

    .rowuser {
      border-radius: 50%;
      background-color: #009a3e;
      line-height: 35px;
      text-align: center;
      color: #ffffff;
      flex: 0 0 35px;
      margin-left: 10px;
    }

    :global(.van-checkbox__icon--disabled.van-checkbox__icon--checked .van-icon) {
      background-color: #ebedf0 !important;
      border-color: #c8c9cc !important;
    }
  }
}

.bottom {
  width: 100%;
  position: absolute;
  bottom: 0px;
  .allUsers {
    padding: 5px 5px 5px 10px;
    border-top: 1px solid #ebedf0;
  }
}
.buttons {
  border-top: 1px solid #ebedf0;
  background-color: #fff;
  display: flex;
  width: 100%;
  line-height: 60px;
  border-radius: 4px;
  padding: 5px 0px 15px;
  align-items: center;

  .checked {
    display: inline-block;
    margin-left: 10px;
    flex: 1px;
    color: #ee0a24;
  }

  .button {
    width: 100px !important;
    margin-right: 10px !important;
    margin-top: 5px !important;
  }

  .ok {
    background-color: #009a3e !important;
    color: #ffffff;
  }

  .clear {
    background-color: #f0f2f5 !important;
    color: #646566;
  }
}

.nosearch {
  top: 41px
}