import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import { userCardService } from './service';
import { PopoverDataDto } from './types';

@Component
export class IndexMixins extends Vue {
    private openId: any;
    @Prop() userInfo!: any;
    visible = false;
    popoverData!: PopoverDataDto;
    @Emit('conversation')
    conversation(userId: string) {
        this.openid(userId);
    }

    private openid(startUserLoginId: string) {
        const param = {
          userId: startUserLoginId
          // userId: 'songyang_hz'
        };
        console.log(param);
        userCardService.getOpenid(param).subscribe(data => {
          console.log(data);
          this.openId = data;
          window.open('https://applink.feishu.cn/client/chat/open?openId=' + this.openId);
        });
      }

    onClose() {
        this.visible = false;
    }
    onClick() {
        userCardService.getUserInfo(this.userInfo.userId).subscribe(data => {
            this.popoverData = data;
            this.mobileFormat();
            this.visible = this.popoverData ? true : false;
        });
    }
    mobileFormat() {
        if (this.popoverData.phoneNumber &&
            this.popoverData.phoneNumber.toString().length === 11) {
            const coNumber = this.popoverData.phoneNumber;
            const newNumber = `${coNumber.substring(0, 3)} ${coNumber.substring(3, 7)} ${coNumber.substring(7, 11)}`;
            this.popoverData.phoneNumber = newNumber;
        }
    }
    created() {
    }
}
