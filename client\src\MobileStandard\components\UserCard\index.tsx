import { mixins } from 'vue-class-component';
import { Component } from 'vue-property-decorator';
import { IndexMixins } from './IndexMixins';
import styles from './user-card.module.less';

@Component
export class UserCard extends mixins(IndexMixins) {
    render() {
        return (
            <div>
                <span on-click={() => this.onClick()}>{this.userInfo.userName}</span>
                <van-popup v-model={this.visible} round>
                    {
                        this.popoverData ?
                            <div class={styles.user}>
                                <div class={styles.top_bj}>
                                    <div class={styles.title}>
                                        <img src={this.popoverData.avatar ? this.popoverData.avatar :
                                            require(`../../../assets/images/app_userpic_default.png`)}
                                            class={this.popoverData.avatar ? '' : styles.user_avator_image}></img>
                                        <div class={styles.title_expand}>
                                            <div class={styles.name_content}>
                                                <span class={styles.name}>{this.popoverData.name}</span>
                                                <img src={require(`../../../assets/images/icon_shenpibuzhou_IMgoutong.png`)}
                                                    class={styles.user_image}
                                                    on-click={() => this.conversation(this.userInfo.userId)} />
                                            </div>
                                            {
                                                this.popoverData.phoneNumber ?
                                                    <a class={styles.mobile}
                                                        href={`tel:${this.popoverData.phoneNumber}`}>
                                                        {this.popoverData.phoneNumber}
                                                    </a> : <div class={styles.mobile} />
                                            }

                                        </div>
                                    </div>
                                    <i class='iconfont icon-pop_btn_close'
                                        style='font-size:23px;'
                                        on-click={() => this.onClose()}></i>
                                </div>
                                {
                                    this.popoverData.email ?
                                        <div class={styles.content}>
                                            <span class={styles.content_label}>{this.$l.getLocale('fields.mailbox')}</span>
                                            <span class={styles.content_value}>{this.popoverData.email}</span>
                                        </div> : ''
                                }
                                <div class={styles.content}>
                                    <span class={styles.content_label}>{this.$l.getLocale('fields.post')}</span>
                                    <span class={styles.content_value}>{this.userInfo.post ? this.userInfo.post :
                                        `${this.popoverData.organizationPath}：${this.popoverData.positionName}`}</span>
                                </div>
                            </div> : ''
                    }
                </van-popup>
            </div>
        );
    }
}
