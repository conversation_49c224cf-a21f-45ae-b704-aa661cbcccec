import { httpHelper } from '@/MobileStandard/common/utils';
import { Observable } from 'rxjs';
import { PopoverDataDto } from './types';

class UserCardService {
  getUserInfo(id: string): Observable<PopoverDataDto> {
    const url = `/api/platform/v1/users/${id}`;
    return httpHelper.get(url, undefined, { loading: false });
  }
  getOpenid(params: any): Observable<any[]> {
    const url = `/api/platform/feishu/openId`;
    return httpHelper.get(url, {params});
  }
}

export const userCardService = new UserCardService();
