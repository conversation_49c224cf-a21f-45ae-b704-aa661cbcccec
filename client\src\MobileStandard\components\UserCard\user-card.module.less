.user {
    background: #ffffff;
    border-radius: 4px;
    box-shadow: 0px 2px 34px 0px rgba(0, 0, 0, 0.24);
    min-height: 280px;
    width: 275px;
    .top_bj {
        height: 104px;
        background-image: url(../../../assets/images/pop_topbg.png);
        background-repeat: round;
        position: relative;

        .title {
            display: flex;
            align-items: center;
            height: 100px;
            margin-left: 36px;

            img {
                height: 50px;
                width: 50px;
                border-radius: 50px;
            }

            .user_avator_image {
                background: #a2deca;
            }

            .title_expand {
                color: #333333;
                margin-left: 10px;
                .name_content {
                    display: flex;
                    align-items: center;
                    .name {
                        font-size: 16px;
                        font-weight: bold;
                        margin-right: 10px;
                    }

                    img {
                        width: 16px;
                        height: 16px;
                        margin-top: 2px;
                        border-radius: 0;
                    }
                }

                .mobile {
                    margin-top: 5px;
                    display: block;
                    font-size: 14px;
                    font-weight: bold;
                    height: 20px;
                    line-height: 20px;
                    color: #333333;
                }
            }
        }

        i {
            position: absolute;
            right: 15px;
            top: 10px;
            cursor: pointer;
        }
    }

    .content {
        margin-top: 20px;
        padding: 0 20px;

        .content_label {
            height: 20px;
            font-size: 13px;
            color: #999999;
            display: block;
            line-height: 20px;
        }

        .content_value {
            min-height: 20px;
            font-size: 12px;
            color: #333333;
            display: block;
            font-weight: normal;
        }
    }
}