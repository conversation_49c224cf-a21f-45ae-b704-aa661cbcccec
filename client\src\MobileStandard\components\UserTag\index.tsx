import { InstanceParamMixin } from '@/MobileStandard/mixins';
import { Component, Vue, Emit, Prop, Watch } from 'vue-property-decorator';
import styles from './user-tag.module.less';
import avatarDefault from '@/assets/images/Avatar_default.png';

@Component
export class UserTag extends Vue {
    @Prop() name!: string;
    @Prop({ default: true }) isDelete!: boolean;
    @Prop() nameStyle!: any;
    @Prop({ default: true }) hasAvatarDefault!: boolean;
    @Emit('delete')
    onDelete() { }
    created() {
    }
    render() {
        return (
            <div class={styles.user + (this.hasAvatarDefault ? ' ' + styles.user_hasAvatarDefault : '')}>
                {
                    this.hasAvatarDefault ? <van-icon name={avatarDefault} size='40'></van-icon> : ''
                }
                <span style={this.nameStyle}>{this.name}</span>
                {
                    this.isDelete ? <van-icon
                        name='close'
                        size='14'
                        class={styles.close}
                        on-click={this.onDelete}></van-icon> : ''
                }
            </div>
        );
    }
}
