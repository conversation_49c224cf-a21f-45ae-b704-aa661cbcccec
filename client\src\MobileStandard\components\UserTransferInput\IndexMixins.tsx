import lodash from 'lodash';
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import { userTransferInputService } from './service';

@Component
export class IndexMixins extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop({ default: true }) multiple!: boolean;
    @Prop() items!: any;
    showUserTransferInput = false;
    showSelectUsers = false;
    searchText = '';
    breadcrumb: any = [{ label: this.$l.getLocale('fields.topLevel'), value: '' }];
    searchOrg: any = [];
    searchUser: any = [];
    selectUsers: any = [];
    parentId = '';
    breadcrumbVisible = true;
    @Watch('visible')
    visibleChange(newVal: boolean) {
        if (newVal) {
            this.searchText = '';
            this.breadcrumb = [{ label: this.$l.getLocale('fields.topLevel'), value: '' }];
            this.selectUsers = JSON.parse(JSON.stringify(this.items));
            this.parentId = '';
            this.breadcrumbVisible = true;
            this.onLoadData();
        }
        this.showUserTransferInput = newVal;
    }
    @Emit('close')
    userTransferInputClose() { }
    @Emit('ok')
    userTransferInputOk(items: any) { }
    onLoadData() {
        this.breadcrumbVisible = true;
        this.searchOrg = [];
        this.searchUser = [];
        userTransferInputService.getDepartmentTreeUser(this.parentId, 1, 1).subscribe((data: any) => {
            this.searchOrg = data.organizations || [];
            this.searchUser = lodash.forEach(data.users || [], (m: any) => {
                m['checked'] = false;
                if (this.selectUsers.findIndex((f: any) => f.userId === m.value && f.fullPathPostion === `${m.organizationNamePath || ''}：${m.positionName || ''}`) > -1) {
                    m['checked'] = true;
                }
            });
        });
    }
    onSearch() {
        if (this.searchText) {
            this.breadcrumbVisible = false;
            this.searchOrg = [];
            this.searchUser = [];
            userTransferInputService.getNewUserList(this.searchText, 1, 1).subscribe((data: any) => {
                this.searchUser = lodash.forEach(data || [], (m: any) => {
                    m['checked'] = false;
                    if (this.selectUsers.findIndex((f: any) => f.userId === m.value && f.fullPathPostion === `${m.organizationNamePath || ''}：${m.positionName || ''}`) > -1) {
                        m['checked'] = true;
                    }
                });
            });
        } else {
            this.onLoadData();
        }
    }
    onBreadcrumbClick(item: any, index: number) {
        this.breadcrumb.splice(index + 1, this.breadcrumb.length - index);
        this.parentId = item.value;
        this.onLoadData();
    }
    onNextOrg(org: any) {
        this.parentId = org.value;
        this.breadcrumb.push({ label: org.label, value: org.value });
        this.onLoadData();
    }
    onSaveClick() {
        this.userTransferInputOk(this.selectUsers);
    }
    onCancelClick() {
        this.userTransferInputClose();
    }
    onSelectUserChange(e: any, item: any) {
        if (this.multiple) {
            const selectUserIndex = this.selectUsers.findIndex((f: any) => f.userId === item.value);
            if (!item.checked && selectUserIndex === -1) {
                this.selectUsers.push({
                    userId: item.value,
                    userName: item.label,
                    userLoginId: item.account || '',
                    userNumber: item.number || '',
                    organizationIdPath: item.organizationIdPath || '',
                    organizationNamePath: item.organizationNamePath || '',
                    positionName: item.positionName || '',
                    fullPathPostion: `${item.organizationNamePath || ''}：${item.positionName || ''}`,
                    email: item.email || '',
                    avatar: item.avatar,
                    mobilePhone: item.mobilePhone || ''
                });
            } else if (!item.checked && selectUserIndex > -1) {
                this.selectUsers[selectUserIndex].userId = item.value;
                this.selectUsers[selectUserIndex].userName = item.label;
                this.selectUsers[selectUserIndex].userLoginId = item.number || '';
                this.selectUsers[selectUserIndex].userNumber = item.number || '';
                this.selectUsers[selectUserIndex].organizationIdPath = item.organizationIdPath || '';
                this.selectUsers[selectUserIndex].organizationNamePath = item.organizationNamePath || '';
                this.selectUsers[selectUserIndex].positionName = item.positionName || '';
                this.selectUsers[selectUserIndex].fullPathPostion = `${item.organizationNamePath || ''}：${item.positionName || ''}`;
                this.selectUsers[selectUserIndex].email = item.email || '';
                this.selectUsers[selectUserIndex].avatar = item.avatar;
                this.selectUsers[selectUserIndex].mobilePhone = item.mobilePhone || '';
            } else if (!!item.checked && selectUserIndex > -1) {
                this.selectUsers.splice(selectUserIndex, 1);
            }
        } else {
            if (!item.checked) {
                this.selectUsers = [];
                this.selectUsers.push({
                    userId: item.value,
                    userName: item.label,
                    userLoginId: item.account || '',
                    userNumber: item.number || '',
                    organizationIdPath: item.organizationIdPath || '',
                    organizationNamePath: item.organizationNamePath || '',
                    positionName: item.positionName || '',
                    fullPathPostion: `${item.organizationNamePath || ''}：${item.positionName || ''}`,
                    email: item.email || '',
                    avatar: item.avatar,
                    mobilePhone: item.mobilePhone || ''
                });
            } else {
                const selectUserIndex = this.selectUsers.findIndex((f: any) => f.userId === item.value);
                if (selectUserIndex > -1) {
                    this.selectUsers.splice(selectUserIndex, 1);
                }
            }
        }
        this.setUserChecked();
    }
    setUserChecked() {
        this.searchUser.map((m: any) => {
            m['checked'] = false;
            if (this.selectUsers.findIndex((f: any) => f.userId === m.value && f.fullPathPostion === `${m.organizationNamePath || ''}：${m.positionName || ''}`) > -1) {
                m['checked'] = true;
            }
        });
    }
    onSelectUserClick() {
        if (this.selectUsers.length > 0) {
            this.showSelectUsers = true;
        }
    }
    onSelectUserClose() {
        this.setUserChecked();
        this.showSelectUsers = false;
    }
    onUserRemove(index: number) {
        this.selectUsers.splice(index, 1);
        this.setUserChecked();
    }
    onSelectAll() {
        lodash.forEach(this.searchUser, (s: any) => this.onSelectUserChange(null, {...s, checked: false}));
    }
    onUnSeelctAll() {
        lodash.forEach(this.searchUser, (s: any) => this.onSelectUserChange(null, {...s, checked: true}));
    }
    onRemoveAll() {
        this.selectUsers = [];
        this.setUserChecked();
    }
    created() {
    }
}
