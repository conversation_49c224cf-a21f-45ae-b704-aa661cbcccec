import { mixins } from 'vue-class-component';
import { Component } from 'vue-property-decorator';
import { IndexMixins } from './IndexMixins';
import styles from './user-transfer-input.module.less';

@Component
export class UserTransferInput extends mixins(IndexMixins) {
    render() {
        return (
            <van-action-sheet v-model={this.showUserTransferInput}
                round={false}
                class={styles.sheet}
                on-click-overlay={this.userTransferInputClose}>
                <div class={styles.top}>
                    <form action='/'>
                        <van-search
                            v-model={this.searchText}
                            shape='round'
                            placeholder={this.$l.getLocale('placeholders.searchKw')}
                            on-search={this.onSearch}
                            on-clear={this.onLoadData}
                            class='van-hairline--bottom'
                        />
                    </form>
                    <div class={styles.breadcrumb + (!this.breadcrumbVisible ? ' ' + styles.breadcrumbHide : '')}>
                        {
                            this.breadcrumb.map((m: any, i: number) => {
                                return <div class={styles.breadcrumbItem +
                                    (i < this.breadcrumb.length - 1 ? ' ' + styles.breadcrumbColor : '')}
                                    on-click={() => this.onBreadcrumbClick(m, i)}>
                                    {
                                        m.label
                                    }
                                    {
                                        i < this.breadcrumb.length - 1 ? ' >' : ''
                                    }
                                </div>;
                            })
                        }
                    </div>
                </div>
                <div class={styles.content + (!this.breadcrumbVisible ? ' ' + styles.contentBreadcrumbHide : '')}>
                    <van-cell-group>
                        {
                            this.searchOrg.map((m: any, i: number) => {
                                return <van-cell title={m.label}>
                                    <template slot='right-icon'>
                                        {
                                            <div class={styles.clusterContent} on-click={() => this.onNextOrg(m)}>
                                                <van-icon name='cluster-o' class={styles.clusterIcon} />
                                                <span>{this.$l.getLocale('fields.lowerLevel')}</span>
                                            </div>
                                        }
                                    </template>
                                </van-cell>;
                            })
                        }
                    </van-cell-group>
                    <van-cell-group>
                        {
                            this.searchUser.map((m: any, i: number) => {
                                return <van-cell title={m.label}>
                                    <template slot='title'>
                                        <van-checkbox
                                            name={m.value}
                                            value={m.checked}
                                            on-click={(e: any) => this.onSelectUserChange(e, m)}
                                        >
                                            {`${this.breadcrumbVisible ? m.label : `${m.label}(${m.organizationNamePath})`}`}
                                        </van-checkbox>
                                    </template>
                                </van-cell>;
                            })
                        }
                    </van-cell-group>
                </div>
                <div class={styles.buttons}>
                    <div class={styles.checked} on-click={this.onSelectUserClick}>
                        <span>  {this.$l.getLocale('fields.selected')}:{this.selectUsers.length}</span>
                        {
                            this.selectUsers.length > 0 ?
                                <van-icon name='arrow-up' /> : ''
                        }
                    </div>
                    {
                        this.multiple ? <div>
                            <van-button type='default' color='#009a3e'
                                block
                                class={styles.small_button}
                                on-click={this.onSelectAll}>全选</van-button>
                            <van-button type='default' color='#ffffff'
                                block
                                class={styles.small_button + ' ' + styles.cancel}
                                on-click={this.onUnSeelctAll}>取消全选</van-button>
                        </div> : ''
                    }
                    <van-button type='default' color='#ffffff'
                        block
                        class={styles.button + ' ' + styles.cancel}
                        on-click={this.onCancelClick}>{this.$l.getLocale('buttons.cancel')}</van-button>
                    <van-button type='default' color='#009a3e'
                        block
                        class={styles.button}
                        on-click={this.onSaveClick}>{this.$l.getLocale('buttons.ok')}</van-button>
                </div>
                <van-action-sheet
                    v-model={this.showSelectUsers}
                    round={false}
                    class={styles.selectSheet}>
                    <div class={styles.top}>
                        <div class={styles.activityInfo}>
                            <span>{this.$l.getLocale('fields.selected')}</span>
                            {
                                this.multiple ? <van-button type='default' color='#ffffff'
                                    block
                                    class={styles.small_button + ' ' + styles.cancel + ' ' + styles.removeAll}
                                    on-click={this.onRemoveAll}>删除全部</van-button> : ''
                            }
                        </div>
                        <van-cell-group border={false}
                            class={styles.selectContent}>
                            {
                                this.selectUsers.map((m: any, i: number) => {
                                    return <van-cell
                                        title={`${m.userName}${m.fullPathPostion &&
                                            m.fullPathPostion.split('：').length > 0 ?
                                            ('(' + m.fullPathPostion.split('：')[0] + ')') : ''}`}
                                        icon='contact'
                                    >
                                        <template slot='right-icon'>
                                            <i class={`iconfont icon-btn_deletelist ${styles.remove}`}
                                                on-click={() => this.onUserRemove(i)} />
                                        </template>
                                    </van-cell>;
                                })
                            }
                        </van-cell-group>
                    </div>
                </van-action-sheet>
            </van-action-sheet>
        );
    }
}
