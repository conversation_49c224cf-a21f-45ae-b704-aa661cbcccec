import { Observable, of, zip } from 'rxjs';
import { httpHelper, toCasedStyleObject } from '@/MobileStandard/common/utils';
import { map, tap } from 'rxjs/operators';

class UserTransferInputService {
    selectOrg(parentId: string) {
        let url = '/api/platform/v1/manage/tree-organizations';
        if (parentId) {
            url += '?parent-id=' + parentId;
        }
        return httpHelper.get(url, {}, { loading: false });
    }
    selectUsers(orgId: string, invalidState: number, workingState: number) {
        const url = `/api/platform/v1/organizations/${orgId}/select-users-status?invalidState=${invalidState}&workingState=${workingState}`;
        return httpHelper.get(url, {}, { loading: false });
    }
    searchUsers(keyword: string, invalidState: number, workingState: number) {
        const url = `/api/platform/v1/select-users-status?keyword=${keyword}&count=20&invalidState=${invalidState}&workingState=${workingState}`;
        return httpHelper.get(url, {}, { loading: false });
    }
    getDepartmentTreeUser(id: any, invalidState: number, workingState: number, allUsers: boolean = true): Observable<any[]> {
        const _url = '/api/platform/v1/manage/tree-organizations-users';
        const params: any = { 'parent-id': id, 'invalidState': invalidState, 'workingState': workingState, 'allUsers': allUsers };
        return httpHelper.get(_url, { params: params }, { loading: false });
    }
    getNewUserList(id: string, invalidState: number, workingState: number, allUsers: boolean = true): Observable<any> {
        // 根据传入的状态找对应的用户
        const _url = '/api/platform/v1/select-users-status-including-posn-and-org';
        const params: any = { keyword: id, count: 20, invalidState: invalidState, workingState: workingState, allUsers: allUsers };
        return httpHelper.get(_url, { params: params }, { loading: false });
    }
    getUserListByOrg2(ids: string, invalidState: number, workingState: number): Observable<any> {
        // const _url = `/api/platform/v1/organizations/${id}/select-users`;
        const _url = `/api/platform/v1/organizations/select-users-status?ids=${ids}&invalidState=${invalidState}&workingState=${workingState}`;
        return httpHelper.get(_url);
    }
    getLaestContact(params: { [key: string]: any }): Observable<any[]> {
        const _url = '/api/platform/v1/latestcontact/new';
        return httpHelper.get(_url, { params: params });
    }
    getUserGroup(params: any): Observable<any> {
        return httpHelper.get(`/api/platform/v1/user-group`, { params: toCasedStyleObject(params) });
    }
}

export const userTransferInputService = new UserTransferInputService();
