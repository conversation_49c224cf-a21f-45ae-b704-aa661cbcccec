import { guidHelper } from '@/MobileStandard/common/utils';
import lodash from 'lodash';
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';

@Component
export class IndexMixins extends Vue {
    @Prop() text!: any;
    baseShowHeight = 60;
    expand = false;
    showExpandBut = false;
    tId = guidHelper.generate();
    @Watch('text', { immediate: true })
    textWatch(newVal: any, oldVal: any) {
        this.$nextTick(() => {
            const offsetHeight = (this.$refs[this.tId] as any).offsetHeight;
            if (offsetHeight > this.baseShowHeight) {
                this.showExpandBut = true;
                this.expand = false;
            }
        });
    }
}
