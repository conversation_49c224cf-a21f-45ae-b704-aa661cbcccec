import { mixins } from 'vue-class-component';
import { Component } from 'vue-property-decorator';
import { IndexMixins } from './IndexMixins';
import styles from './user-transfer-view.module.less';

@Component
export class UserTransferView extends mixins(IndexMixins) {
    render() {
        return (
            <div class={styles.view}>
                <span
                    class={this.showExpandBut && !this.expand ? styles.webkit : styles.block}
                    ref={this.tId}>
                    {this.text}
                </span>
                {
                    this.showExpandBut ? <span
                        class={styles.expandbut}
                        on-click={() => this.expand = !this.expand}
                    >{this.expand ? '收起' : '展开'}</span> : ''
                }
            </div>
        );
    }
}
