import { dateHelper } from '@/MobileStandard/common/utils/date-helper';
import { removeWatermark, setWaterMark } from '@/MobileStandard/common/waterMarks';
import { authService } from '@/MobileStandard/services/auth';
import { Component, Vue } from 'vue-property-decorator';

@Component
export class EmptyLayout extends Vue {
  created() {
    setTimeout(() => {
      if (authService.user.name && authService.user.account) {
        setWaterMark((authService.user.name + ':' + authService.user.account), dateHelper.dateFormat(authService.user.currentTime));
      }
    });
  }
  destroyed() {
    removeWatermark();
  }
  render() {
    return <router-view key={this.$route.fullPath} />;
  }
}
