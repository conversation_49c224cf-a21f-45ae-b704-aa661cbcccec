import { Component, Vue } from 'vue-property-decorator';

@Component
export class InstanceParamMixin extends Vue {
  get isIndependent(): boolean {
    return this.$route.query['independent'] ?
      (this.$route.query['independent'] || false) as boolean : false;
  }

  get pageType(): string {
    const paths = this.$route.path.replace('/', '').split('/');
    return paths ? paths[0] === 'detail' ? paths[1] : paths[0] : '';
  }

  get number(): string {
    return this.$route.query['number'] as string;
  }

  get processId(): string {
    return this.$route.query['process-id'] as string;
  }

  get draftId(): string {
    return this.$route.query['draft-id'] as string;
  }

  get taskId(): string {
    return this.$route.query['task-id'] as string;
  }

  get bsid(): string {
    return this.$route.query['bsid'] as string;
  }

  get btid(): string {
    return this.$route.query['btid'] as string;
  }

  get boid(): string {
    return this.$route.query['boid'] as string;
  }

  get authId(): string {
    return this.$route.query['auth-id'] as string;
  }

  get listType(): string {
    return this.$route.query['listType'] as string;
  }

  get backUrl(): string {
    return this.$route.query['backUrl'] as string;
  }

  get canRecall(): string {
    return this.$route.query['canRecall'] as string;
  }

  get simulate(): boolean {
    return this.$route.query['simulate'] as string === '1';
  }
}
