import { routerGuard } from '@/MobileStandard/common/utils';
import { FilePreview } from '@/MobileStandard/components/DetailPage/FilePreview';
import { EmptyLayout } from '@/MobileStandard/layout/EmptyLayout';
import { InstanceStart } from '@/MobileStandard/views/InstanceStart';
import { InstanceView } from '@/MobileStandard/views/InstanceView';
import { RouteConfig } from 'vue-router';

export const coreFeaturesRoutes: RouteConfig[] = [
  {
    path: 'detail',
    component: EmptyLayout,
    beforeEnter: routerGuard,
    children: [
      {
        path: 'todo',
        component: EmptyLayout,
        beforeEnter: routerGuard,
        children: [
          {
            path: ':number',
            component: InstanceView,
            meta: {
              title: 'detailTodo'
            }
          },
        ]
      },
      {
        path: 'cc-to-me',
        component: EmptyLayout,
        beforeEnter: routerGuard,
        children: [
          {
            path: ':number',
            component: InstanceView,
            meta: {
              title: 'detailCcToMe'
            }
          },
        ],
      },
      {
        path: 'my-processes',
        component: EmptyLayout,
        beforeEnter: routerGuard,
        children: [
          {
            path: ':number',
            component: InstanceView,
            meta: {
              title: 'detailMyProcesses'
            }
          },
        ],
      },
      {
        path: 'done',
        component: EmptyLayout,
        beforeEnter: routerGuard,
        children: [
          {
            path: ':number',
            component: InstanceView,
            meta: {
              title: 'detailDone'
            }
          },
        ],
      }
    ],
    meta: {
      title: 'detailTodo'
    }
  },
  {
    path: 'start',
    component: InstanceStart,
    beforeEnter: routerGuard,
    children: [
      {
        path: ':number',
        component: InstanceStart,
        meta: {
          title: 'start'
        }
      },
    ],
    meta: {
      title: 'start'
    }
  },
  {
    path: 'file-preview',
    component: EmptyLayout,
    children: [
      {
        path: ':fileId',
        component: FilePreview,
      },
    ],
  }
];
