import { RouteConfig } from 'vue-router';
import { routerGuard, routerLogoutGuard, routerLoginGuard } from '@/MobileStandard/common/utils';
import { Login } from '@/MobileStandard/views/Login';
import { UnAuthorized } from '@/MobileStandard/views/401';
import { EmptyLayout } from '../layout/EmptyLayout';
import { TodoCenter } from '../views/TodoCenter';
import { ProcessMap } from '../views/ProcessMap';
import { Test } from '../views/Test';
import { TestFullScreen } from '../views/TestFullScreen';
import { TestFullScreen2 } from '../views/TestFullScreen2';
import { coreFeaturesRoutes } from './core-features/core-features.routes';
import { Home } from '../views/Home';

export const mobileStandardRoutes: RouteConfig[] = [
  {
    path: '/',
    component: EmptyLayout,
    children: [
      {
        path: '/home',
        beforeEnter: routerGuard,
        component: Home,
        meta: {
          title: 'default'
        }
      },
      {
        path: '/todo',
        beforeEnter: routerGuard,
        component: TodoCenter,
        meta: {
          title: 'todo'
        }
      },
      {
        path: '/map',
        beforeEnter: routerGuard,
        component: ProcessMap,
        meta: {
          title: 'map'
        }
      },
      ...coreFeaturesRoutes,
      {
        path: '/test',
        beforeEnter: routerGuard,
        component: Test,
        meta: {
          title: 'test'
        }
      },
      {
        path: '/fullScreen',
        beforeEnter: routerGuard,
        component: TestFullScreen,
        meta: {
          title: 'fullScreen'
        }
      },
      {
        path: '/test2',
        beforeEnter: routerGuard,
        component: TestFullScreen2,
        meta: {
          title: 'fullScreen2'
        }
      },
      { path: '', redirect: 'home', meta: { title: 'home' } },
    ],
    beforeEnter: routerGuard,
    meta: {
      title: 'todo'
    }
  },
  {
    path: '/login',
    beforeEnter: routerLoginGuard,
    component: Login,
    meta: {
      title: 'default'
    }
  },
  {
    path: '/logout',
    beforeEnter: routerLogoutGuard,
    meta: {
      title: 'default'
    }
  },
  {
    path: '/401',
    component: UnAuthorized,
    meta: {
      title: 'default'
    }
  },
  {
    path: '*',
    redirect: '/',
    meta: {
      title: 'default'
    }
  },
];
