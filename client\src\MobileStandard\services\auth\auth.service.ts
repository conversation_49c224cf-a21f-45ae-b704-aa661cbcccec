import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';

import { httpHelper, toCasedStyleObject } from '@/MobileStandard/common/utils';
import { User } from './auth.types';
import { menuService } from '../menu';

class AuthService {
  user: User = {
    currentTime: ''
  };
  logoutUri = '';
  adminUri = '';

  private setAuthState(data: any) {
    this.logoutUri = data.logoutUri;
    delete data.logoutUri;
    this.adminUri = data.adminUri;
    delete data.adminUri;
    this.user = data;

    menuService.getAuthorizedMenus();
  }

  getAuthState() {
    return httpHelper.get('/auth/state').pipe(
      tap(data => {
        this.setAuthState(data);
        return data;
      })
    );
  }

  login(account: string, password: string, nonce: string, code: string): Observable<void> {
    return httpHelper.post('/auth/state',
    { account: account, password: password, nonce: nonce, captcha: code },
    undefined,
    { loading: true });
  }

  logout(): Observable<void> {
    return httpHelper.delete('/auth/state');
  }

  impersonateout(): Observable<void> {
    return httpHelper.delete('/auth/impersonate');
  }

  checkAnonymousInstanceViewState(number: string, taskId: string, token: string): Observable<void> {
    return httpHelper.get(
      `/api/process/v1/instances/${number}/user-state`,
      {
        params: toCasedStyleObject({ taskId: taskId, token: token }),
      },
      {
        loading: false,
      }
    );
  }
  checkTripartiteSystemViewToken(number: string, bsid: string, viewToken: string): Observable<void> {
    return httpHelper.get(
      `/api/process/v1/instances/${number}/view-token`,
      {
        params: toCasedStyleObject({ bsid: bsid, viewToken: viewToken }),
      },
      {
        loading: false,
      }
    );
  }
}

export const authService = new AuthService();
