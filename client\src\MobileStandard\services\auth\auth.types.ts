export interface User {
  currentTime: string;
  id?: string;
  name?: string;
  impersonatorId?: string;
  account?: string;
  workNumber?: string;
  positions?: UserPosition[];
  menus?: MenuItem[];
  featrues?: Featrue[];
}

export interface UserPosition {
  label: string;
  value: string;
  organizationPath: string;
  organizationPathText: string;
  primaryPosition: boolean;
}

export interface Featrue {
  menuPath: string;
  permissions: string[];
}

/**
 * 菜单项
 */
export interface MenuItem {
  id: string;
  path?: string;
  name: string;
  children?: MenuItem[];
  isOpen?: boolean;
  icon?: string;
  route: string;
  hidden?: boolean;
  actionIds?: string[];
}
