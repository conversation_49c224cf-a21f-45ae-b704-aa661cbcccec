import { Observable } from 'rxjs';
import { httpHelper } from '@/MobileStandard/common/utils';
import { InstanceRecordResultDto, ProcessModuleState } from './common.types';
import { ValueLabelPair } from '@/MobileStandard/common/defines';
import { map } from 'rxjs/operators';

class CommonService {
  getProcessFormModuleState(processId: string, taskId: string, number: string): Observable<ProcessModuleState> {
    const url = `/api/form/v1/templates/extension-data`;
    return httpHelper.get(url, { params: { 'process-id': processId, 'task-id': taskId, 'instance-id': number } });
  }
  getDictionaries(params: string[]): Observable<{ [key: string]: ValueLabelPair[] } | ValueLabelPair[]> {
    let paramsString = '';
    params.forEach((item: string, index: number) => {
      paramsString += `${index === 0 ? '?' : '&'}group=${item}`;
    });
    const _url = `/api/platform/v1/select-dictionaries${paramsString}`;
    return httpHelper.get(_url, undefined, { loading: false });
  }
  GetSystemPar(params: any): Observable<any> {
    const _url = `/api/process/v1/manage/process-paramter-modules/getSystemParByProcessTemplateID`;
    return httpHelper.post(_url, params, undefined, { loading: false });
  }
  getApproval(number: string, formdata: any): Observable<InstanceRecordResultDto> {
    // const _url = `/api/process/v1/instances/${number}/steps`;
    const _url = `/api/engine/v1/instances/${number}/steps`;
    return httpHelper.post(_url, formdata, undefined, { loading: false }).pipe(
      map(data => {
        const result: InstanceRecordResultDto = { data: [], branchName: '' };
        result.datum = data;
        result.branchName = data.branchName;
        result.data = data.steps;
        // data.steps.forEach((d: any) => {
        //   const tasks = (d.tasks || []).length > 0 ? d.tasks : [d];
        //   result.data.push(
        //     tasks.map((task: any) => ({
        //       userName: task.userName,
        //       userId: task.userId,
        //       stepName: task.activityName || task.name,
        //       state: task.status || 'todo',
        //       stepType: task.nodeType,
        //       stateName: task.detailStatusName || task.statusName,
        //       stayDuration: (Math.floor((Number(task.elapsedTime) / 3600) * 100) / 100).toString(),
        //       resolveTime: task.finishTime,
        //       commentTextArray: task.comments || [],
        //       resolveUserId: task.resolveUserId,
        //       resolveUserName: task.resolveUserName,
        //       detailStatus: task.detailStatus,
        //       activityResolverName: task.activityResolverName,
        //       activityResolveType: task.activityResolveType
        //     }))
        //   );
        // });

        return result;
      })
    );
  }
}

export const commonService = new CommonService();
