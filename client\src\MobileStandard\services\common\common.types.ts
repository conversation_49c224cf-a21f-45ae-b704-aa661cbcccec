export interface ProcessModuleState {
    baseInfo?: boolean;
    relation?: boolean;
    attachment?: boolean;
    record?: boolean;
    controlPoint?: boolean;
}

/**
 * 审批 状态枚举
 */
export enum ApproveState {
    start = 'start',
    ready = 'ready',
    processing = 'processing',
    approved = 'approved',
    refused = 'refused',
    canceled = 'canceled',
    rejected = 'rejected',
    todo = 'todo',
}
export interface InstanceRecordDto {
    userId?: string;
    userName: string;
    stepName: string;
    state: ApproveState;
    stateName?: string;
    stepType?: string;
    stayDuration?: string;
    resolveTime?: string;
    commentTextArray: string[];
    statusName?: string;
}
export interface InstanceRecordResultDto {
    datum?: any;
    data: InstanceRecordDto[][];
    branchName?: string;
}
