import { Observable, BehaviorSubject } from 'rxjs';

import { httpHelper } from '@/MobileStandard/common/utils';
import { Settings } from '@/MobileStandard/common/defines';
import { authService, Featrue } from '../auth';
import { MenuGroup, MenuViewState } from './menu.types';
import { map } from 'rxjs/operators';

class MenuService {
  modules: MenuGroup[] = [];
  private menus: MenuGroup[] = [];
  private authorizedMenus: MenuGroup[] = [];
  private defaultPage = '';
  private menuViewStateSource: BehaviorSubject<MenuViewState>;

  menuViewState$: Observable<MenuViewState>;

  /**
   * 菜单状态
   */
  get menuViewState(): MenuViewState {
    return MenuViewState.Collapsed;
    // alert(document.body.clientWidth <= Settings.ScreenBoundary);
    // return document.body.clientWidth <= Settings.ScreenBoundary ||
    // localStorage.getItem(Settings.UserMenuViewStateCacheKey) === 'collapsed'
    //   ? MenuViewState.Collapsed
    //   : MenuViewState.Expanded;
  }

  constructor() {
    // 初始化菜单显示状态
    this.menuViewStateSource = new BehaviorSubject<MenuViewState>(this.menuViewState);
    this.menuViewState$ = this.menuViewStateSource.asObservable();
  }

  /**
   * 切换菜单伸缩状态
   * @param menuViewState 菜单伸缩状态
   */
  switchMenuViewState(menuViewState: MenuViewState) {
    localStorage.setItem(Settings.UserMenuViewStateCacheKey, menuViewState);
    this.menuViewStateSource.next(menuViewState);
  }

  updateAuthorization(features: Featrue[] | undefined) {
    // debugger
    if (features) {
      if (this.authorizedMenus.length > 0) {
        this.defaultPage = this.authorizedMenus[0].children[0].route;
      }
    } else {
      this.authorizedMenus = [];
      this.defaultPage = '';
    }
  }

  getAuthorizedMenus() {
    this.modules = [];
    this.authorizedMenus = [];
    const menus: MenuGroup[] = [];
    if (authService.user && authService.user.menus && (authService.user.menus as []).length > 0) {
      (authService.user.menus as []).forEach((first: any) => {
        this.modules.push({
          key: `${first.path}`,
          name: first['name'],
          icon: first.icon,
          children: [],
        });
        if ((first.children || []).length > 0) {
          first.children.forEach((second: any) => {
            menus.push({ key: `${first.path}${second.path}`, name: second.name, icon: second.icon, children: [] });
          });
        }
      });
      this.authorizedMenus = menus;
    }
    return menus;
  }

  getDefaultPage(): string {
    if (this.defaultPage) {
      return this.defaultPage;
    }

    return '/401';
  }

  getCurrentPageKeys(path: string): string[] {
    const menuGroup = this.authorizedMenus.find(m => m.children.some(c => c.route === path));

    if (menuGroup) {
      const page = menuGroup.children.find(c => c.route === path);
      if (page) {
        return [menuGroup.key, page.key];
      }
    }

    return ['', ''];
  }

  getBreadcrumbs(path: string): string[] {
    const breadcrumbs: string[] = [];
    // 筛选页面
    const menuGroup = this.authorizedMenus.find(m => m.children.some(c => c.route === path));

    if (menuGroup) {
      const page = menuGroup.children.find(c => c.route === path);
      if (page) {
        breadcrumbs.push(menuGroup.name);
        breadcrumbs.push(page.name);
      }
    }

    return breadcrumbs;
  }

  getMenuNumbers(params: any): Observable<any> {
    // const url = '/api/process/v1/my-workspace/menu-numbers';
    const url = '/api/todo-centre/v1/global/allTotal';
    return httpHelper.get(url,  { params: params },  { loading: false });
  }
  getCurrentTime(): Observable<any> {
    // debugger
    const _url = `/api/process/v1/manage/systems/currentTime`;
    return httpHelper.get(_url, {});
  }
  openButton(id: any): Observable<any> {
    const _url = `/api/platform/v1/check-process-query-authorization?currentUserId=` + id;
    return httpHelper.get(_url, {});
  }
}

export const menuService = new MenuService();
