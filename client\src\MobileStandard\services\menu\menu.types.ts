/**
 * 菜单组
 */
export interface MenuGroup {
  /**
   * key
   */
  key: string;
  /**
   * 名称
   */
  name: string;
  /**
   * 图标
   */
  icon: string;
  /**
   * 页面菜单
   */
  children: PageMenu[];
}

/**
 * 页面菜单
 */
export interface PageMenu {
  /**
   * key
   */
  key: string;
  /**
   * 页面名称
   */
  name: string;
  /**
   * 页面路由
   */
  route: string;
}

/**
 * 菜单视图状态
 */
export enum MenuViewState {
  /**
   * 展开的
   */
  Expanded = 'expanded',
  /**
   * 收起的
   */
  Collapsed = 'collapsed',
}
