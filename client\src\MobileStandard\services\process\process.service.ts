import { httpHelper } from '@/MobileStandard/common/utils';
import { Observable } from 'rxjs';

import { ProcessInfoDto, ProcessModuleState, ProcDto } from './process.types';

class ProcessService {
  getProcessIdByBTID(bsid: string, btid: string, boid: string): Observable<ProcDto[]> {
    const _url = `/api/process/v1/processes/ids`;
    return httpHelper.get(_url, { params: { bsid, btid, boid } });
  }

  getFieldRight(params: any): Observable<any> {
    const _url = `/api/process/v1/field-right`;
    return httpHelper.get(_url, { params: params });
  }

  GetSystemPar(params: any): Observable<any> {
    const _url = `/api/process/v1/manage/process-paramter-modules/getSystemParByProcessTemplateID`;
    return httpHelper.post(_url, params, undefined, { loading: false });
  }

  getNumberByBTID(bsid: string, btid: string, boid: string): Observable<string> {
    const _url = `/api/engine/v1/instances/number`;
    return httpHelper.get(_url, { params: { bsid, btid, boid } });
  }

  getProcessFormModuleState(processId: string, taskId: string, number: string): Observable<ProcessModuleState> {
    const url = `/api/form/v1/templates/extension-data`;
    return httpHelper.get(url, { params: { 'process-id': processId, 'task-id': taskId, 'instance-id': number } });
  }

  getProcessInfo(id: string): Observable<ProcessInfoDto> {
    const url = `/api/process/v1/processes/${id}/info`;
    return httpHelper.get(url);
  }

  /**
   * 获取流程关键参数
   * @param processId 流程id
   * @param instanceId 示例编号
   * @param authId 授权id
   * @param data 表单数据
   * @param btid btid
   * @returns
   */
  getProcessMainParams(processId: string, instanceId: string, authId: string, btid: string = ''): Observable<any[]> {
    const _url = `/api/engine/v1/process-main-params`;
    return httpHelper.get(
      _url,
      {
        params: {
          'process-id': processId,
          'instance-number': instanceId,
          'btid': btid,
          'auth-id': authId,
        },
      },
      { loading: false }
    );
  }
}

export const processService = new ProcessService();
