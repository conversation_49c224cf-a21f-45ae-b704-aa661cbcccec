export interface ProcessDto {
  id: string;
  name: string;
  groupName?: string;
  isFavorite: boolean;
  authId: string;
  businessUrl: string;
  businessMobileUrl: string;
}

export interface ProcessInfoDto {
  name?: string;
  introduction?: string;
  btid?: string;
  processVersion?: string;
  businessTypeName?: string;
}

export interface ProcessModuleState {
  baseInfo?: boolean;
  relation?: boolean;
  attachment?: boolean;
  record?: boolean;
  controlPoint?: boolean;
  comment?: boolean;
  document?: boolean;
}

export interface ProcDto {
  id: string;
  processName: string;
  authId: string;
}
