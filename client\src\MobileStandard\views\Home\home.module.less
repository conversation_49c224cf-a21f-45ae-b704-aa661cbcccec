.home {
  background-image: url(@/assets/images/home_top1.png);
  height: 180px;
  background-size: cover;
  background-repeat: no-repeat;
  text-align: center;

  .title {
    padding-top: 56px;
    font-size: 30px;
    color: #fcf194;
    font-weight: 700;
  }
  .title1 {
    color: #fff;
    font-size: 16px;
    margin-top: 3px;
  }
}

.content {
  border-radius: 15px 15px 0px 0px;
  margin-top: -20px;
  background: white;
  min-height: 300px;

  .title {
    padding: 27px 21px 21px 23px;
    font-size: 18px;
    font-weight: 700;
  }

  .module {
    display: inline-block;
    font-size: 20px;
    height: 50px;

    .name {
      position: relative;
      top: -3px;
    }

    .icon {
      margin-left: 20px;
      color: #307af7;
      span {
        font-size: 28px;
      }
    }

    div {
      display: initial;
    }
  }

  :global(.van-grid-item__content) {
    background-color: #f3f4f9;
    border-radius: 5px;
    color: #46454a;
    line-height: 50px;
  }
}
