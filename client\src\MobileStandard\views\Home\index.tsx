import { removeWatermark } from '@/MobileStandard/common/waterMarks';
import { MenuGroup, menuService } from '@/MobileStandard/services/menu';
import { Component, Vue } from 'vue-property-decorator';
import styles from './home.module.less';

@Component
export class Home extends Vue {
  private processCenterKey = '00000000-0000-0000-0000-000000000000';
  private menuItems: any = [
    { moduleId: this.processCenterKey, moduleName: '流程中心'},
  ];

  created() {
    this.initModule();
  }

  mounted() {
    setTimeout(() => {
      removeWatermark();
    });
  }

  private initModule() {
    this.menuItems = this.menuItems.concat(menuService.modules.map((m: any) => ({
      moduleId: m.key, moduleName: m.name
    })));
  }

  private moduleChange(menuItem: any) {
    if (menuItem.moduleId === this.processCenterKey) {
      this.$router.push({ path: '/todo', replace: true });
    } else {
      window.location.href = '/' + menuItem.moduleId;
    }
  }

  render() {
    return (
      <div>
        <div class={styles.home}>
          <div class={styles.title}>赛伍客户服务平台</div>
          <div class={styles.title1}>助力组织的数字化转型</div>
        </div>
        <div class={styles.content}>
          <div class={styles.title}>数字化功能模块</div>
          <van-grid column-num='2' border={false} gutter='20px'>
            {this.menuItems.map((mi: any) => {
              return (
                <van-grid-item on-click={() => {
                  this.moduleChange(mi);
                }}>
                  <div class={styles.module}>
                    <div class={styles.name}>{mi.moduleName}</div>
                    <div class={styles.icon}>
                      <span class='iconfont icon-sub_icon_103'></span>
                    </div>
                  </div>
                </van-grid-item>
              );
            })}
          </van-grid>
        </div>
      </div>
    );
  }
}
