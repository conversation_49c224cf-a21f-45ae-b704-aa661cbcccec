import { guid<PERSON><PERSON>per, subjectHelper } from '@/MobileStandard/common/utils';
import {
  ApprovalComment,
  BaseInfo,
  FormInfo,
  ProcessDeduction,
  ProcessDeductionParallel,
  RelevantAttachment,
  RelevantProcess,
  Document
} from '@/MobileStandard/components';
import { ApproveState } from '@/MobileStandard/components/DetailPage/BaseInfo/type';
import { authService } from '@/MobileStandard/services/auth';
import { ProcDto, ProcessInfoDto, ProcessModuleState, processService } from '@/MobileStandard/services/process';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import lodash from 'lodash';
import { Dialog } from 'vant';
import { Component, Vue } from 'vue-property-decorator';
import { ProcessChoose } from './ProcessChoose';
import { instanceStartService } from './service';

@Component({
  components: {
    ProcessChoose
  }
})
export class IndexMixins extends Vue {
  tabActive = 'base';
  urlParamteres: UrlParametersDto = {};
  processInfo: ProcessInfoDto = {};
  moduleState: ProcessModuleState = {
    baseInfo: true,
    relation: true,
    attachment: true,
    record: true,
    comment: true,
    document: false
  };
  isParalle = true;
  isDeductioning = false;
  hasBranch = false;
  buttonHide = false;
  isAnchorClick = false;
  anchorTop = 0;
  processChooseVisible = false;
  dataProcessArr: ProcDto[] = [];
  businessNumber = '';
  modalVisible = true;
  isFirstGetBaseInfo = true;
  comment = '';
  gwObject = {
    visible: false,
    fileId: ''
  };
  hasAction = false;
  onHasAction(isHas: boolean) {
    this.hasAction = isHas;
  }
  getPageType() {
    const path = this.$route.path.replace('/independent', '').replace('/', '');
    return path ? path.split('/')[0] : '';
  }
  onProcessDeductionChange(hasBranch: boolean, isDeductioning: boolean) {
    this.hasBranch = hasBranch;
    this.isDeductioning = isDeductioning;
  }
  getProcessFormModuleState(processId: string, number: string) {
    processService.getProcessFormModuleState(processId, '',
      number).subscribe(state => {
        this.moduleState = state;
      });
  }
  getProcessInfo() {
    processService.getProcessInfo(this.urlParamteres.processId || '').subscribe(data => {
      subjectHelper.getSubject('processInfo$').next(data);
    });
  }
  onActionSubmit(actionCode: string, actionName: string, actionData: any) {
    let jbxx_st: any = { isSuccess: false, data: null };
    jbxx_st = (this.$refs.baseInfo as BaseInfo).getValues();
    switch (actionCode) {
      case 'start':
        if (!this.hasBranch) {
          this.$toast({
            type: 'fail',
            forbidClick: true,
            message: this.$l.getLocale('tips.hasBranch')
          });
          return;
        }
        const spyj_st = (this.$refs.approvalComment as ApprovalComment).getValues();
        // if (this.urlParamteres.number && !spyj_st) {
        //   this.$toast({
        //     type: 'fail',
        //     forbidClick: true,
        //     message: '请填写发起备注'
        //   });
        //   return;
        // }
        let gwwj_st: any = [];
        if (this.moduleState.document) {
          gwwj_st = (this.$refs.document as Document).getValues();
          if (!gwwj_st || gwwj_st.length === 0) {
            this.$toast({
              type: 'fail',
              forbidClick: true,
              message: '请上传公文正文'
            });
            return;
          }
        }
        this.buttonHide = true;
        // 获取所有信息
        (this.$refs.baseInfoForm as any).validate().then(() => {
          const formInfo = this.$refs.formInfo as FormInfo;
          (formInfo.instance.$refs.coreInfoForm as any).validate().then(() => {
            let bdxx_st: any = { isSuccess: false, data: null };
            let lcty_st: any = { isSuccess: false, data: null, limitedMsg: null };
            if (this.isParalle) {
              lcty_st = (this.$refs.processDeductionParallel as ProcessDeductionParallel).getValues('start');
            } else {
              lcty_st = (this.$refs.processDeduction as ProcessDeduction).getValues('start');
            }
            const xglc_st = (this.$refs.relevantProcess as RelevantProcess).getValues();
            const xgfj_st = (this.$refs.relevantAttachment as RelevantAttachment).getValues();
            formInfo.getValues(true, 'approve').subscribe(data => {
              bdxx_st = data;
              if (jbxx_st.isSuccess && jbxx_st.data && bdxx_st.isSuccess && lcty_st.isSuccess) {
                if (lcty_st.limitedMsg !== null && lcty_st.limitedMsg.length > 0) {
                  Dialog.confirm({
                    title: this.$l.getLocale('fields.reminder'),
                    message: lodash.join(lcty_st.limitedMsg, '\n'),
                    confirmButtonText: this.$l.getLocale('buttons.continue-start')
                  })
                    .then(() => {
                      this.startBefor(jbxx_st, bdxx_st, spyj_st, xglc_st, lcty_st, xgfj_st, gwwj_st);
                    })
                    .catch(() => {
                      this.buttonHide = false;
                    });
                } else {
                  this.startBefor(jbxx_st, bdxx_st, spyj_st, xglc_st, lcty_st, xgfj_st, gwwj_st);
                }
              } else {
                this.buttonHide = false;
              }
            }, () => this.buttonHide = false);
          }, (error: any) => {
            if (error && error.length > 0) {
              this.$toast({
                type: 'fail',
                forbidClick: true,
                message: error[0].message
              });
            }
            this.buttonHide = false;
          });
        }, () => this.buttonHide = false);
        break;
      case 'save':
        this.buttonHide = true;
        // 获取所有信息
        let jbxx_s: any = { isSuccess: false, data: null };
        let bdxx_s: any = { isSuccess: false, data: null };
        let lcty_s: any = { isSuccess: false, data: null };
        jbxx_s = (this.$refs.baseInfo as BaseInfo).getValues();
        if (this.isParalle) {
          lcty_s = (this.$refs.processDeductionParallel as ProcessDeductionParallel).getValues('save');
        } else {
          lcty_s = (this.$refs.processDeduction as ProcessDeduction).getValues('save');
        }
        const xglc_s = (this.$refs.relevantProcess as RelevantProcess).getValues();
        const xgfj_s = (this.$refs.relevantAttachment as RelevantAttachment).getValues();
        const spyj_s = (this.$refs.approvalComment as ApprovalComment).getValues();
        const gwwj_s = this.moduleState.document ? (this.$refs.document as Document).getValues() : [];
        (this.$refs.formInfo as FormInfo).getValues(false).subscribe(data => {
          bdxx_s = data;
          if (jbxx_s.isSuccess && jbxx_s.data && bdxx_s.isSuccess && lcty_s.isSuccess) {
            const saveData: any = {
              id: this.urlParamteres.draftId,
              processId: jbxx_s.data.processId,
              parentProcessId: jbxx_s.data.parentProcessId,
              topic: jbxx_s.data.topic,
              userId: jbxx_s.data.userId,
              userName: jbxx_s.data.userName,
              haveAttachments: xgfj_s && xgfj_s.length > 0,
              data: {
                processName: jbxx_s.data.processName,
                moduleState: this.moduleState,
                baseInfo: jbxx_s.data,
                formData: bdxx_s.data,
                relationInstances: xglc_s,
                instanceAttachments: xgfj_s,
                approvalComments: spyj_s,
                previewData: lcty_s.data.steps,
                isKeyWordsChange: lcty_s.data.isKeyWordsChange,
                instanceDocument: gwwj_s
              },
              authId: this.urlParamteres.authId
            };
            console.log('保存数据:', saveData);
            instanceStartService.save(saveData).subscribe(s => {
              this.$toast({
                type: 'success',
                forbidClick: true,
                message: this.$l.getLocale('tips.saveDraftSuccess'),
                onClose: () => {
                  this.$router.push({ path: '/todo?tabActive=cg' });
                }
              });
            });
          } else {
            this.buttonHide = false;
          }
        }, () => this.buttonHide = false);
        break;
      case 'cancel':
        this.buttonHide = true;
        if (jbxx_st.isSuccess && jbxx_st.data &&
          this.urlParamteres.number && this.urlParamteres.todoId) {
          const updateStatusPars = {
            status: 9,
            taskId: this.urlParamteres.todoId,
            systemCode: 'BPM',
            businessID: jbxx_st.data.instanceId,
            businessNumber: this.urlParamteres.number
          };
          instanceStartService.updateStatus(updateStatusPars).subscribe(() => {
            this.cancel(actionData);
          }, () => this.buttonHide = false);
        } else {
          this.cancel(actionData);
        }
        break;
      case 'delete':
        this.buttonHide = true;
        if (jbxx_st.isSuccess && jbxx_st.data && this.urlParamteres.draftId) {
          const deleteParams = {
            isCallback: true,
            systemCode: 'BPM',
            businessID: this.urlParamteres.draftId,
            businessNumber: this.urlParamteres.number,
            userLoginId: authService.user.account,
            userName: authService.user.name
          };
          instanceStartService.deleteTodoDraft(deleteParams).subscribe(s => {
            this.$toast({
              type: 'success',
              forbidClick: true,
              message: this.$l.getLocale('tips.deleteDraftSuccess'),
              onClose: () => {
                this.$router.push({ path: '/todo' });
              }
            });
          }, () => this.buttonHide = false);
        } else {
          this.buttonHide = false;
        }
        break;
    }
  }
  startBefor(jbxx_st: any, bdxx_st: any, spyj_st: any, xglc_st: any, lcty_st: any, xgfj_st: any, gwwj_st: any) {
    if (this.urlParamteres.number && this.urlParamteres.todoId) {
      const updateStatusPars = {
        status: 9,
        taskId: this.urlParamteres.todoId,
        systemCode: 'BPM',
        businessID: jbxx_st.data.instanceId,
        businessNumber: this.urlParamteres.number
      };
      instanceStartService.updateStatus(updateStatusPars).subscribe(() => {
        this.start(jbxx_st, bdxx_st, spyj_st, xglc_st, lcty_st, xgfj_st, gwwj_st);
      }, () => this.buttonHide = false);
    } else {
      this.start(jbxx_st, bdxx_st, spyj_st, xglc_st, lcty_st, xgfj_st, gwwj_st);
    }
  }
  start(jbxx_st: any, bdxx_st: any, spyj_st: any, xglc_st: any, lcty_st: any, xgfj_st: any, gwwj_st: any) {
    const startData: any = {
      processId: jbxx_st.data.processId,
      parentProcessId: jbxx_st.data.parentProcessId,
      topic: jbxx_st.data.topic,
      startUserId: jbxx_st.data.userId,
      startUserName: jbxx_st.data.userName,
      startUserAccount: jbxx_st.data.userAccount,
      params: { ...Object.assign(jbxx_st.data, bdxx_st.data) },
      positionId: jbxx_st.data.positionId,
      positionName: jbxx_st.data.positionName,
      organizationPath: jbxx_st.data.organizationPath,
      organizationNamePath: jbxx_st.data.organizationPathText,
      approvalComments: spyj_st,
      instanceRelations: xglc_st,
      instanceAttachments: xgfj_st,
      previewStepChanges: lcty_st.data.steps,
      bsid: this.urlParamteres.bsid,
      btid: this.urlParamteres.btid,
      boid: this.urlParamteres.boid,
      authId: this.urlParamteres.authId,
      simulate: this.urlParamteres.simulate,
      isAllNode: lcty_st.data.isAllNode,
      fixedProcessUsers: lcty_st.data.fixedProcessUsers,
      category: jbxx_st.data.category,
      isKeyWordsChange: lcty_st.data.isKeyWordsChange,
      instanceDocument: gwwj_st
    };
    if (this.urlParamteres.number) {
      startData['instanceNumber'] = this.urlParamteres.number;
    }
    console.log('发起数据:', startData);
    instanceStartService.start(startData).subscribe(s => {
      this.$toast({
        type: 'success',
        forbidClick: true,
        message: this.$l.getLocale('tips.startSuccess'),
        onClose: () => {
          if (this.urlParamteres.draftId) {
            instanceStartService.deleteDraft(this.urlParamteres.draftId)
              .subscribe(() => this.$router.push({ path: '/todo' }),
                () => this.$router.push({ path: '/todo' }));
          } else {
            if (this.urlParamteres.backUrl) {
              window.location.href = this.urlParamteres.backUrl;
            } else {
              this.$router.push({ path: '/todo' });
            }
          }
        }
      });
    });
  }
  cancel(actionData: any) {
    if (this.urlParamteres.todoId && this.urlParamteres.todoId !== guidHelper.empty()) {
      instanceStartService.cancel(this.urlParamteres.todoId, actionData).subscribe(() => {
        this.$toast({
          type: 'success',
          forbidClick: true,
          message: this.$l.getLocale('tips.cancelSuccess'),
          onClose: () => {
            this.$router.push({ path: '/todo' });
          }
        });
      }, () => this.buttonHide = false);
    } else if (this.urlParamteres.number) {
      instanceStartService.instanceCancel(this.urlParamteres.number, actionData).subscribe(() => {
        this.$toast({
          type: 'success',
          forbidClick: true,
          message: this.$l.getLocale('tips.cancelSuccess'),
          onClose: () => {
            this.$router.push({ path: '/todo' });
          }
        });
      }, () => this.buttonHide = false);
    } else {
      this.buttonHide = false;
    }
  }
  onTabsChange(name: any, title: any) {
    if (name === 'document') {
      this.gwObject.visible = true;
    } else {
      this.gwObject.visible = false;
    }
  }
  onDocumentClick(fileId: any, isGo: boolean) {
    this.gwObject.fileId = fileId;
    if (isGo) {
      this.gwObject.visible = true;
    }
    if (!fileId) {
      (this.$refs.webOffice as any).officeDestroy();
    }
  }
  onWebOfficeRename(rename: string) {
    (this.$refs.document as any).rename(rename);
  }
  beforeCreate() {
    // instanceStartService.getConfig().subscribe(data => {
    //   if (data) {
    //     if (data['IsParalle']) {
    //       this.isParalle = data['IsParalle'];
    //     }
    //   }
    // });
  }
  created() {
    this.urlParamteres.number = this.$route.params['number'] as string;
    this.urlParamteres.draftId = this.$route.query['draft-id'] as string;
    this.urlParamteres.processId = this.$route.query['process-id'] as string;
    this.urlParamteres.boid = this.$route.query['boid'] as string;
    this.urlParamteres.btid = this.$route.query['btid'] as string;
    this.urlParamteres.bsid = this.$route.query['bsid'] as string;
    this.urlParamteres.taskId = this.$route.query['task-id'] as string;
    this.urlParamteres.authId = this.$route.query['auth-id'] as string;
    this.urlParamteres.todoId = this.$route.query['todo-id'] as string;
    this.urlParamteres.simulate = (this.$route.query['simulate'] as string) === '1';
    this.urlParamteres.movitech_token = this.$route.query['movitech_token'] as string;
    this.urlParamteres.pageType = this.getPageType();
    this.urlParamteres.postId = this.$route.query['post-id'] as string;
    this.urlParamteres.isIndependent = this.$route.path.startsWith('/independent');
    this.urlParamteres.againStartNumber = this.$route.query['again-start-number'] as string;
    this.urlParamteres.backUrl = this.$route.query['backUrl'] as string;
    subjectHelper.getSubject('baseInfo$').subscribe((s: any) => {
      if (this.isFirstGetBaseInfo) {
        this.isFirstGetBaseInfo = false;
        this.getProcessFormModuleState(s.parentProcessId, this.urlParamteres.number || '');
        // 待办需要消
        if (s.status === ApproveState.rejected) {
          if (this.urlParamteres.todoId) {
            const data = {
              taskId: this.urlParamteres.todoId,
              systemCode: 'BPM',
              businessID: s.instanceId,
              businessNumber: this.urlParamteres.number
            };
            instanceStartService.taskReadyById(data).subscribe();
          }
          /// 只有BPM的流程，再退回发起的时候，才需要更新发起待办。非BPM在退回时，不创建发起人的待办
          if (String(this.urlParamteres.bsid || '').toLowerCase() === 'bpm') {
            instanceStartService.taskReadInEngine(s.instanceId).subscribe();
          }
        }
      }
      if (this.urlParamteres.number && s.status !== ApproveState.ready && s.status !== ApproveState.rejected) {
        this.buttonHide = true;
      }
    });
  }
  mounted() {
    if (this.urlParamteres.draftId && !this.urlParamteres.number &&
      !this.urlParamteres.againStartNumber) {
      // 获取草稿信息,没有实例也没有再次发起实例
      instanceStartService.getDraft(this.urlParamteres.draftId).subscribe(data => {
        this.urlParamteres.processId = data.processId;
        subjectHelper.getSubject('draftInfo$').next(data);
      });
    } else if (this.urlParamteres.processId && !this.urlParamteres.number) {
      // 根据processid获取流程信息,没有实例也没有再次发起实例
      this.getProcessInfo();
    } else if (this.urlParamteres.boid &&
      this.urlParamteres.btid &&
      this.urlParamteres.bsid && !this.urlParamteres.number &&
      !this.urlParamteres.againStartNumber) {
      // 业务系统发起,没有实例也没有再次发起实例
      processService.getProcessIdByBTID(this.urlParamteres.bsid,
        this.urlParamteres.btid, this.urlParamteres.boid).subscribe(s => {
          if (s.length === 0) {
            this.$toast({
              type: 'fail',
              forbidClick: true,
              message: `${this.$t('instance.notice.matching-process-fail')}`,
            });
          } else if (s.length === 1) {
            this.urlParamteres.authId = s[0].authId;
            this.urlParamteres.processId = s[0].id;
            this.getProcessInfo();
          } else {
            this.dataProcessArr = s;
            // 加载出所有返回的流程，供用户选择
            this.processChooseVisible = true;
          }
        });
      processService.getNumberByBTID(this.urlParamteres.bsid,
        this.urlParamteres.btid,
        this.urlParamteres.boid).subscribe(s => {
          if (s) {
            this.businessNumber = s;
            if (this.businessNumber) {
              Dialog.alert({
                message: '该流程已成功发起。',
              }).then(() => {
              });
            }
          }
        });
    }
  }
}
