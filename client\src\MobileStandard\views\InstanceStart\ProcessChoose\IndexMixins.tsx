import lodash from 'lodash';
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';

@Component
export class IndexMixins extends Vue {
    @Prop({ default: false }) visible!: boolean;
    @Prop() dataProcess!: any;
    radio = '';
    @Emit('processid')
    processid(v: string, authId: string) { }
    onRadioClick(name: any) {
        const item = lodash.find(this.dataProcess, (f: any) => f.id === name);
        if (item) {
            this.processid(item.id, item.authId);
        }
    }
}
