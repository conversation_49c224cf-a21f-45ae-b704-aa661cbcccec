import { Component } from 'vue-property-decorator';
import styles from './process-choose.module.less';
import { IndexMixins } from './IndexMixins';
import { mixins } from 'vue-class-component';
import lodash from 'lodash';

@Component
export class ProcessChoose extends mixins(IndexMixins) {
    render() {
        return (
            <van-dialog value={this.visible}
                show-confirm-button={false}>
                <div class={styles.main_content}>
                    <van-radio-group v-model={this.radio}
                        on-change={(name: string) => this.onRadioClick(name)}>
                        {
                            lodash.map(this.dataProcess, (d: any, di: number) => {
                                return [<div class={styles.group}>
                                    <van-radio name={d.id} />
                                    <div class={styles.process}>
                                        <div>
                                            <span>流程名称：</span>
                                            <span>{d.processName}</span>
                                        </div>
                                        <div>
                                            <span>流程编号：</span>
                                            <span>{d.id}</span>
                                        </div>
                                    </div>
                                </div>, di >= this.dataProcess.length - 1 ? '' :
                                <van-divider />];
                            })
                        }
                    </van-radio-group>
                </div>
            </van-dialog>
        );
    }
}
