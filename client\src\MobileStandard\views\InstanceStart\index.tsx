import { Component } from 'vue-property-decorator';
import styles from './process-start.module.less';
import { IndexMixins } from './IndexMixins';
import { mixins } from 'vue-class-component';

@Component
export class InstanceStart extends mixins(IndexMixins) {
  render() {
    return (
      <div class={styles.start_content}
        id='approval'>
        <van-tabs
          v-model={this.tabActive}
          sticky
          scrollspy
          color='#ffffff'
          background='#009a3e'
          line-height='3'
          line-width='25'
          title-active-color='#ffffff'
          title-inactive-color='#ffffff'
          on-change={(name: any, title: any) => this.onTabsChange(name, title)}
        >
          <van-tab
            name='base'
            title={this.$l.getLocale('fields.baseInfo')}>
            <div class={styles.content}
              style={{ display: !this.gwObject.visible ? 'block' : 'none' }}>
              <van-form ref='baseInfoForm'>
                <base-info
                  urlParamteres={this.urlParamteres}
                  visible={this.moduleState.baseInfo}
                  ref='baseInfo'
                />
              </van-form>
            </div>
          </van-tab>
          <van-tab
            name='core'
            title={this.$l.getLocale('fields.approvalInfo')}>
            <div class={styles.content}
              style={{ display: !this.gwObject.visible ? 'block' : 'none' }}>
              <form-info
                urlParamteres={this.urlParamteres}
                class={styles.content_instance}
                ref='formInfo'
              />
              {
                this.moduleState.document ?
                  <document
                    urlParamteres={this.urlParamteres}
                    visible={this.moduleState.document}
                    delete={this.hasAction && !this.buttonHide}
                    on-document-click={(filedId: any, isGo: boolean) => this.onDocumentClick(filedId, isGo)}
                    ref='document'
                  /> : ''
              }
              <relevant-attachment
                urlParamteres={this.urlParamteres}
                visible={this.moduleState.attachment}
                editable={this.hasAction && !this.buttonHide}
                class={styles.content_instance}
                ref='relevantAttachment'
              />
              <relevant-process
                urlParamteres={this.urlParamteres}
                visible={this.moduleState.relation}
                editable={true}
                class={styles.content_instance}
                ref='relevantProcess'
              />
              {
                this.urlParamteres.number ? this.isParalle ?
                  <approval-record-parallel
                    urlParamteres={this.urlParamteres}
                    visible={this.moduleState.record}
                    ref='approvalRecordParallel'
                    class={styles.content_instance}
                  /> :
                  <approval-record
                    urlParamteres={this.urlParamteres}
                    visible={this.moduleState.record}
                    ref='approvalRecord'
                    class={styles.content_instance}
                  /> : ''
              }
            </div>
          </van-tab>
          <van-tab
            name='approval'
            title={this.$l.getLocale('fields.ProcessDeduction')}>
            <div class={styles.content}
              style={{ display: !this.gwObject.visible ? 'block' : 'none' }}
            >
              {this.isParalle ?
                (
                  <process-deduction-parallel
                    urlParamteres={this.urlParamteres}
                    class={styles.content_instance}
                    ref='processDeductionParallel'
                    on-change={(hasBranch: boolean, isDeductioning: boolean) => this.onProcessDeductionChange(hasBranch, isDeductioning)}
                  />
                )
                :
                (
                  <process-deduction
                    urlParamteres={this.urlParamteres}
                    class={styles.content_instance}
                    ref='processDeduction'
                    on-change={(hasBranch: boolean, isDeductioning: boolean) => this.onProcessDeductionChange(hasBranch, isDeductioning)}
                  />
                )
              }

              <approval-comment
                urlParamteres={this.urlParamteres}
                visible={this.moduleState.comment}
                on-change={(value: any) => this.comment = value}
                class={styles.content_instance}
                ref='approvalComment'
              />
              {this.processChooseVisible && this.dataProcessArr.length > 0 ? (
                <process-choose
                  visible={this.processChooseVisible}
                  dataProcess={this.dataProcessArr}
                  on-processid={(v: string, authId: string) => {
                    this.processChooseVisible = false;
                    this.urlParamteres.authId = authId;
                    this.urlParamteres.processId = v;
                    this.getProcessInfo();
                  }}
                />
              ) : null}
            </div>
          </van-tab>
          {
            this.moduleState.document ? <van-tab
              name='document'
              title='公文正文'>
              <div class={styles.content + ' ' + styles.gw}
                style={{ display: this.gwObject.visible ? 'block' : 'none' }}>
                <web-office
                  fileId={this.gwObject.fileId}
                  visible={this.gwObject.visible}
                  urlParamteres={this.urlParamteres}
                  hasButton={this.hasBranch && this.hasAction && !this.businessNumber}
                  on-rename={(rename: string) => this.onWebOfficeRename(rename)}
                  ref='webOffice'
                />
              </div>
            </van-tab> : ''
          }

        </van-tabs>
        <page-bottom
          urlParamteres={this.urlParamteres}
          isButtonHide={!this.hasBranch || this.buttonHide || this.businessNumber}
          isDeductioning={this.isDeductioning}
          comment={this.comment}
          on-action-submit={this.onActionSubmit}
          on-has-action={this.onHasAction}
          ref='pageBottom'
        />
      </div>
    );
  }
}
