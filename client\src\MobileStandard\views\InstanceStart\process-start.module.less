.start_content {
    height: 100%;
    :global(.van-tab__text) {
        font-size: 15px;
        font-weight: bold;
    }

    :global(.van-tabs__content) {
        height: 100%;
        background-color: #f0f2f5;

        :global(.van-tab__pane) {
            background-color: #f0f2f5;
        }

        :global(.van-tab__pane:last-child) {
            padding-bottom: 70px;
        }
    }

    .content {
        padding: 0 12px 0 12px;

        .content_instance:not(:first-child) {
            margin-top: 10px;
        }
    }

    .content:first-child {
        padding-top: 10px;
    }

    .gw {
        height: calc(~'100vh - 110px');
        padding-top: 15px !important;
    }
}