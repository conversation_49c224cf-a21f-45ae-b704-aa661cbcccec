import { Observable } from 'rxjs';

import { httpHelper } from '@/MobileStandard/common/utils';
import { InstanceStartDto } from './types';
import { ActionDataDto } from '@/MobileStandard/components/DetailPage/PageBottom/types';

class InstanceStartService {
  start(data: InstanceStartDto): Observable<string> {
    const url = `/api/process/v1/instances/start`;
    return httpHelper.post(url, data, undefined, { loading: true });
  }

  save(data: any): Observable<string> {
    const url = `/api/engine/v2/drafts`;
    return httpHelper.post(url, data, undefined, { loading: true });
  }

  getDraft(id: string): Observable<any> {
    const url = `/api/engine/v2/drafts/${id}`;
    return httpHelper.get(url);
  }

  deleteDraft(id: string): Observable<void> {
    const _url = `/api/engine/v2/drafts/${id}`;
    return httpHelper.delete(_url);
  }

  deleteTodoDraft(params: any): Observable<any> {
    const _url = `/api/todo-centre/v1/draft/deleteAndDeleteRelatedBusinessByBs`;
    return httpHelper.delete(_url, { data: params }, {loading: true});
  }

  cancel(taskId: string, data: ActionDataDto): Observable<void> {
    const url = `/api/process/v1/tasks/${taskId}/cancel`;
    return httpHelper.put(url, data, undefined, { loading: true });
  }

  instanceCancel(number: string, data: ActionDataDto): Observable<void> {
    const url = `/api/process/v1/instances/${number}/cancel`;
    return httpHelper.put(url, { ...data, startCancel: true }, undefined, { loading: true });
  }

  taskReadyById(data: any): Observable<void> {
    const url = `/api/todo-centre/v1/task/readByTaskId`;
    return httpHelper.put(url, data);
  }

  updateStatus(data: any): Observable<void> {
    const url = `/api/todo-centre/v1/task/updateStatus`;
    return httpHelper.put(url, data);
  }
  taskReadInEngine(id: any): Observable<void> {
    const url = `/api/engine/v1/tasks/${id}/read-start-task`;
    return httpHelper.put(url);
  }
  // 获取完整nacos数据
  getConfig(): Observable<any> {
    const _url = '/api/config';
    return httpHelper.get(_url, undefined, { loading: false });
  }
}

export const instanceStartService = new InstanceStartService();
