import { InstanceBaseInfoDto } from '@/MobileStandard/components/DetailPage/BaseInfo/type';
import { PreviewStepChangeDto } from '@/MobileStandard/components/DetailPage/ProcessDeduction/types';
import { AttachmentDto } from '@/MobileStandard/components/DetailPage/RelevantAttachment/types';
import { RelationInstanceDto } from '@/MobileStandard/components/DetailPage/RelevantProcess/types';
import { ProcessModuleState } from '@/MobileStandard/services/process/process.types';

export interface InstanceStartDto {
  healthy?: boolean;
  processId?: string;
  instanceNumber?: string;
  topic?: string;
  startUserId?: string;
  startUserName?: string;
  startUserAccount?: string;
  params?: any;
  approvalComments?: string;
  positionId?: string;
  positionName?: string;
  organizationId?: string;
  organizationPath?: string;
  organizationNamePath?: string;
  instanceRelations?: any;
  instanceAttachments?: AttachmentDto[];
  previewStepChanges?: PreviewStepChangeDto[];
  bsid?: string;
  btid?: string;
  boid?: string;
  controlPoint?: any;
  authId?: string;
  simulate?: boolean;
}

export interface StartDataDto {
  moduleState?: ProcessModuleState;
  baseInfo?: InstanceBaseInfoDto;
  processName?: string;
  formData?: any;
  relationInstances?: RelationInstanceDto[];
  instanceAttachments?: AttachmentDto[];
  approvalComments?: string;
  previewData?: any;
}
