import { Component, Prop, Vue } from 'vue-property-decorator';
import { instanceViewService } from './service';
import { UrlParametersDto } from '@/MobileStandard/services/urlParameter';
import { ProcessModuleState, processService } from '@/MobileStandard/services/process';
import { FormInfo, RelevantAttachment, InstanceSendRecvRecord } from '@/MobileStandard/components';
import { subjectHelper } from '@/MobileStandard/common/utils';
import { InstanceBaseInfoDto } from '@/MobileStandard/components/DetailPage/BaseInfo/type';
import { dateHelper } from '@/MobileStandard/common/utils/date-helper';
import { authService } from '@/MobileStandard/services/auth';

@Component({
  components: { InstanceSendRecvRecord }
})
export class IndexMixins extends Vue {
  tabActive = 'base';
  urlParamteres: UrlParametersDto = {};
  moduleState: ProcessModuleState = {
    baseInfo: true,
    relation: true,
    attachment: true,
    record: true,
    comment: true,
    document: false
  };
  buttonHide = false;
  comment = '';
  formInfoChangeCount = 0;
  isAnchorClick = false;
  anchorTop = 0;
  baseInfo: InstanceBaseInfoDto = {};
  notPermitted = false;
  notPermittedLoading = true;
  ShowFullScreen = false;
  FKXGXXHtml = '';
  isParalle = true;
  homeUrl = '';
  gwObject = {
    visible: false,
    fileId: ''
  };
  hasAction = false;
  onHasAction(isHas: boolean) {
    this.hasAction = isHas;
  }
  hengping(html: any) {
    this.ShowFullScreen = true;
    document.getElementsByClassName('custom_table')[0].innerHTML = html;
  }
  getPageType() {
    const path = this.$route.path.replace('/independent', '').replace('/', '');
    return path && path.split('/').length > 0 ? path.split('/')[1] : '';
  }
  onActionSubmit(actionCode: string, actionName: string, actionData: any) {
    this.buttonHide = true;
    const updateStatusPars = {
      status: 9,
      taskId: this.urlParamteres.taskId,
      systemCode: 'BPM',
      businessID: this.baseInfo.instanceId,
      businessNumber: this.urlParamteres.number
    };
    if (actionCode && actionCode.startsWith('approve') ||
      actionCode.startsWith('handover') ||
      actionCode.startsWith('counter-sign')) {
      const xgfj = (this.$refs.relevantAttachment as RelevantAttachment).getValues();
      let bdxx_st: any = { isSuccess: false, data: null };
      const params: any = {
        instanceAttachments: xgfj,
        actionCode: actionCode,
        actionName: actionName,
        ...actionData
      };
      const formInfo = this.$refs.formInfo as FormInfo;
      if (actionCode.startsWith('handover') ||
        actionCode.startsWith('counter-sign') || actionCode === 'approve_ignore') {
        bdxx_st = this.normalApproval(false, formInfo, bdxx_st, updateStatusPars, params, actionCode, actionData);
      } else {
        (formInfo.instance.$refs.coreInfoForm as any).validate().then(() => {
          bdxx_st = this.normalApproval(true, formInfo, bdxx_st, updateStatusPars, params, actionCode, actionData);
        }, (error: any) => {
          if (error && error.length > 0) {
            this.$toast({
              type: 'fail',
              forbidClick: true,
              message: error[0].message
            });
          }
          this.buttonHide = false;
        });
      }
    } else if (actionCode === 'reject') {
      instanceViewService.updateStatus(updateStatusPars).subscribe(() => {
        const xgfj = (this.$refs.relevantAttachment as RelevantAttachment).getValues();
        const params = {
          targetUsers: actionData.targetUsers,
          targetActivityId: actionData.targetActivityId,
          resolveComment: actionData.resolveComment,
          isCallSystem: actionData.isCallSystem,
          actionCode: actionCode,
          actionName: actionName,
          instanceAttachments: xgfj
        };
        instanceViewService.reject(this.urlParamteres.taskId || '', params, actionData.rejectType).subscribe(() => {
          this.$toast({
            type: 'success',
            forbidClick: true,
            message: this.$l.getLocale('tips.rejectSuccess'),
            onClose: () => {
              // this.$router.push({ path: '/todo' });
              window.location.href = this.homeUrl;
            }
          });
        }, () => this.buttonHide = false);
      }, () => this.buttonHide = false);
    } else if (actionCode === 'refuse') {
      instanceViewService.updateStatus(updateStatusPars).subscribe(() => {
        const params = {
          actionCode: actionCode,
          actionName: actionName,
          ...actionData
        };
        instanceViewService.refuse(this.urlParamteres.taskId || '', params).subscribe(() => {
          this.$toast({
            type: 'success',
            forbidClick: true,
            message: this.$l.getLocale('tips.refuseSuccess'),
            onClose: () => {
              // this.$router.push({ path: '/todo' });
              window.location.href = this.homeUrl;
            }
          });
        }, () => this.buttonHide = false);
      }, () => this.buttonHide = false);
    } else if (actionCode === 'notice') {
      instanceViewService.notice(actionData).subscribe(() => {
        this.$toast({
          type: 'success',
          forbidClick: true,
          message: this.$l.getLocale('tips.noticeSuccess')
        });
        this.buttonHide = false;
      }, () => this.buttonHide = false);
    } else if (actionCode === 'delay') {
      const params: any = {
        actionCode: actionCode,
        actionName: actionName,
        ...actionData
      };
      instanceViewService.delay(this.urlParamteres.taskId || '', params).subscribe(() => {
        this.$toast({
          type: 'success',
          forbidClick: true,
          message: this.$l.getLocale('tips.delay-success'),
          onClose: () => {
            // this.$router.push({ path: '/todo' });
            window.location.href = this.homeUrl;
          }
        });
      }, () => this.buttonHide = false);
    } else if (actionCode === 'urge') {
      const urgeParams = {
        systemCode: 'BPM',
        businessID: this.baseInfo.instanceId,
        businessNumber: this.urlParamteres.number,
        userLoginId: authService.user.account,
        userName: authService.user.name,
        startTime: this.baseInfo.startDate
      };
      instanceViewService.doUrge(urgeParams).subscribe(() => {
        this.$toast({
          type: 'success',
          forbidClick: true,
          message: this.$l.getLocale('tips.urgingSuccess')
        });
        this.buttonHide = false;
      }, () => this.buttonHide = false);
    } else if (actionCode === 'withdraw') {
      if (this.urlParamteres.pageType === 'my-processes') {
        // instanceViewService.getStartRecallStatus(this.urlParamteres.number || '').subscribe(data => {
        // if (data) {
        const updateMyProcessesStatusPars = {
          status: 9,
          systemCode: 'BPM',
          businessID: this.baseInfo.instanceId,
          startTime: this.baseInfo.startDate
        };
        instanceViewService.updateMyProcessesStatus(updateMyProcessesStatusPars).subscribe(() => {
          instanceViewService.doWithdraw(this.urlParamteres.number || '').subscribe(() => {
            this.$toast({
              type: 'success',
              forbidClick: true,
              message: this.$l.getLocale('tips.withdrawSuccess'),
              onClose: () => {
                // this.$router.push({ path: '/todo' });
                window.location.href = this.homeUrl;
              }
            });
          }, () => this.buttonHide = false);
        }, () => this.buttonHide = false);
        // } else {
        //   this.$toast({
        //     type: 'fail',
        //     forbidClick: true,
        //     message: `该流程已审批，不能撤回`
        //   });
        //   this.buttonHide = false;
        // }
        // }, () => this.buttonHide = false);
      } else if (this.urlParamteres.pageType === 'done') {
        instanceViewService.doDoneWithdraw(this.urlParamteres.taskId || '').subscribe(() => {
          this.$toast({
            type: 'success',
            forbidClick: true,
            message: this.$l.getLocale('tips.withdrawSuccess'),
            onClose: () => {
              // this.$router.push({ path: '/todo' });
              window.location.href = this.homeUrl;
            }
          });
        }, () => this.buttonHide = false);
      }
    } else if (actionCode === 'receive') {
      instanceViewService.receive(this.urlParamteres.instanceSendRecvRecordId || '', actionData.comment).subscribe(() => {
        this.$toast({
          type: 'success',
          forbidClick: true,
          message: this.$l.getLocale('tips.receive-success'),
          onClose: () => {
            // this.$router.push({ path: '/todo' });
            window.location.href = this.homeUrl;
          }
        });
      }, () => this.buttonHide = false);
    }
  }
  normalApproval(
    isValidate: any,
    formInfo: FormInfo,
    bdxx_st: any,
    updateStatusPars: any,
    params: any,
    actionCode: string,
    actionData: any) {
    formInfo.getValues(isValidate, actionCode).subscribe(data => {
      bdxx_st = data;
      if (bdxx_st.isSuccess) {
        instanceViewService.updateStatus(updateStatusPars).subscribe(() => {
          if (this.formInfoChangeCount > 1 || bdxx_st.isTJHFZ) {
            params['params'] = bdxx_st.data;
          }
          if (actionCode.startsWith('handover')) {
            instanceViewService.handover(this.urlParamteres.taskId || '', params).subscribe(() => {
              this.$toast({
                type: 'success',
                forbidClick: true,
                message: this.$l.getLocale('tips.handoverSuccess'),
                onClose: () => {
                  window.location.href = this.homeUrl;
                  // this.$router.push({ path: '/todo' });
                }
              });
            }, () => this.buttonHide = false);
          } else if (actionCode.startsWith('counter-sign')) {
            instanceViewService.sign(this.urlParamteres.taskId || '', params, actionData.signType).subscribe(() => {
              this.$toast({
                type: 'success',
                forbidClick: true,
                message: this.$l.getLocale('tips.counterSignSuccess'),
                onClose: () => {
                  window.location.href = this.homeUrl;
                  // this.$router.push({ path: '/todo' });
                }
              });
            }, () => this.buttonHide = false);
          } else {
            instanceViewService.done(this.urlParamteres.taskId || '', params).subscribe(() => {
              this.$toast({
                type: 'success',
                forbidClick: true,
                message: this.$l.getLocale('tips.approvalSuccess'),
                onClose: () => {
                  window.location.href = this.homeUrl;
                  // this.$router.push({ path: '/todo' });
                }
              });
            }, () => this.buttonHide = false);
          }
        }, () => this.buttonHide = false);
      } else {
        this.buttonHide = false;
      }
    }, () => this.buttonHide = false);
    return bdxx_st;
  }

  approval() {

  }
  onTabsChange(name: any, title: any) {
    if (name === 'document') {
      this.gwObject.visible = true;
    } else {
      this.gwObject.visible = false;
    }
  }
  onDocumentClick(fileId: any, isGo: boolean) {
    this.gwObject.fileId = fileId;
    if (isGo) {
      this.gwObject.visible = true;
    }
    if (!fileId) {
      (this.$refs.webOffice as any).officeDestroy();
    }
  }
  onWebOfficeRename(rename: string) {
    (this.$refs.document as any).rename(rename);
  }
  beforeCreate() {
    const viewToken = this.$route.query['view-token'] as string;
    if (viewToken && viewToken !== '') {
      authService.checkTripartiteSystemViewToken(this.$route.params.number, this.$route.query['bsid'] as string, viewToken).subscribe(
        () => {
          this.notPermittedLoading = false;
        },
        () => {
          this.notPermitted = true;
        }
      );
    } else {
      authService
        .checkAnonymousInstanceViewState(
          this.$route.params.number,
          this.$route.query['task-id'] as string,
          this.$route.query.movitech_token as string
        )
        .subscribe(
          () => {
            this.notPermittedLoading = false;
          },
          () => {
            this.notPermitted = true;
          }
        );
    }
    instanceViewService.getConfig().subscribe(data => {
      if (data) {
        // if (data['IsParalle']) {
        //   this.isParalle = data['IsParalle'];
        // }
        this.homeUrl = data['saiwuSetting']['home'] + window.location.search;
      }
    });
  }
  created() {
    this.urlParamteres.number = this.$route.params['number'] as string;
    this.urlParamteres.taskId = this.$route.query['task-id'] as string;
    this.urlParamteres.instanceSendRecvRecordId = this.$route.query['instance-send-recv-record-id'] as string;
    this.urlParamteres.pageType = this.getPageType();
    processService.getProcessFormModuleState('', this.urlParamteres.taskId || '', this.urlParamteres.number).subscribe(state => {
      this.moduleState = state;
    });
    subjectHelper.getSubject('formInfo$').subscribe((s: any) => {
      this.formInfoChangeCount += 1;
    });
    subjectHelper.getSubject('baseInfo$').subscribe((s: any) => {
      if (!this.baseInfo.instanceId) {
        // 待办或抄送需要消
        if (this.urlParamteres.pageType === 'todo') {
          const data = {
            taskId: this.urlParamteres.taskId,
            systemCode: 'BPM',
            businessID: s.instanceId,
            businessNumber: this.urlParamteres.number
          };
          instanceViewService.taskReadyById(data).subscribe();
          if (this.urlParamteres.taskId) {
            instanceViewService.taskReadInEngine(this.urlParamteres.taskId).subscribe();
          }
        } else if (this.urlParamteres.pageType === 'cc-to-me') {
          const data = {
            ccId: this.urlParamteres.taskId,
            startTime: dateHelper.currentTime,
            systemCode: 'BPM',
            businessID: s.instanceId,
            businessNumber: this.urlParamteres.number
          };
          instanceViewService.carboncopyReadyById(data).subscribe();
        }
        this.baseInfo = s;
      }
    });
  }
}
