import { Component, Vue } from 'vue-property-decorator';
import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import styles from './instance-view.module.less';

@Component
export class InstanceView extends mixins(IndexMixins) {
    render() {
        if (this.notPermitted) {
            return <van-empty image='error'
                class={styles.van_empty}>
                <template slot='description'>
                    <div class={styles.status}>
                        403
                    </div>
                    <div class={styles.tips}>
                        你没有此页面的访问权限
                    </div>
                </template>
            </van-empty>;
        } else if (this.notPermittedLoading) {
            return '';
        }
        return (
            <div class={styles.approval_content}
                id='approval'>
                <div v-show={this.ShowFullScreen} style='background: #fff;;height: 100%;position: absolute;top: 0; left: 0;z-index: 100'>
                    <img src={require('@/assets/images/btn_hengping.png')} style='display:inline-block;position: fixed;float:right;right:5px;top:5px;z-index:1000'
                        on-click={() => { this.ShowFullScreen = false; }}></img>
                    <div class='custom_table' style='z-index: 100'></div>
                </div>
                <van-form ref='approvalForm' v-show={!this.ShowFullScreen}>
                    <van-tabs
                        v-model={this.tabActive}
                        sticky
                        scrollspy
                        color='#ffffff'
                        background='#009a3e'
                        line-height='3'
                        line-width='25'
                        title-active-color='#ffffff'
                        title-inactive-color='#ffffff'
                        on-change={(name: any, title: any) => this.onTabsChange(name, title)}
                    >
                        <div class={styles.title}
                            style={{ display: !this.gwObject.visible ? 'block' : 'none' }}>
                            <div class={styles.desc}>
                                <div class={styles.desc_content}>
                                    {
                                        this.baseInfo.status ? `${this.baseInfo.topic} [${this.baseInfo.processName}]` : ''
                                    }
                                </div>
                            </div>
                        </div>
                        <van-tab
                            name='base'
                            title={this.$l.getLocale('fields.baseInfo')}>
                            <div class={styles.content}
                                style={{ display: !this.gwObject.visible ? 'block' : 'none' }}>
                                <base-info
                                    urlParamteres={this.urlParamteres}
                                    visible={this.moduleState.baseInfo}
                                    editable={false}
                                    ref='baseInfo'
                                />
                            </div>
                        </van-tab>
                        <van-tab
                            name='core'
                            title={this.$l.getLocale('fields.approvalInfo')}>
                            <div class={styles.content}
                                style={{ display: !this.gwObject.visible ? 'block' : 'none' }}>
                                <form-info
                                    urlParamteres={this.urlParamteres}
                                    class={styles.content_instance}
                                    editable={false}
                                    ref='formInfo'
                                    on-hengping={(html: any) => this.hengping(html)}
                                />
                                {
                                    this.moduleState.document ?
                                        <document
                                            upload={false}
                                            delete={false}
                                            urlParamteres={this.urlParamteres}
                                            visible={this.moduleState.document}
                                            editable={false}
                                            on-document-click={(filedId: any, isGo: boolean) => this.onDocumentClick(filedId, isGo)}
                                            ref='document'
                                        /> : ''
                                }
                                <relevant-attachment
                                    urlParamteres={this.urlParamteres}
                                    visible={this.moduleState.attachment}
                                    editable={this.urlParamteres.pageType === 'todo' && this.hasAction && !this.buttonHide}
                                    class={styles.content_instance}
                                    ref='relevantAttachment'
                                />
                                <relevant-process
                                    urlParamteres={this.urlParamteres}
                                    visible={this.moduleState.relation}
                                    class={styles.content_instance}
                                    editable={false}
                                    ref='relevantProcess'
                                />
                            </div>
                        </van-tab>
                        {
                            this.baseInfo.isShouFaWen ? <instance-send-recv-record
                            visible={this.baseInfo.isShouFaWen}
                            instanceId={this.baseInfo.instanceId}
                            ref='InstanceSendRecvRecord'
                            /> : null
                        }
                        <van-tab
                            name='approval'
                            title={this.$l.getLocale('fields.approvalRecord')}>
                            <div class={styles.content}
                                style={{ display: !this.gwObject.visible ? 'block' : 'none' }}>
                                {this.isParalle ?
                                    (
                                        <approval-record-parallel
                                            urlParamteres={this.urlParamteres}
                                            visible={this.moduleState.record}
                                            ref='approvalRecord'
                                            class={styles.content_instance}
                                        />
                                    )
                                    :
                                    (
                                        <approval-record
                                            urlParamteres={this.urlParamteres}
                                            visible={this.moduleState.record}
                                            ref='approvalRecord'
                                            class={styles.content_instance}
                                        />
                                    )
                                }
                                {
                                    this.urlParamteres.pageType === 'todo' ?
                                        <approval-comment
                                            urlParamteres={this.urlParamteres}
                                            visible={this.moduleState.comment}
                                            on-change={(value: any) => this.comment = value}
                                            class={styles.content_instance}
                                            ref='approvalComment'
                                        /> : ''
                                }
                            </div>
                        </van-tab>
                        {
                            this.moduleState.document ?
                                <van-tab
                                    name='document'
                                    title='公文正文'>
                                    <div class={styles.content + ' ' + styles.gw}
                                        style={{ display: this.gwObject.visible ? 'block' : 'none' }}>
                                        <web-office
                                            fileId={this.gwObject.fileId}
                                            visible={this.gwObject.visible}
                                            urlParamteres={this.urlParamteres}
                                            hasButton={this.hasAction}
                                            on-rename={(rename: string) => this.onWebOfficeRename(rename)}
                                            ref='webOffice'
                                        />
                                    </div>
                                </van-tab> : ''
                        }
                    </van-tabs>
                </van-form>
                <page-bottom
                    urlParamteres={this.urlParamteres}
                    isButtonHide={this.buttonHide}
                    comment={this.comment}
                    on-action-submit={this.onActionSubmit}
                    on-has-action={this.onHasAction}
                    ref='pageBottom'
                />
            </div>
        );
    }
}
