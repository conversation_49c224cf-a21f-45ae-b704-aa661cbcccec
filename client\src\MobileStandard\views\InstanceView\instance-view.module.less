.van_empty {
    height: 100%;

    div {
        display: block;
        text-align: center;
    }

    .status {
        font-size: 16px;
        font-weight: bold;
    }
}

.approval_content {
    height: 100%;

    .title {
        padding: 10px 12px 0 12px;
        height: 70px;

        .desc {
            width: 100%;
            height: 100%;
            background-image: url('@/assets/images/app_detail_titbg.png');
            background-repeat: round;
            font-size: 15px;
            font-weight: bold;

            .desc_content {
                height: 100%;
                width: 100%;
                padding: 0 12px;
                box-sizing: border-box;
                align-items: center;
                display: flex;
            }
        }
    }

    :global(.van-tab__text) {
        font-size: 15px;
        font-weight: bold;
    }

    :global(.van-tabs__content) {
        height: 100%;
        background-color: #f0f2f5;

        :global(.van-tab__pane) {
            background-color: #f0f2f5;
        }

        :global(.van-tab__pane:last-child) {
            padding-bottom: 70px;
        }
    }

    .content {
        padding: 0 12px 0 12px;

        .content_instance:not(:first-child) {
            margin-top: 10px;
        }
    }

    .content:first-child {
        padding-top: 10px;
    }

    .gw {
        height: calc(~'100vh - 110px');
        padding-top: 15px !important;
    }
}