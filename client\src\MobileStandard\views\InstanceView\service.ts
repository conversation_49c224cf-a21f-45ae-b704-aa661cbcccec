import { httpHelper } from '@/MobileStandard/common/utils';
import { ActionDataDto } from '@/MobileStandard/components/DetailPage/PageBottom/types';
import { Observable } from 'rxjs';
class InstanceViewService {
    done(taskId: string, data: ActionDataDto): Observable<void> {
        const url = `/api/process/v1/tasks/${taskId}/done`;
        return httpHelper.put(url, data, undefined, { loading: true });
    }
    handover(taskId: string, data: ActionDataDto): Observable<void> {
        const url = `/api/process/v1/tasks/${taskId}/handover`;
        return httpHelper.put(url, data, undefined, { loading: true });
    }
    sign(taskId: string, data: ActionDataDto, type: string): Observable<void> {
        const url = `/api/process/v1/tasks/${taskId}/extra-${type}`;
        return httpHelper.put(url, data, undefined, { loading: true });
    }
    reject(taskId: string, data: ActionDataDto, resolveType: string): Observable<void> {
        const url = `/api/process/v1/tasks/${taskId}/reject`;
        return httpHelper.put(url, data, { params: { resolveType: resolveType } }, { loading: true });
    }
    refuse(taskId: string, data: ActionDataDto): Observable<void> {
        const url = `/api/process/v1/tasks/${taskId}/refuse`;
        return httpHelper.put(url, data, undefined, { loading: true });
    }
    notice(data: ActionDataDto): Observable<void> {
        const url = `/api/process/v1/tasks/notice`;
        return httpHelper.post(url, data, undefined, { loading: true });
    }
    taskReadyById(data: any): Observable<void> {
        const url = `/api/todo-centre/v1/task/readByTaskId`;
        return httpHelper.put(url, data);
    }
    carboncopyReadyById(data: any): Observable<void> {
        const url = `/api/todo-centre/v1/carboncopy/readByCCId`;
        return httpHelper.put(url, data);
    }
    updateStatus(data: any): Observable<void> {
        const url = `/api/todo-centre/v1/task/updateStatus`;
        return httpHelper.put(url, data);
    }
    updateMyProcessesStatus(data: any): Observable<void> {
        const url = `/api/todo-centre/v1/myStart`;
        return httpHelper.put(url, data);
    }
    delay(taskId: string, data: ActionDataDto): Observable<void> {
        const url = `/api/process/v1/tasks/${taskId}/delay`;
        return httpHelper.put(url, data, undefined, { loading: true });
    }
    doUrge(params: any): Observable<void> {
        const url = `/api/todo-centre/v1/myStart/ownUrge`;
        return httpHelper.post(url, params, {}, { loading: true });
    }
    doWithdraw(number: string): Observable<void> {
        const url = `/api/process/v1/instances/${number}/recall`;
        return httpHelper.put(url, undefined, undefined, { loading: true });
    }
    doDoneWithdraw(taskId: string): Observable<void> {
        const url = `/api/process/v1/tasks/${taskId}/recall-task`;
        return httpHelper.put(url, undefined, undefined, { loading: true });
    }
    taskReadInEngine(taskId: any): Observable<void> {
        const url = `/api/engine/v1/tasks/${taskId}/read-task`;
        return httpHelper.put(url);
    }
    // 获取完整nacos数据
    getConfig(): Observable<any> {
        const _url = '/api/config';
        return httpHelper.get(_url, undefined, { loading: false });
    }
    getStartRecallStatus(number: string): Observable<any> {
        const _url = `/api/engine/v1/instances/${number}/start-recall-status`;
        return httpHelper.get(_url, undefined, { loading: true });
    }
    receive(id: string, comment: string): Observable<void> {
        const url = `/api/process/v1/instanceSendRecvRecord/operate`;
        return httpHelper.put(url, {
            instanceSendRecvRecordId: id,
            content: comment,
            actionCode: 'receive',
            status: 1
        }, undefined, { loading: true });
    }
}

export const instanceViewService = new InstanceViewService();
