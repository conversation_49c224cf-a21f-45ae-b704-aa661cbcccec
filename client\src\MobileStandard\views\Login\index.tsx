import { Component, Vue } from 'vue-property-decorator';

import styles from './login.module.less';
import { LoginCaptcha } from '@/MobileStandard/components';
import { authService } from '@/MobileStandard/services/auth';

@Component({
  components: { LoginCaptcha },
})
export class Login extends Vue {
  private account = '';
  private password = '';
  private code = '';
  private nonce = '';
  private buttonVisible = true;
  onSubmit() {
    this.buttonVisible = !this.buttonVisible;
    authService.login(this.account, this.password, this.nonce, this.code).subscribe(
      () => {
        const refererUrl = this.$route.query['referer'] as string;
        this.$router.push(refererUrl || '/');
      },
      () => {
        this.buttonVisible = !this.buttonVisible;
        (this.$refs.captcha as LoginCaptcha).getCaptcha();
      }
    );
  }

  render() {
    return (
      <div class={styles.login}>
        <div class={styles.header}>{this.$l.getLocale('fields.systemName')}</div>
        <div class={styles.content}>
          <van-form on-submit={this.onSubmit} class={styles.form}>
            <van-field
              v-model={this.account}
              name={this.$l.getLocale('fields.userName')}
              label={this.$l.getLocale('fields.userName')}
              placeholder={this.$l.getLocale('fields.userName')}
              left-icon=''
              rules={[{ required: true, message: this.$l.getLocale(['fields.please', 'fields.input', 'fields.userName']) }]}
              class={styles.field}
            ></van-field>
            <van-field
              v-model={this.password}
              type='password'
              name={this.$l.getLocale('fields.password')}
              label={this.$l.getLocale('fields.password')}
              placeholder={this.$l.getLocale('fields.password')}
              left-icon=''
              rules={[{ required: true, message: this.$l.getLocale(['fields.please', 'fields.input', 'fields.password']) }]}
              class={styles.field}
            ></van-field>
            <van-field
              v-model={this.code}
              name={this.$l.getLocale('fields.verificationCode')}
              label={this.$l.getLocale('fields.verificationCode')}
              placeholder={this.$l.getLocale('fields.verificationCode')}
              left-icon=''
              rules={[{ required: true, message: this.$l.getLocale(['fields.please', 'fields.input', 'fields.verificationCode']) }]}
              class={styles.field}
            >
              <template slot='button'>
                <login-captcha
                  ref='captcha'
                  on-load={(nonce: string) => {
                    this.nonce = nonce;
                  }}
                />
              </template>
            </van-field>
            {
              this.buttonVisible ?
                <van-button type='info' class={styles.button} size='small'
                  native-type='submit'>{this.$l.getLocale('buttons.login')}</van-button> :
                ''
            }
          </van-form>
        </div>
      </div>
    );
  }
}
