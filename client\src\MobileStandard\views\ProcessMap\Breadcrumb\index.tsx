import lodash from 'lodash';
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import styles from './breadcrumb.module.less';

@Component
export class Breadcrumb extends Vue {
    @Prop() breadcrumb!: any;
    @Prop({ default: '>' }) separator!: any;
    @Emit('click')
    click(breadcrumbName: any, breadcrumbValue: any, breadcrumbIndex: any) {
    }
    private onBreadcrumbClick(breadcrumbName: any, breadcrumbValue: any, breadcrumbIndex: any) {
        if (breadcrumbIndex !== this.breadcrumb.length - 1) {
            this.click(breadcrumbName, breadcrumbValue, breadcrumbIndex);
        }
    }
    created() {
    }
    render() {
        return (
            <div class={styles.breadcrumb}>
                {
                    lodash.map(this.breadcrumb, (m: any, mi: any) => {
                        return [<span class={styles.breadcrumb_link + (mi === this.breadcrumb.length - 1 ? ' ' + styles.active : '')}
                            on-click={() => this.onBreadcrumbClick(m.label, m.value, mi)}>
                            {
                                m.label
                            }
                        </span>, mi !== this.breadcrumb.length - 1 ? <span class={styles.breadcrumb_separator}>
                            {
                                this.separator
                            }
                        </span> : ''];
                    })
                }
            </div>
        );
    }
}
