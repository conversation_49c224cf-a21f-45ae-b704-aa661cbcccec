import { Component, Vue } from 'vue-property-decorator';
import { Breadcrumb } from './Breadcrumb';
import { Process } from './Pocess';
import { ToolBar } from './ToolBar';
import { processMapService } from './service';
import { MainMenu } from '@/MobileStandard/components';
import lodash from 'lodash';

@Component({
  components: { MainMenu, ToolBar, Process, Breadcrumb },
})
export class IndexMixins extends Vue {
  searchData: any = {};
  data: any = [];
  breadcrumb: any = [{
    label: this.$l.getLocale('fields.startProcess'),
    value: ''
  }];
  breadcrumbIndex = 0;
  favoriteProcesses = {
    isOpen: true,
    children: []
  };
  sourceData: any = [];
  onToolBarChange(searchData: any) {
    this.breadcrumbIndex = 0;
    this.breadcrumb.splice(1, this.breadcrumb.length - 1);
    this.searchData = searchData;
    this.onFavoriteProcesses();
    this.onLoadData();
  }
  onLoadData() {
    const params = {
      'kw': this.searchData.processName, // 流程名称关键字
      'is-auth': 1, // 授权筛选
      'type': 1, // 类型
      'use-type': 0, // 使用类型, 1-用于授权审批，0-用于其他
      'PositionId': this.searchData.position, // 岗位ID
      'BusinessTypeId': this.searchData.businessType, // 业务类型ID
      'ScopeId': this.searchData.scope // 适用范围Id
    };
    processMapService.processMap(params).subscribe(data => {
      this.sourceData = data;
      this.data = data;
    });
  }
  onClassChange(classObject: any) {
    this.breadcrumb.push({
      label: classObject.groupName,
      value: classObject.groupId
    });
    this.breadcrumbIndex = this.breadcrumb.length - 1;
    this.data = classObject;
  }
  onBreadcrumbChange(breadcrumbName: any, breadcrumbValue: any, breadcrumbIndex: any) {
    this.breadcrumbIndex = breadcrumbIndex;
    this.breadcrumb.splice(breadcrumbIndex + 1, this.breadcrumb.length - 1 - breadcrumbIndex);
    if (this.breadcrumbIndex === 0) {
      this.onFavoriteProcesses();
      this.onLoadData();
    } else if (this.breadcrumbIndex > 0) {
      this.onBreadcrumbData(null, 0);
    }
  }
  onBreadcrumbData(item: any, index: number) {
    if (this.breadcrumb.length - 1 > index) {
      if (index === 0) {
        const cuItem = lodash.find(this.sourceData, (c: any) => c.groupId === this.breadcrumb[index + 1].value);
        this.onBreadcrumbData(cuItem, index + 1);
      } else {
        const cuItem = lodash.find(item.children, (c: any) => c.groupId === this.breadcrumb[index + 1].value);
        this.onBreadcrumbData(cuItem, index + 1);
      }
    } else if (this.breadcrumb.length - 1 === index) {
      this.data = item;
    }
  }
  onProcessClick(processObject: any) {
    if (processObject.businessMobileUrl) {
      window.location.href = processObject.businessMobileUrl;
    }
  }
  onFavoriteProcesses() {
    const params = {
      type: 1, // 类型
      positionId: this.searchData.position // 岗位ID
    };
    processMapService.favoriteProcesses(params).subscribe(data => this.favoriteProcesses.children = data);
  }
  created() {
  }
}
