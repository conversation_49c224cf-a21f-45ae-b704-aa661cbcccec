import lodash from 'lodash';
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';
import { processMapService } from '../service';
import styles from './process.module.less';
import collect from '@/assets/images/icon_collect.png';
import uncollect from '@/assets/images/icon_uncollect.png';
import { Dialog } from 'vant';

@Component
export class Process extends Vue {
    @Prop() data!: any;
    @Prop({ default: 0 }) breadcrumbIndex!: number;
    showShuoming = false;
    shuomingText = '';
    @Emit('class-change')
    classChange(classObject: any) { }
    @Emit('process-click')
    processClick(processObject: any) { }
    private collectChange(processId: any) {
        processMapService.setFavorite(processId);
    }
    private onShuomingModal(processId: string) {
        processMapService.getIntroduction(processId).subscribe(data => {
            this.shuomingText = data;
            if (this.shuomingText) {
                Dialog.alert({
                    message: this.shuomingText,
                }).then(() => {
                    // on close
                });
            }
            this.showShuoming = data ? true : false;
        });
    }
    created() {
    }
    render() {
        return (
            <div class={styles.div}>
                {
                    this.breadcrumbIndex === 0 ? [
                        <div class={styles.content}>
                            <div class={styles.title}
                                on-click={() => this.data['commonProcess'].isOpen = !this.data['commonProcess'].isOpen}>
                                <i class='iconfont icon-sub_changyongliucheng'
                                    style='color:#808fa1;font-size:14px;margin-right:10px;' />
                                <span>{this.$l.getLocale('fields.commonProcesses')}</span>
                                <van-icon name={this.data['commonProcess'].isOpen ? 'arrow-down' : 'arrow-up'}
                                    color='#999999' />
                            </div>
                            {
                                this.data['commonProcess'].isOpen ?
                                    <div class={styles.process_content}>
                                        {
                                            lodash.map(this.data['commonProcess'].children, (c: any) => {
                                                return lodash.map(lodash.filter(c.processes, (cp: any) => cp.isFavorite),
                                                    (cc: any) => {
                                                        return <div class={styles.process + ' ' + styles.common_process}>
                                                            <span on-click={() => this.processClick(cc)}>{cc.name}</span>
                                                            <div class={styles.buttons}>
                                                                {
                                                                    cc.isIntroduction ? <i class='iconfont icon-btn_liuchengshuoming'
                                                                        style='color:#009a3e;font-size:15px;margin-right:10px;'
                                                                        on-click={() => this.onShuomingModal(cc.id)}
                                                                    ></i> : ''
                                                                }
                                                                <img src={collect} class={styles.collect}
                                                                    on-click={() => {
                                                                        cc.isFavorite = !cc.isFavorite;
                                                                        this.collectChange(cc.id);
                                                                    }} />
                                                            </div>
                                                        </div>;
                                                    });
                                            })
                                        }
                                    </div> : ''
                            }
                        </div>,
                        <div class={styles.content}>
                            {
                                lodash.map(this.data['process'], (p: any, pi: number) => {
                                    const no = (pi + 1).toString();
                                    return <div>
                                        <div class={styles.title}
                                            on-click={() => {
                                                p.isOpen = !p.isOpen;
                                                this.$forceUpdate();
                                            }}>
                                        <i class={`iconfont icon-sub_icon_1${lodash.padStart(no, 2, '0')}`}
                                            style='color:#009a3e;font-size:14px;margin-right:10px;' />
                                        <span>{p.groupName}</span>
                                        <van-icon name={p.isOpen ? 'arrow-down' : 'arrow-up'} color='#999999' />
                                    </div>
                                    {
                                        !p.isOpen ? p.processes.length > 0 ? p.processes.map((item: any) => {
                                            return <div class={styles.process_content}>
                                                  <div class={styles.process + ' ' + styles.common_process}>
                                                      <span  on-click={() => this.processClick(item)}>{item.name}</span>
                                                      <div class={styles.buttons}>
                                                          {item.isIntroduction ? <i class='iconfont icon-btn_liuchengshuoming'
                                                              style='color:#009a3e;font-size:15px;margin-right:10px;'
                                                              on-click={() => this.onShuomingModal(item.id)}></i> : ''}
                                                          <img src={item.isFavorite ? collect : uncollect} class={styles.collect}
                                                               on-click={() => {
                                                                  item.isFavorite = !item.isFavorite;
                                                                  this.collectChange(item.id); }}/>
                                                      </div>
                                                  </div>
                                              </div>;
                                          }) : '' : ''
                                    }
                                </div>;
                                })
                            }
                        </div>
                    ] : [
                        this.data['process'] && this.data['process'].processes &&
                            this.data['process'].processes.length > 0 ?
                            <div class={styles.content}>
                                {
                                    lodash.map(this.data['process'].processes, (dp: any) => {
                                        return <div class={styles.process}>
                                            <span on-click={() => this.processClick(dp)}>{dp.name}</span>
                                            <div class={styles.buttons}>
                                                {
                                                    dp.isIntroduction ? <i class='iconfont icon-btn_liuchengshuoming'
                                                        style='color:#009a3e;font-size:15px;margin-right:10px;'
                                                        on-click={() => this.onShuomingModal(dp.id)}
                                                    ></i> : ''
                                                }
                                                <img src={dp.isFavorite ? collect : uncollect} class={styles.collect}
                                                    on-click={() => {
                                                        dp.isFavorite = !dp.isFavorite;
                                                        this.collectChange(dp.id);
                                                    }} />
                                            </div>
                                        </div>;
                                    })
                                }
                            </div> : '',
                        this.data['process'] && this.data['process'].children ?
                            this.breadcrumbIndex === 3 ?
                                lodash.map(this.data['process'].children, (cp: any) => {
                                    return <div class={styles.content}>
                                        <div class={styles.title}>
                                            <span class={styles.class_process_title}>{cp.groupName}</span>
                                        </div>
                                        <div class={styles.process_content}>
                                            {
                                                lodash.map(cp.processes, (cpp: any) => {
                                                    return <div class={styles.process}>
                                                        <span on-click={() => this.processClick(cpp)}>{cpp.name}</span>
                                                        <div class={styles.buttons}>
                                                            {
                                                                cpp.isIntroduction ? <i class='iconfont icon-btn_liuchengshuoming'
                                                                    style='color:#009a3e;font-size:15px;margin-right:10px;'
                                                                    on-click={() => this.onShuomingModal(cpp.id)}
                                                                ></i> : ''
                                                            }
                                                            <img src={cpp.isFavorite ? collect : uncollect} class={styles.collect}
                                                                on-click={() => {
                                                                    cpp.isFavorite = !cpp.isFavorite;
                                                                    this.collectChange(cpp.id);
                                                                }} />
                                                        </div>
                                                    </div>;
                                                })
                                            }
                                        </div>
                                    </div>;
                                }) :
                                <div class={styles.content}>
                                    {
                                        lodash.map(this.data['process'].children, (cp: any) => {
                                            return <div class={styles.title}
                                                on-click={() => this.classChange(cp)}
                                            >
                                                <span>{cp.groupName}</span>
                                                <van-icon name='arrow'
                                                    color='#999999' />
                                            </div>;
                                        })
                                    }
                                </div> : ''
                    ]
                }
            </div>
        );
    }
}
