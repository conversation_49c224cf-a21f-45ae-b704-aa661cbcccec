.div {
    padding: 0 12px;
    padding-bottom: 80px;
}

.content {
    margin-top: 10px;
    background: #ffffff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0px 0.5px 0px 0px #e8e8e8;

    .title {
        height: 50px;
        display: flex;
        align-items: center;
        padding: 0 12px;
        box-shadow: 0px 0.5px 0px 0px #e8e8e8;

        span {
            font-size: 14px;
            color: #333333;
            flex: 1;
        }

        .class_process_title {
            color: #999999;
        }
    }

    .process {
        min-height: 55px;
        box-shadow: 0px 0.5px 0px 0px #e8e8e8;
        display: flex;
        align-items: center;
        padding: 0 12px;

        span {
            font-size: 12px;
            color: #333333;
            flex: 1;
            cursor: pointer;
        }

        .buttons {
            width: 50px;
            text-align: right;
            display: flex;
            align-items: center;
            justify-content: center;

            .collect {
                width: 15px;
                height: 15px;
            }
        }
    }

    .common_process {
        padding-left: 40px;
    }
}
