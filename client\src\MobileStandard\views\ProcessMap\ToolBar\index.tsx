import lodash from 'lodash';
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import styles from './tool-bar.module.less';
import { processMapService } from '../service';

@Component
export class ToolBar extends Vue {
    @Prop({ default: 0 }) breadcrumbIndex!: number;
    private processName = '';
    private companyVisible = false;
    private myPositionDomainBusinessTypeScope: any = [];
    private checkedIndex: any = [0, 0, 0, 0];
    private checkedValue: any = [];
    @Emit('change')
    change(search: any) {
    }
    private onCompanyConfirm(values: any, indexs: any) {
        this.checkedIndex = indexs;
        this.companyVisible = false;
        this.onSearch();
    }
    private onCompanyClick() {
        this.companyVisible = true;
        this.$nextTick(() => {
            (this.$refs.companyRef as any).setColumnIndex(0, this.checkedIndex[0]);
            (this.$refs.companyRef as any).setColumnIndex(1, this.checkedIndex[1]);
            (this.$refs.companyRef as any).setColumnIndex(2, this.checkedIndex[2]);
            (this.$refs.companyRef as any).setColumnIndex(3, this.checkedIndex[3]);
        });
    }
    private onCompanyCancel() {
        this.companyVisible = false;
    }
    private get companyLable() {
        const label: any = [];
        let array = this.myPositionDomainBusinessTypeScope;
        if (array && array.length > 0) {
            lodash.forEach(this.checkedIndex, (i: any) => {
                if (array && array[i]) {
                    label.push(array[i].text);
                    array = array[i].children;
                } else {
                    array = [];
                }
            });
        }
        return label.join(' / ');
    }
    private myPositionDomainBusinessTypeScopeValues(item: any, index: number): any {
        if (item && item.length > 0) {
            this.checkedValue.push(item[this.checkedIndex[index]].value);
            if (index < 3) {
                this.myPositionDomainBusinessTypeScopeValues(item[this.checkedIndex[index]].children, index + 1);
            }
        }
    }
    private myPositionDomainBusinessTypeScopeFormat(item: any, index: number): any {
        return lodash.map(item, (i: any, ii: number) => {
            const column: any = {
                text: i.label,
                value: i.value,
                typeCode: i.typeCode,
            };
            if (index < 4 && i.isDefault) {
                this.checkedIndex[index] = ii;
            }
            if (i.items && i.items.length > 0) {
                column['children'] = this.myPositionDomainBusinessTypeScopeFormat(i.items, index + 1);
            }
            return column;
        });
    }
    private getSearchData() {
        const search: any = {};
        search['processName'] = this.processName;
        this.myPositionDomainBusinessTypeScopeValues(this.myPositionDomainBusinessTypeScope, 0);
        console.log(this.checkedValue);
        search['position'] = this.checkedValue[0];
        search['domain'] = this.checkedValue[1];
        search['businessType'] = this.checkedValue[2];
        search['scope'] = this.checkedValue[3];
        return search;
    }
    private onSearch() {
        if (this.myPositionDomainBusinessTypeScope &&
            this.myPositionDomainBusinessTypeScope.length > 0) {
            this.checkedValue = [];
            this.change(this.getSearchData());
        }
    }
    created() {
        processMapService.myPositionDomainBusinessTypeScope().subscribe(data => {
            this.myPositionDomainBusinessTypeScope = this.myPositionDomainBusinessTypeScopeFormat(data, 0);
            console.log(this.checkedIndex);
            this.onSearch();
        });
    }
    render() {
        return (
            <div class={styles.bgi + (this.breadcrumbIndex === 0 ? ' ' + styles.first_toor_bar : '')}>
                <div class={styles.tool_bar}>
                    <div class={styles.company}
                        on-click={this.onCompanyClick}>
                        <i class='iconfont icon-ico_liuchengshiyongfanwei'
                            style='color:#009a3e;font-size:12px;'></i>
                        <span>
                            {
                                this.companyLable
                            }
                        </span>
                    </div>
                    <form action='/'
                        class={styles.form}>
                        <van-search
                            v-model={this.processName}
                            placeholder={this.$l.getLocale('placeholders.processStartSearch')}
                            background='#ffffff'
                            shape='round'
                            clearable
                            on-search={() => this.onSearch()}
                            on-clear={() => this.onSearch()}
                        />
                    </form>
                    <van-popup v-model={this.companyVisible}
                        position='bottom'
                        round>
                        <van-picker
                            show-toolbar
                            columns={this.myPositionDomainBusinessTypeScope}
                            on-confirm={(value: any, index: any) => this.onCompanyConfirm(value, index)}
                            on-cancel={this.onCompanyCancel}
                            ref='companyRef'
                        />
                    </van-popup>
                </div>
            </div>
        );
    }
}
