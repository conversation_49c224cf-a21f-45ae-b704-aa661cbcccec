.bgi {
    padding: 0 12px;
}

.tool_bar {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    background: #ffffff;

    .company {
        height: 32px;
        flex: 2;
        background-color: #f5f5f5;
        border-radius: 16px;
        text-align: left;
        overflow: hidden;
        padding-right: 5px;
        i {
            margin: 0 5px 0 10px;
        }

        span {
            font-size: 12px;
            height: 12px;
            color: #333333;
            line-height: 32px;
            font-weight: bold;
        }
    }

    .form {
        flex: 1;
        margin-left: 5px;

        :global(.van-search__content) {
            background: #f5f5f5;

            :global(.van-icon-search) {
                font-size: 12px;
            }

            :global(.van-field__control) {
                font-size: 12px;
                color: #999999;
                font-weight: 400px;
            }
        }
    }
}

.first_toor_bar {
    height: 165px;
    background-image: url(../../../../assets/images/<EMAIL>);
    background-repeat: round;

    .tool_bar {
        background: none;

        .company {
            background: rgba(255, 255, 255, 0.54);
            // border: 1px solid rgba(14, 146, 102, 0.60);
            border: 1px solid #009a3e;
        }

        .form {
            :global(.van-search) {
                background: rgba(255, 255, 255, 0.54) !important;
                // border: 1px solid rgba(14, 146, 102, 0.60);
                border: 1px solid #009a3e;
                border-radius: 16px;
            }

            :global(.van-search__content) {
                background: rgba(255, 255, 255, 0.54);
            }
        }
    }
}