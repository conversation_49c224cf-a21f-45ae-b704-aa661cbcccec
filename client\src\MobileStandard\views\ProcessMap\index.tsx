import { Component } from 'vue-property-decorator';
import styles from './process-map.module.less';
import { IndexMixins } from './IndexMixins';
import { mixins } from 'vue-class-component';
import { getComponentFromProp } from '@/MobileStandard/common/utils';

@Component
export class ProcessMap extends mixins(IndexMixins) {
  render() {
    const processEfficiencyDom = getComponentFromProp(this, 'processEfficiency');
    return (
      <main-menu tabbarActive='process-map'>
        <template slot='content'>
          <tool-bar
            breadcrumbIndex={this.breadcrumbIndex}
            on-change={(searchData: any) => this.onToolBarChange(searchData)}
          ></tool-bar>
          {
            this.breadcrumbIndex > 0 ?
              <div class={styles.breadcrumb_content}><breadcrumb breadcrumb={this.breadcrumb}
                on-click={(breadcrumbName: any, breadcrumbValue: any, breadcrumbIndex: any) =>
                  this.onBreadcrumbChange(breadcrumbName, breadcrumbValue, breadcrumbIndex)} /></div> : ''
          }
          <div class={styles.process_content + (this.breadcrumbIndex > 0 ? ' ' + styles.process_content_more : '')}>
            <div class={styles.main_conetnt + (this.breadcrumbIndex === 0 &&
              processEfficiencyDom ? ' ' + styles.dom_content : '')}>
              {
                this.breadcrumbIndex === 0 ? processEfficiencyDom : ''
              }
              <process data={{ process: this.data, commonProcess: this.favoriteProcesses }}
                breadcrumbIndex={this.breadcrumbIndex}
                on-class-change={(classObject: any) => this.onClassChange(classObject)}
                on-process-click={(processObject: any) => this.onProcessClick(processObject)}
              />
            </div>
          </div>
        </template>
      </main-menu>
    );
  }
}
