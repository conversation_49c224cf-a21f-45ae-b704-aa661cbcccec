import { Observable } from 'rxjs';
import { httpHelper } from '@/MobileStandard/common/utils';

class ProcessMapService {
    getIntroduction(id: any): Observable<any> {
        const url = `/api/process/v1/processes/${id}/introduction`;
        return httpHelper.get(url, {}, { loading: false });
    }
    favoriteProcesses(params: any): Observable<any> {
        const _url = '/api/process/v1/favorite-processes';
        return httpHelper.get(_url, { params: params });
    }
    myPositionDomainBusinessTypeScope(): Observable<any> {
        const _url = '/api/process/v1/process-map/myPositionDomainBusinessTypeScope?type=1';
        return httpHelper.get(_url);
    }
    processMap(params: any): Observable<any> {
        const _url = '/api/process/v1/process-map';
        return httpHelper.get(_url, { params: params });
    }
    setFavorite(processId: string): Observable<string> {
        const url = `/api/process/v1/processes/${processId}/favorite-processes`;
        return httpHelper.post(url, {}, {}, { loading: false });
    }
}

export const processMapService = new ProcessMapService();
