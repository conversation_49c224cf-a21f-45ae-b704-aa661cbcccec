<div id="custom-form">
	<van-form ref="coreInfoForm" v-if="completeFieldRight">
		<instance-collapse-item title="表单信息" :expand="true">
			<div class="custom_table_content" v-if="attributes[`FKXGXX`].display">
				<div v-html="FKXGXXHtml" class="custom_table"></div>
				<img :src="hengping" class="custom_table_hengping"  @click="fullScreen()/>
			</div>
			<div class="custom_table_button" v-if="attributes[`FKXGXX`].display && attributes[`FKXGXX`].editable && !attributes[`FKXGXX`].disabled && true" @click="addTableRow('FKXGXX')">
				<i class="iconfont icon-btn_addtabe" />
				<span>添加</span>
			</div>
			<van-action-sheet v-if="attributes[`FKXGXX`].display" v-model="attributes[`FKXGXX`].ShowSheet" @click-overlay="closeTableRow('FKXGXX')" :round="false" class="custom_sheet">
				<div class="custom_title">
					<van-icon class="custom_title_close" name="arrow-down" size="20" @click="closeTableRow('FKXGXX')" />
					<div class="custom_title_buttons">
						<van-button class="custom_title_button" type="danger" size="small" @click="deleteTableRow('FKXGXX')" v-if="attributes[`FKXGXX`].editable && !attributes[`FKXGXX`].disabled && true">删除</van-button>
						<van-button class="custom_title_button" type="info" size="small" @click="copyAndAddTableRow('FKXGXX')" v-if="attributes['FKXGXX'].editable && !attributes[`FKXGXX`].disabled && true">复制并添加 </van-button>
						<van-button v-if="attributes[`FKXGXX`].editable && !attributes[`FKXGXX`].disabled" class="custom_title_button" type="info" size="small" @click="saveTableRow('FKXGXX')">保存</van-button>
					</div>
				</div>
				<div class="custom_title_tip">当前编辑的是第{{record.index + 1}}行</div>
				<van-form ref="coreInfoForm_table" v-if="record.rid">
					<van-field :placeholder="attributes['FKXGXX_FKJE_'+ record.rid].placeholder" :required="attributes['FKXGXX_FKJE_'+ record.rid].editable && lodash.some(rules['FKXGXX_FKJE_'+ record.rid], (so) => so.required)" :label="attributes['FKXGXX_FKJE_'+ record.rid].label" input-align="right" error-message-align="right" :rules="rules['FKXGXX_FKJE_'+ record.rid]" :class="attributes['FKXGXX_FKJE_'+ record.rid].class" v-if="attributes['FKXGXX_FKJE_'+ record.rid].display" :readonly="!attributes['FKXGXX_FKJE_'+ record.rid].editable || attributes['FKXGXX_FKJE_'+ record.rid].disabled" :clearable="attributes['FKXGXX_FKJE_'+ record.rid].editable && !attributes['FKXGXX_FKJE_'+ record.rid].disabled" :value="value.FKXGXX[record.index].FKJE" @input="function(e) {handleChange(e, 'FKJE', record.index, record, 'FKXGXX');}" />
					<van-field :placeholder="attributes['FKXGXX_FKZH_'+ record.rid].placeholder" :required="attributes['FKXGXX_FKZH_'+ record.rid].editable && lodash.some(rules['FKXGXX_FKZH_'+ record.rid], (so) => so.required)" :label="attributes['FKXGXX_FKZH_'+ record.rid].label" input-align="right" error-message-align="right" :rules="rules['FKXGXX_FKZH_'+ record.rid]" :class="attributes['FKXGXX_FKZH_'+ record.rid].class" v-if="attributes['FKXGXX_FKZH_'+ record.rid].display" :readonly="!attributes['FKXGXX_FKZH_'+ record.rid].editable || attributes['FKXGXX_FKZH_'+ record.rid].disabled" :clearable="attributes['FKXGXX_FKZH_'+ record.rid].editable && !attributes['FKXGXX_FKZH_'+ record.rid].disabled" :value="value.FKXGXX[record.index].FKZH" @input="function(e) {handleChange(e, 'FKZH', record.index, record, 'FKXGXX');}" />
					<van-field :placeholder="attributes['FKXGXX_FKDW_'+ record.rid].placeholder" :required="attributes['FKXGXX_FKDW_'+ record.rid].editable && lodash.some(rules['FKXGXX_FKDW_'+ record.rid], (so) => so.required)" :label="attributes['FKXGXX_FKDW_'+ record.rid].label" input-align="right" error-message-align="right" :rules="rules['FKXGXX_FKDW_'+ record.rid]" :class="attributes['FKXGXX_FKDW_'+ record.rid].class" v-if="attributes['FKXGXX_FKDW_'+ record.rid].display" :readonly="!attributes['FKXGXX_FKDW_'+ record.rid].editable || attributes['FKXGXX_FKDW_'+ record.rid].disabled" :clearable="attributes['FKXGXX_FKDW_'+ record.rid].editable && !attributes['FKXGXX_FKDW_'+ record.rid].disabled" :value="value.FKXGXX[record.index].FKDW" @input="function(e) {handleChange(e, 'FKDW', record.index, record, 'FKXGXX');}" />
					<van-field :placeholder="attributes['FKXGXX_FKJE1_'+ record.rid].placeholder" :required="attributes['FKXGXX_FKJE1_'+ record.rid].editable && lodash.some(rules['FKXGXX_FKJE1_'+ record.rid], (so) => so.required)" :label="attributes['FKXGXX_FKJE1_'+ record.rid].label" input-align="right" error-message-align="right" :rules="rules['FKXGXX_FKJE1_'+ record.rid]" :class="attributes['FKXGXX_FKJE1_'+ record.rid].class" v-if="attributes['FKXGXX_FKJE1_'+ record.rid].display" :readonly="!attributes['FKXGXX_FKJE1_'+ record.rid].editable || attributes['FKXGXX_FKJE1_'+ record.rid].disabled" :clearable="attributes['FKXGXX_FKJE1_'+ record.rid].editable && !attributes['FKXGXX_FKJE1_'+ record.rid].disabled" :value="value.FKXGXX[record.index].FKJE1" @input="function(e) {handleChange(e, 'FKJE1', record.index, record, 'FKXGXX');}" />
					<van-field :placeholder="attributes['FKXGXX_FKJE2_'+ record.rid].placeholder" :required="attributes['FKXGXX_FKJE2_'+ record.rid].editable && lodash.some(rules['FKXGXX_FKJE2_'+ record.rid], (so) => so.required)" :label="attributes['FKXGXX_FKJE2_'+ record.rid].label" input-align="right" error-message-align="right" :rules="rules['FKXGXX_FKJE2_'+ record.rid]" :class="attributes['FKXGXX_FKJE2_'+ record.rid].class" v-if="attributes['FKXGXX_FKJE2_'+ record.rid].display" :readonly="!attributes['FKXGXX_FKJE2_'+ record.rid].editable || attributes['FKXGXX_FKJE2_'+ record.rid].disabled" :clearable="attributes['FKXGXX_FKJE2_'+ record.rid].editable && !attributes['FKXGXX_FKJE2_'+ record.rid].disabled" :value="value.FKXGXX[record.index].FKJE2" @input="function(e) {handleChange(e, 'FKJE2', record.index, record, 'FKXGXX');}" />
				</van-form>
			</van-action-sheet>
            <van-popup v-model="attributes.ShowFullScreen">
                <div v-html="FKXGXXHtml" class="custom_table"></div>
            </van-popup>
		</instance-collapse-item>
	</van-form>
</div>
<script>
	window.component = {
	data: function() {
		return {
			"form": undefined,
			"loadCreated": false,
			"completeFieldRight": false,
			"debounceHandle": this.lodash.debounce(this.debounceHandleChange, 500),
			"FKXGXXHtml": "",
			"record": {
				rid: "",
				index: -1
			},
			"value": {
				"FKXGXX": [],
				"FL": "",
				"XMMC": "",
				"FormTransportType": "",
				"FKXGXX_FKDW": "",
				"FKXGXX_FKJE": "",
				"FKXGXX_FKZH": "",
				"FKXGXX_FKJE1": "",
				"FKXGXX_FKJE2": "",
				"FKXGXX_FKJE3": "",
				"FKXGXX_FKJE4": "",
				"FKXGXX_FKJE5": "",
				"FKXGXX_FKJE6": ""
			},
			"attributes": {
                "ShowFullScreen": false,
                "isShowFullScreen": false,
				"FKXGXX": {
					"label": "明细子表",
					"placeholder": "明细子表",
					"display": true,
					"disabled": false,
					"controlType": "mmt-table",
					"class": "",
					"editable": false,
					"Cdata": [],
					"CurrentEditRid": "",
					"HasSerialColumn": true,
					"ColumnHeaders": [{
						"title": "付款金额",
						"id": "5b0c958d-41fa-5be5-0e7e-c54d4737e966",
						"align": "left",
						"width": 200,
						"children": []
					}, {
						"title": "付款账号",
						"id": "40971d10-3172-49e6-0f26-df5de8f54d93",
						"align": "left",
						"width": 200,
						"children": []
					}, {
						"title": "付款单位",
						"id": "593d89ae-514d-e631-dc99-e2b2b1a43fc1",
						"align": "left",
						"width": 200,
						"children": []
					}, {
						"title": "付款金额1",
						"id": "6d8bf950-e3e8-9d40-b73d-419e9ac9d564",
						"align": "left",
						"width": 200,
						"children": []
					}, {
						"title": "付款金额2",
						"id": "e169711e-1a37-4bae-c59e-4920dfa1d508",
						"align": "left",
						"width": 200,
						"children": []
					}],
					"Pagination": false,
					"HasRowDeleteButton": true,
					"ColumnFilters": true,
					"ColumnSorter": true,
					"ColumnHeaders_Slots": null,
					"ColumnHeaders_ControlKey": ["FKJE", "FKZH", "FKDW", "FKJE1", "FKJE2"],
					"ColumnHeaders_Width": 0,
					"ShowSheet": false
				},
				"FKXGXX_FKJE": {
					"label": "付款金额",
					"placeholder": "请输入付款金额",
					"display": true,
					"disabled": false,
					"controlType": "mmt-input",
					"class": "",
					"editable": false,
					"Cdata": ""
				},
				"FKXGXX_FKZH": {
					"label": "付款账号",
					"placeholder": "请输入付款账号",
					"display": true,
					"disabled": false,
					"controlType": "mmt-input",
					"class": "",
					"editable": false,
					"Cdata": ""
				},
				"FKXGXX_FKDW": {
					"label": "付款单位",
					"placeholder": "请输入付款单位",
					"display": true,
					"disabled": false,
					"controlType": "mmt-input",
					"class": "",
					"editable": false,
					"Cdata": ""
				},
				"FKXGXX_FKJE1": {
					"label": "付款金额1",
					"placeholder": "请输入付款金额1",
					"display": true,
					"disabled": false,
					"controlType": "mmt-input",
					"class": "",
					"editable": false,
					"Cdata": ""
				},
				"FKXGXX_FKJE2": {
					"label": "付款金额2",
					"placeholder": "请输入付款金额2",
					"display": true,
					"disabled": false,
					"controlType": "mmt-input",
					"class": "",
					"editable": false,
					"Cdata": ""
				},
				"FL": {
					"display": true,
					"controlType": "",
					"editable": false
				},
				"XMMC": {
					"display": true,
					"controlType": "",
					"editable": false
				},
				"FormTransportType": {
					"display": true,
					"controlType": "",
					"editable": false
				},
				"FKXGXX_FKJE3": {
					"display": true,
					"controlType": "",
					"editable": false
				},
				"FKXGXX_FKJE4": {
					"display": true,
					"controlType": "",
					"editable": false
				},
				"FKXGXX_FKJE5": {
					"display": true,
					"controlType": "",
					"editable": false
				},
				"FKXGXX_FKJE6": {
					"display": true,
					"controlType": "",
					"editable": false
				}
			},
			"rules": {}
		};
	},
	methods: {
		initEditable: function() {
			if (this.editable) {
				Object.keys(this.attributes)
					.forEach(f => {
						this.attributes[f].editable = true;
					})
			}
		},
		addTableRow: function(key) {
			var cdata = {
				rid: this.guidHelper.generate()
			};
			this.record.rid = cdata.rid;
			this.record.index = this.value[key].length;
			this.lodash.forEach(this.attributes[key].ColumnHeaders_ControlKey, (f) => {
				if (f) {
					this.$set(this.attributes, `${key}_${f}_${cdata.rid}`, this.lodash.cloneDeep(this.attributes[`${key}_${f}`]));
					if (this.rules[`${key}_${f}`]) {
						this.$set(this.rules, `${key}_${f}_${cdata.rid}`, this.lodash.cloneDeep(this.rules[`${key}_${f}`]));
					}
					cdata[f] = this.attributes[`${key}_${f}`].Cdata;
				}
			});
			if (this.value[key]) {
				this.value[key].push(cdata)
			} else {
				this.$set(this.value, key, [cdata]);
			}
			this.attributes[key].ShowSheet = true;
			this.initTableHtml(key);
		},
		closeTableRow: function(key) {
			this.record.rid = "";
			this.record.index = -1;
			this.attributes[`${key}`].ShowSheet = false;
			this.initTableHtml(key);
		},
		deleteTableRow: function(key) {
			var omitKeys = [];
			this.lodash.forEach(this.attributes[key].ColumnHeaders_ControlKey, f => {
				omitKeys.push(`${key}_${f}_${this.record.rid}`);
			});
			this.attributes = this.lodash.omit(this.attributes, omitKeys);
			this.rules = this.lodash.omit(this.rules, omitKeys);
			this.value[key].splice(this.record.index, 1);
			this.record.index = -1;
			this.record.rid = "";
			this.attributes[`${key}`].ShowSheet = false;
			this.initTableHtml(key);
		},
		copyAndAddTableRow: function(key) {
			var copyRowValue = this.lodash.cloneDeep(this.value[key][this.record.index]);
			copyRowValue.rid = this.guidHelper.generate();
			this.value[key].push(copyRowValue);
			this.lodash.forEach(this.attributes[key].ColumnHeaders_ControlKey, (f) => {
				this.$set(this.attributes, `${key}_${f}_${copyRowValue.rid}`, this.lodash.cloneDeep(this.attributes[`${key}_${f}_${this.record.rid}`]));
				if (this.rules[`${key}_${f}_${this.record.rid}`]) {
					this.$set(this.rules, `${key}_${f}_${copyRowValue.rid}`, this.lodash.cloneDeep(this.rules[`${key}_${f}_${this.record.rid}`]));
				}
			});
			this.closeTableRow(key);
		},
		saveTableRow: function(key) {
			this.$refs.coreInfoForm_table.validate()
				.then(() => {
					this.record.index = -1;
					this.record.rid = "";
					this.attributes[key].ShowSheet = false;
					this.initTableHtml(key);
				});
		},
		initTableColumns: function(key) {
			var columns = [];
			if (this.attributes[key].HasSerialColumn) {
				columns.push({
					"title": "序号",
					"align": "center",
					"width": 50
				});
				this.attributes[key].ColumnHeaders_Width += 50;
			}
			var recColumns = this.recursionTableColumns(key, this.attributes[key].ColumnHeaders, 0);
			columns.push(...recColumns.t);
			return columns;
		},
		recursionTableColumns: function(key, items, index) {
			var temp = [];
			this.lodash.map(items, (m) => {
				var co = {};
				if (!m.children || m.children.length === 0) {
					var controlKey = this.attributes[key].ColumnHeaders_ControlKey[index];
					if (controlKey && this.attributes[`${key}_${controlKey}`].display) {
						co["title"] = m.title;
						co["dataIndex"] = controlKey;
						co["width"] = m.width;
						co["align"] = m.align;
						this.attributes[`${key}_${controlKey}`]['width'] = m.width;
						this.attributes[`${key}_${controlKey}`]['align'] = m.align;
						this.attributes[key].ColumnHeaders_Width += m.width;
						temp.push(co);
					}
					index += 1;
				} else {
					co["title"] = m.title;
					co["align"] = m.align;
					const res = this.recursionTableColumns(key, m.children, index);
					co["children"] = res.t;
					index = res.i;
					if (co["children"].length > 0) {
						temp.push(co);
					}
				}
			});
			return {
				t: temp,
				i: index
			};
		},
		getTableRowNumber: function(column, rows) {
			var r = rows;
			(column.children || [])
			.map((m) => {
				var n = this.getTableRowNumber(m, rows + 1);
				if (n > r) {
					r = n;
				}
			});
			return r;
		},
		getTableColNumber: function(column, cols) {
			var r = cols;
			(column.children || [])
			.map((m) => {
				var n = this.getTableColNumber(m, cols + column.children.length - 1);
				if (n > r) {
					r = n;
				}
			});
			return r;
		},
		changeTableHeaderColumns: function(column, rows, totalRows) {
			column.map((m) => {
				var childCols = (m.children || [])
					.length === 0 ? 1 : 0;
				(m.children || [])
				.map((c) => {
					var n = this.getTableColNumber(c, 1);
					childCols += n;
				});
				m["colspan"] = childCols;
				var childRows = 1;
				(m.children || [])
				.map((c) => {
					var n = this.getTableRowNumber(c, 1);
					if (n > childRows) {
						childRows = n;
					}
				});
				m["rowspan"] = 1;
				if (childRows === 1) {
					if ((m.children || [])
						.length === 0 && rows < totalRows) {
						m["rowspan"] = totalRows - rows + 1;
					}
				}
				this.changeTableHeaderColumns(m.children || [], rows + 1, totalRows);
			});
		},
		getTableHeaderCols: function(columnHeaders, row, maxRow, tableCols) {
			columnHeaders.map((m, i) => {
				var cols = "";
				cols += `<th colspan="${columnHeaders[i].colspan}" rowspan="${columnHeaders[i].rowspan}"style = "text-align:${columnHeaders[i].align};width:${columnHeaders[i].width && columnHeaders[i].width > 0? columnHeaders[i].width: 200}px" >${ m.title}</ th >`;
				tableCols[row - 1] = tableCols[row - 1] ? tableCols[row - 1] + cols : cols;
				this.getTableHeaderCols(m.children || [], row + 1, maxRow, tableCols);
			});
		},
		editTableRow: function(key, rid, i) {
			this.record.rid = rid;
			this.record.index = i;
			this.attributes[key].ShowSheet = true;
		},
		initTableHtml: function(key) {
			this.attributes[key].ColumnHeaders_Width = 0;
			var columns = this.initTableColumns(key);
			var maxRow = 1;
			columns.map((c) => {
				var n = this.getTableRowNumber(c, 1);
				if (n > maxRow) {
					maxRow = n;
				}
			});
			var maxCol = 0;
			columns.map((c) => {
				var n = this.getTableColNumber(c, 1);
				maxCol += n;
			});
			this.changeTableHeaderColumns(columns, 1, maxRow);
			var tableCols = [];
			this.getTableHeaderCols(columns, 1, maxRow, tableCols);
			var tableHeader = "";
			for (var i = 0; i < maxRow; i++) {
				tableHeader += `<tr>${tableCols[i]}</tr>`;
			}
			var tableBodyCols = "";
			this.lodash.forEach(this.value[key], (d, di) => {
				var row = `<tr onClick="editTableRow('${key}','${d.rid}',${di})">`;
				if (this.attributes[key].HasSerialColumn) {
					row += `<td style='text-align:center' class='custom_table_serial_td'>${di + 1}</td>`;
				}
				var cks = this.attributes[key].ColumnHeaders_ControlKey;
				for (var i = 0; i <= cks.length; i++) {
					row += cks[i] && this.attributes[`${key}_${cks[i]}`].display ? `<td style='text-align:${this.attributes[`${key}_${cks[i]}`].align};width:${this.attributes[`${key}_${cks[i]}`].width}px;'>${this.attributes[`${key}_${cks[i]}_${d.rid}`].display ? this.getCustomTableCol(d,cks[i],key,di,i) : ""}</td>` : ""
				}
				row += "</tr>";
				tableBodyCols += row;
			});
            if(this.attributes.isShowFullScreen) {
                var table = `<table class='table_wrap' cellspacing='0'><thead>${tableHeader}</thead>${tableBodyCols}</table>`;
            } else {
                var table = `<table cellspacing='0' style='width:${this.attributes[key].ColumnHeaders_Width}px'><thead>${tableHeader}</thead>${tableBodyCols}</table>`;
            }
			// var table = `<table cellspacing='0' style='width:${this.attributes[key].ColumnHeaders_Width}px'>${tableHeader}${tableBodyCols}</table>`;
			this[`${key}Html`] = table;
		},
        fullScreen() {
            this.attributes.ShowFullScreen = true;
            this.attributes.isShowFullScreen = true;
        },
		getCustomTableCol: function(row, col, tableCode, rowIndex, colIndex) {
			var result = "";
			var controlType = this.attributes[`${tableCode}_${col}_${row.rid}`].controlType;
			switch (controlType) {
				case "mmt-select":
				case "mmt-radio":
				case "mmt-check-box":
					result = this.getOptionLabel(row[col], this.attributes[`${tableCode}_${col}_${row.rid}`].options);
					break;
				case "mmt-input-number":
					result = this.inputNumberText(row[col], `${tableCode}_${col}_${row.rid}`);
					break;
				case "mmt-select-person":
					result = this.userText(row[col]);
					break;
				case "mmt-select-organization":
					result = this.organizationText(row[col]);
					break;
				case "mmt-label":
					result = this.labelFormat(row[col], `${tableCode}_${col}_${row.rid}`);
					break;
				case "mmt-date-picker":
					result = row[col] ? row[col].toString() : "";;
					break;
				case "mmt-hyperlink":
					if (row[col]) {
						result += "<div>";
						this.lodash.forEach(row[col], (m, mi) => {
							result += "<div style='width:100%;'><a target = '_blank'style = 'text-decoration:" + (this.attributes[tableCode + "_" + col + "_" + row.rid].HasUnderline ? 'underline' : 'none') + ";'>" + m.label + "</a></div>";
						});
						result += "</div>"
					}
					break;
				case "mmt-upload-files":
					if (row[col]) {
						result += "<div>";
						this.lodash.forEach(row[col], (m, mi) => {
							result += "<div style='display:flex;'> <div style='position:relative;width:100%;'><div style='position:relative;display:flex;'><span style='text-decoration:underline;flex:1;color:#0e9266;'>" + m.name + "</span></div></div></div>";
						});
					}
					break;
				case "mmt-traffic-light":
					var style = this.trafficLight(row[col]);
					result = "<div style=" + style + "/>";
					break;
				default:
					result = row[col] ? row[col].toString() : "";
					break;
			}
			return result;
		},
		submitCheck: function(boid, number, processId, processVersion) {
			var messages = [];
			return new Promise((res, rej) => {
				var requireRule = this.lodash.find(this.rules.FKXGXX, (f) => {
					return f.required;
				});
				if (requireRule && this.value.FKXGXX.length === 0) {
					messages.push(requireRule.message);
				}
				if (messages.length > 0) {
					res(messages);
				}
				res([]);
			});
		},
		debounceHandleChange: function(key, index, record, pKey) {
			this.valueChange();
		},
		handleChange: function(value, key, index, record, pKey) {
			var tKey = pKey ? `${pKey}_${key}_${record.rid}` : key;
			switch (this.attributes[tKey].controlType) {
				case "mmt-select":
				case "mmt-radio":
					this.setControlCdata(value, key, index, record, pKey);
					if (this.attributes[tKey].TextDataKey) {
						this.setControlCdata("", this.attributes[tKey].TextDataKey, index, record, pKey);
						if (value) {
							var opt = this.lodash.find(this.attributes[tKey].options, o => o.value === value);
							this.setControlCdata(opt ? opt.label : "", this.attributes[tKey].TextDataKey, index, record, pKey);
						}
					}
					break;
				case "mmt-check-box":
					this.setControlCdata(value.join(";"), key, index, record, pKey);
					if (this.attributes[tKey].TextDataKey) {
						this.setControlCdata("", this.attributes[tKey].TextDataKey, index, record, pKey);
						if (value) {
							var opts = this.lodash.map(value, (v) => {
								var opt = this.lodash.find(this.attributes[tKey].options, (o) => o.value === value);
								return opt ? opt.label : "";
							});
							this.setControlCdata(opts.join(";"), this.attributes[tKey].TextDataKey, index, record, pKey);
						}
					}
					break;
				case "mmt-date-picker":
					if (this.attributes[tKey].DisplayModal === "daterange") {
						this.setControlCdata(value ? this.lodash.trim(value.join(";"), ";") : "", key, index, record, pKey);
					} else {
						this.setControlCdata(value, key, index, record, pKey);
					}
					break;
				case "mmt-select-organization":
					this.setControlCdata(value, key, index, record, pKey);
					if (this.attributes[tKey].FillField) {
						if (this.attributes[tKey].FillField.name) {
							const fillVal = value ? this.lodash.map(value, (v) => v.organizationName)
								.join(";") : "";
							this.setControlCdata(fillVal, this.attributes[tKey].FillField.name, index, record, pKey);
							this.setControlFormCdata(fillVal, this.attributes[tKey].FillField.name, index, record, pKey);
						}
						if (this.attributes[tKey].FillField.code) {
							const fillVal = value ? this.lodash.map(value, (v) => v.organizationCode)
								.join(";") : "";
							this.setControlCdata(fillVal, this.attributes[tKey].FillField.code, index, record, pKey);
							this.setControlFormCdata(fillVal, this.attributes[tKey].FillField.code, index, record, pKey);
						}
					}
					break;
				case "mmt-select-person":
					this.setControlCdata(value, key, index, record, pKey);
					if (this.attributes[tKey].FillField) {
						if (this.attributes[tKey].FillField.dept) {
							const fillVal = value ? this.lodash.map(value, (v) => v.organizationNamePath)
								.join(";") : "";
							this.setControlCdata(fillVal, this.attributes[tKey].FillField.dept, index, record, pKey);
							this.setControlFormCdata(fillVal, this.attributes[tKey].FillField.dept, index, record, pKey);
						}
						if (this.attributes[tKey].FillField.email) {
							const fillVal = value ? this.lodash.map(value, (v) => v.email)
								.join(";") : "";
							this.setControlCdata(fillVal, this.attributes[tKey].FillField.email, index, record, pKey);
							this.setControlFormCdata(fillVal, this.attributes[tKey].FillField.email, index, record, pKey);
						}
						if (this.attributes[tKey].FillField.name) {
							const fillVal = value ? this.lodash.map(value, (v) => v.userName)
								.join(";") : "";
							this.setControlCdata(fillVal, this.attributes[tKey].FillField.name, index, record, pKey);
							this.setControlFormCdata(fillVal, this.attributes[tKey].FillField.name, index, record, pKey);
						}
						if (this.attributes[tKey].FillField.userLoginId) {
							const fillVal = value ? this.lodash.map(value, (v) => v.userLoginId)
								.join(";") : "";
							this.setControlCdata(fillVal, this.attributes[tKey].FillField.userLoginId, index, record, pKey);
							this.setControlFormCdata(fillVal, this.attributes[tKey].FillField.userLoginId, index, record, pKey);
						}
					}
					break;
				default:
					this.setControlCdata(value, key, index, record, pKey);
					break;
			}
			this.debounceHandle(key, index, record, pKey);
		},
		setControlCdata: function(value, key, index, record, pKey) {
			if (pKey) {
				this.value[pKey][index][key] = value;
			} else {
				this.value[key] = value;
			}
		},
		setControlFormCdata: function(value, key, index, record, pKey) {},
		setValue: function(data) {
			if (data) {
				this.lodash.forEach(this.lodash.keys(data), d => {
					if (this.attributes[d]) {
						if (this.attributes[d].controlType === "mmt-table") {
							if (this.lodash.isArray(data[d])) {
								var forData = this.lodash.map(data[d], (dd) => {
									return {
										...dd,
										rid: this.guidHelper.generate()
									};
								});
								this.value[d] = forData;
								this.attributes[d].cdata = forData;
								for (var i = 0; i < forData.length; i++) {
									this.lodash.forEach(this.attributes[d].ColumnHeaders_ControlKey, (f) => {
										if (f) {
											this.$set(this.attributes, `${d}_${f}_${forData[i].rid}`, this.lodash.cloneDeep(this.attributes[`${d}_${f}`]));
											this.$set(this.rules, `${d}_${f}_${forData[i].rid}`, this.lodash.cloneDeep(this.rules[`${d}_${f}`]));
											this.attributes[`${d}_${f}_${forData[i].rid}`].cdata = this.attributes[d].cdata[i][f];
										}
									});
								}
								this.initTableHtml(d);
							}
						} else {
							this.value[d] = data[d];
							this.attributes[d].cdata = data[d];
						}
					} else {
						this.value[d] = data[d];
					}
				});
			}
			this.valueChange();
		},
		setFieldRight: function(data) {
			if (data) {
				this.lodash.forEach(this.lodash.keys(data), m => {
					if (data[m].type === "主表") {
						this.lodash.forEach(data[m].children, c => {
							if (this.attributes[c.value]) {
								if (c.lockkj == "open") {
									this.attributes[c.value].display = c.kj == "1" ? true : false;
								}
								if (c.lockkx == "open") {
									this.attributes[c.value].editable = c.kx == "1" ? true : false;
								}
								if (c.lockbt == "open") {
									if (!this.rules[c.value]) {
										this.rules[c.value] = [];
									}
									var emptyIndex = this.rules[c.value].findIndex((f) => f.required);
									if (c.bt == "1" && emptyIndex == -1) {
										this.rules[c.value].push({
											required: true,
											message: `${this.attributes[c.value].label} -必填项未输入`,
										});
									} else if (c.bt == "0" && emptyIndex > -1) {
										this.rules[c.value].splice(emptyIndex, 1);
									}
								}
							}
						});
					} else if (data[m].type === "明细表") {
						if (this.attributes[data[m].value]) {
							if (data[m].tablehide == "0") {
								this.attributes[data[m].value].display = true;
							} else if (data[m].tablehide == "1") {
								this.attributes[data[m].data].display = false;
							}
						}
						this.lodash.forEach(data[m].children, c => {
							if (this.attributes[`${data[m].value}_${c.value}`]) {
								if (c.lockkj == "open") {
									this.attributes[`${data[m].value}_${c.value}`].display = c.kj == "1" ? true : false;
								}
								if (c.lockkx == "open") {
									this.attributes[`${data[m].value}_${c.value}`].editable = c.kx == "1" ? true : false;
								}
								if (c.lockbt == "open") {
									if (!this.rules[`${data[m].value}_${c.value}`]) {
										this.rules[`${data[m].value}_${c.value}`] = [];
									}
									var emptyIndex = this.rules[`${data[m].value}_${c.value}`].findIndex((f) => f.required);
									if (c.bt == "1" && emptyIndex == -1) {
										this.rules[`${data[m].value}_${c.value}`].push({
											required: true,
											message: `${this.attributes[`${data[m].value}_${c.value}`].label} -必填项未输入`,
										});
									} else if (c.bt == "0" && emptyIndex > -1) {
										this.rules[`${data[m].value}_${c.value}`].splice(emptyIndex, 1);
									}
								}
								if (this.value[data[m].value] && this.lodash.isArray(this.value[data[m].value])) {
									for (var i = 0; i < this.value[data[m].value].length; i++) {
										if (this.attributes[`${data[m].value}_${c.value}_${i}`]) {
											if (c.lockkj == "open") {
												this.attributes[`${data[m].value}_${c.value}_${i}`].display = c.kj == "1" ? true : false;
											}
											if (c.lockkx == "open") {
												this.attributes[`${data[m].value}_${c.value}_${i}`].editable = c.kx == "1" ? true : false;
											}
											if (c.lockbt == "open") {
												if (!this.rules[`${data[m].value}_${c.value}_${this.value[data[m].value].rid}`]) {
													this.rules[`${data[m].value}_${c.value}_${this.value[data[m].value].rid}`] = [];
												}
												var emptyIndex = this.rules[`${data[m].value}_${c.value}_${this.value[data[m].value].rid}`].findIndex((f) => f.required);
												if (c.bt == "1" && emptyIndex == -1) {
													this.rules[`${data[m].value}_${c.value}_${i}`].push({
														required: true,
														message: `${this.attributes[`${data[m].value}_${c.value}_${i}`].label } -必填项未输入`,
													});
												} else if (c.bt == "0" && emptyIndex > -1) {
													this.rules[`${data[m].value}_${c.value}_${this.value[data[m].value].rid}`].splice(emptyIndex, 1);
												}
											}
										}
									}
								}
							}
						});
					}
				});
			}
			this.completeFieldRight = true;
		},
		SUM: function(arr) {
			if (typeof arr === "object" && arr instanceof Array && arr.length > 0) {
				return arr.reduce(function(prev, curr, idx, arr) {
					return curr ? prev + curr : prev;
				});
			} else {
				return arr;
			}
		},
		MAX: function(arr) {
			if (typeof arr === "object" && arr instanceof Array && arr.length > 0) {
				return Math.max.apply(null, arr);
			} else {
				return arr;
			}
		},
		MIN: function(arr) {
			if (typeof arr === "object" && arr instanceof Array && arr.length > 0) {
				return Math.min.apply(null, arr);
			} else {
				return arr;
			}
		},
		AVG: function(arr) {
			if (typeof arr === "object" && arr instanceof Array && arr.length > 0) {
				return this.SUM(arr) / arr.length;
			} else {
				return arr;
			}
		},
		COUNT: function(arr) {
			return arr.length;
		},
		ZhMoney: function(arr) {
			if (!isNaN(arr)) {
				var cnNums = new Array('零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖');
				var cnIntRadice = new Array('', '拾', '佰', '仟');
				var cnIntUnits = new Array('', '万', '亿', '兆');
				var cnDecUnits = new Array('角', '分', '毫', '厘');
				var cnInteger = '整';
				var cnIntLast = '元';
				var maxNum = 999999999999999.9999;
				var decimalNum;
				var chineseStr = '';
				var parts;
				if (arr == '') {
					return '';
				}
				var money = parseFloat(arr);
				if (money >= maxNum) {
					return '';
				}
				if (money == 0) {
					chineseStr = cnNums[0] + cnIntLast + cnInteger;
					return chineseStr;
				}
				money = money.toString();
				if (money.indexOf('.') == -1) {
					integerNum = money;
					decimalNum = '';
				} else {
					parts = money.split('.');
					integerNum = parts[0];
					decimalNum = parts[1].substr(0, 4);
				}
				if (parseInt(integerNum, 10) > 0) {
					var zeroCount = 0;
					var IntLen = integerNum.length;
					for (var i = 0; i < IntLen; i++) {
						var n = integerNum.substr(i, 1);
						var p = IntLen - i - 1;
						var q = p / 4;
						var m = p % 4;
						if (n == '0') {
							zeroCount++;
						} else {
							if (zeroCount > 0) {
								chineseStr += cnNums[0];
							}
							zeroCount = 0;
							chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
						}
						if (m == 0 && zeroCount < 4) {
							chineseStr += cnIntUnits[q];
						}
					}
					chineseStr += cnIntLast;
				}
				if (decimalNum != '') {
					var decLen = decimalNum.length;
					for (var i = 0; i < decLen; i++) {
						var n = decimalNum.substr(i, 1);
						if (n != '0') {
							chineseStr += cnNums[Number(n)] + cnDecUnits[i];
						}
					}
				}
				if (chineseStr == '') {
					chineseStr += cnNums[0] + cnIntLast + cnInteger;
				} else if (decimalNum == '') {
					chineseStr += cnInteger;
				}
				return chineseStr;
			}
			return '';
		}
	},
	created: function() {
		if (!this.loadCreated) {
			this.initEditable();
			window.editTableRow = this.editTableRow;
			this.initTableHtml("FKXGXX");
			this.loadCreated = true
		}
	}
};
</script>