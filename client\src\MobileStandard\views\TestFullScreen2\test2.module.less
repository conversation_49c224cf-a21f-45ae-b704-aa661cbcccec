.action_sheet {
    // position: fixed;
    // height: 100%;
    // width: 100%;
    // max-height: 100% !important;

    // :global(.van-action-sheet__content) {
    //     background: #ffffff;
    // }
}
.henping {
    // transform-origin: top right;
    // transform: rotateZ(90deg) translateX(100%);
    // color:#333333;
    // padding:10px;
    // height: 100vw;
    // width: 100vh;
}
.table{
    // table-layout: fixed;
    // word-wrap: break-word;
    // word-break: break-all;
    // text-align: center;
    // overflow: auto;
    // width: 100%;
    // height: 100%;
    // //top: 35%;    
    // left: 100vh;
    // -webkit-transform: rotate(90deg);
    // -moz-transform: rotate(90deg);
    // -ms-transform: rotate(90deg);
    // background: red;
    // position: absolute;
    // transform-origin: 0% 0%;
}
// .fullScreen{
//     overflow: auto;
//     position: absolute;
//     width: 100vh;
//     height: 100vw;
//     top: 0;
//     left: 100vw;
//     -webkit-transform: rotate(90deg);
//     -moz-transform: rotate(90deg);
//     -ms-transform: rotate(90deg);
//     transform: rotate(90deg);
//     transform-origin: 0% 0%;
// }
@media screen and (orientation: portrait) {
    .table {
        // overflow: auto;
        // position: absolute;
        // width: 100vh;
        // height: 100vw;
        // top: 0;
        // left: 100vw;
        // -webkit-transform: rotate(90deg);
        // -moz-transform: rotate(90deg);
        // -ms-transform: rotate(90deg);
        // transform: rotate(90deg);
        // transform-origin: 0% 0%;
    }
}

@media screen and (orientation: landscape) {
    .table {
        // position: absolute;
        // top: 0;
        // left: 0;
        // width: 100vw;
        // height: 100vh;
        // overflow: auto;
    }
}
.outfx {
    // -webkit-transform: rotate(90deg);
    // // width: 100vh;
    //  position: absolute;
    //     width: 100vh;
    //     // height: 100vw;
    //     top: 0;
    //     // left: 100vw;
}
.table_wrap {
    width:100%;
    // height: 80vh;
    // overflow: auto;
    border-bottom:1px solid #61dafb;
    // -webkit-transform: rotate(90deg);
    // width: 100vh;
    // transform-origin: 48% 48%;
    table {
        table-layout: fixed;
        width: 100%;
        border-collapse:separate;
        border-spacing:0;
        border:0;
      }
      
      td, th{
          width:150px;
          box-sizing: border-box;
          border-right:1px solid red;
          border-bottom:1px solid red;
          /*超出长度...*/
          overflow: hidden;
          white-space: nowrap;
          text-overflow: ellipsis;
      }
      
      thead tr th {
          position:sticky;
          top:0;
          // background:#61dafb;
          background:pink;
      }
      
      th:first-child, td:first-child {
          position:sticky;
          left:0;
          background:#61dafb;
      }
      th:first-child {
          z-index:1; /*左上角单元格z-index，切记要设置，不然表格纵向横向滚动时会被该单元格右方或者下方的单元格遮挡*/
          background:pink;
      }
  }