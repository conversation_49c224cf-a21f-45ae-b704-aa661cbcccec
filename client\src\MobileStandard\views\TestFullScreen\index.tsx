import { UserCard } from '@/MobileStandard/components';
import lodash from 'lodash';
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import styles from './test2.module.less';

@Component({
    components: {
        UserCard
    }
})
export class TestFullScreen extends Vue {
    private columns = [{
        title: '第一列',
        dataIndex: 'text1'
    },
    {
        title: '第二列',
        dataIndex: 'text2'
    }, {
        title: '第三列',
        dataIndex: 'text3'
    }, {
        title: '第四列',
        dataIndex: 'text4'
    }, {
        title: '第五列',
        dataIndex: 'text5'
    }, {
        title: '第六列',
        dataIndex: 'text6'
    }, {
        title: '第七列',
        dataIndex: 'text7'
    }, {
        title: '第八列',
        dataIndex: 'text8'
    }, {
        title: '第九列',
        dataIndex: 'text9'
    }, {
        title: '第十列',
        dataIndex: 'text10'
    }];
    private data = [{
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试11',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试111',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }];
    private tcc = false;
    private resoveData: any = [];
    private xz() {
        this.tcc = !this.tcc;
    }
    private scrollEvent(e: any) {
        const tbodyRightScrollLeft = e.target.scrollLeft;
        const tbodyRightScrollTop = e.target.scrollTop;
        // if ( this.$refs.titsRight) {
        // this.$refs.titsRight.scrollLeft
        // }
        this.$refs.titsRight = tbodyRightScrollLeft;
        this.$refs.tbodyLeft = tbodyRightScrollTop;
        // this.$refs.titsRight.scrollLeft = tbodyRightScrollLeft;
        // this.$refs.tbodyLeft.scrollTop = tbodyRightScrollTop;
    }
    render() {
        return (
            <div style={{ overflow: 'auto', color: '#333333', width: '100%', }}>
                <div class={styles.outfx}>
                    <div class={styles.table_wrap}>
                        <table class={styles.table}>
                            <thead>
                                <tr>
                                    {
                                        lodash.map(this.columns, (c: any) => {
                                            return <th width='100px'>{c.title}</th>;
                                        })
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                {
                                    lodash.map(this.data, (c: any) => {
                                        // console.log(c);
                                        const tds: any = [];
                                        lodash.forEach(this.columns, (cc: any) => {
                                            // console.log(cc.dataIndex);
                                            tds.push(<td>{c[cc.dataIndex]}</td>);
                                        });
                                        return <tr>{tds}</tr>;
                                    })
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
                <i class='iconfont icon-btn_hengping'
                style='color: red;position: fixed;right: 10px;top: 10px;'
                on-click={() => this.xz()}></i>
            </div>
        );
    }
}
