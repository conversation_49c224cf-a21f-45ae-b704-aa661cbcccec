
import { UserCard } from '@/MobileStandard/components';
import lodash from 'lodash';
import { Component, Emit, Prop, Vue } from 'vue-property-decorator';
import styles from './test.module.less';

@Component({
    components: {
        UserCard
    }
})
export class Test extends Vue {
    private columns = [{
        title: '第一列',
        dataIndex: 'text1'
    },
    {
        title: '第二列',
        dataIndex: 'text2'
    }, {
        title: '第三列',
        dataIndex: 'text3'
    }, {
        title: '第四列',
        dataIndex: 'text4'
    }, {
        title: '第五列',
        dataIndex: 'text5'
    }, {
        title: '第六列',
        dataIndex: 'text6'
    }, {
        title: '第七列',
        dataIndex: 'text7'
    }, {
        title: '第八列',
        dataIndex: 'text8'
    }, {
        title: '第九列',
        dataIndex: 'text9'
    }, {
        title: '第十列',
        dataIndex: 'text10'
    }];
    private data = [{
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }, {
        text1: '测试1',
        text2: '测试2',
        text3: '测试3',
        text4: '测试4',
        text5: '测试5',
        text6: '测试6',
        text7: '测试7',
        text8: '测试8',
        text9: '测试9',
        text10: '测试10',
    }];
    private tcc = false;
    private resoveData: any = [];
    private xz() {
        this.tcc = !this.tcc;
    }
    render() {
        return (
            // <user-card userInfo={
            //     {
            //         name: '11',
            //         mobile: '111',
            //         mail: '222',
            //         post: 'cccc',
            //         id: '11'
            //     }
            // }></user-card>
            // <div style={{ overflow: 'auto', background: '#ffffff', color: '#333333' }}>
            //     <table width='1000px'>
            //         <tr>
            //             {
            //                 lodash.map(this.columns, (c: any) => {
            //                     return <th width='100px'>{c.title}</th>;
            //                 })
            //             }
            //         </tr>
            //         {
            //             lodash.map(this.data, (c: any) => {
            //                 console.log(c);
            //                 const tds: any = [];
            //                 lodash.forEach(this.columns, (cc: any) => {
            //                     console.log(cc.dataIndex);
            //                     tds.push(<td>{c[cc.dataIndex]}</td>);
            //                 });
            //                 return <tr>{tds}</tr>;
            //             })
            //         }
            //     </table>
            //     <i class='iconfont icon-btn_hengping'
            //         style='color: red;position: fixed;right: 10px;top: 10px;'
            //         on-click={() => this.xz()}></i>
            //     <van-action-sheet v-model={this.tcc}
            //         round={false}
            //         overlay={false}
            //         class={styles.action_sheet}
            //     >
            //         <div>
            //             <div style='transform-origin: top right;transform: rotateZ(90deg) translateX(100%);color:#333333;'>
            //                 <table style='width:1000px'>
            //                     <tr>
            //                         {
            //                             lodash.map(this.columns, (c: any) => {
            //                                 return <th width='100px'>{c.title}</th>;
            //                             })
            //                         }
            //                     </tr>
            //                     {
            //                         lodash.map(this.data, (c: any) => {
            //                             console.log(c);
            //                             const tds: any = [];
            //                             lodash.forEach(this.columns, (cc: any) => {
            //                                 console.log(cc.dataIndex);
            //                                 tds.push(<td>{c[cc.dataIndex]}</td>);
            //                             });
            //                             return <tr>{tds}</tr>;
            //                         })
            //                     }
            //                 </table>
            //             </div>
            //         </div>
            //         <i class='iconfont icon-btn_hengping'
            //             style='color: red;position: fixed;right: 10px;bottom: 10px;'
            //             on-click={() => this.xz()}></i>
            //     </van-action-sheet>
            // </div>
            <div style={{ overflow: 'auto', background: '#ffffff', color: '#333333' }}>
        {/* <table class={styles.table}>
                    <tr>
                        {
                            lodash.map(this.columns, (c: any) => {
                                return <th width='100px'>{c.title}</th>;
                            })
                        }
                    </tr>
                    {
                        lodash.map(this.data, (c: any) => {
                            console.log(c);
                            const tds: any = [];
                            lodash.forEach(this.columns, (cc: any) => {
                                console.log(cc.dataIndex);
                                tds.push(<td>{c[cc.dataIndex]}</td>);
                            });
                            return <tr>{tds}</tr>;
                        })
                    }
                </table> */}
        <i class='iconfont icon-btn_hengping' style='color: red;position: fixed;right: 10px;top: 10px;' on-click={() => this.xz()}></i>
        <van-action-sheet v-model={this.tcc} round={false} overlay={false} class={styles.action_sheet}>
          <div class={styles.henping}>
            <table border='1' cellspacing='0' style='width:1300px;text-align: center;'>
              <tr>
                {lodash.map(this.columns, (c: any) => {
                  return <th width='100px'>{c.title}</th>;
                })}
              </tr>
              {lodash.map(this.data, (c: any) => {
                console.log(c);
                const tds: any = [];
                lodash.forEach(this.columns, (cc: any) => {
                  console.log(cc.dataIndex);
                  tds.push(<td>{c[cc.dataIndex]}</td>);
                });
                return <tr>{tds}</tr>;
              })}
            </table>
          </div>
          <i class='iconfont icon-btn_hengping' style='color: red;position: fixed;right: 10px;bottom: 10px;' on-click={() => this.xz()}></i>
        </van-action-sheet>
      </div>
        );
    }
}
