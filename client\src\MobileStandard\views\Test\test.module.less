.action_sheet {
    position: fixed;
    height: 100%;
    width: 100%;
    max-height: 100% !important;

    :global(.van-action-sheet__content) {
        background: #ffffff;
    }
}
.henping {
    transform-origin: top right;
    transform: rotateZ(90deg) translateX(100%);
    color:#333333;
    padding:10px;
    height: 100vw;
    width: 100vh;
}
.table{
    table-layout: fixed;
    word-wrap: break-word;
    word-break: break-all;
    text-align: center;
}

@media screen and (orientation: portrait) {
    .table {
        overflow: auto;
        position: absolute;
        width: 100vh;
        height: 100vw;
        top: 0;
        left: 100vw;
        -webkit-transform: rotate(90deg);
        -moz-transform: rotate(90deg);
        -ms-transform: rotate(90deg);
        transform: rotate(90deg);
        transform-origin: 0% 0%;
    }
}

@media screen and (orientation: landscape) {
    .table {
        position: absolute;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        overflow: auto;
    }
}