import { Component, Emit, Prop, Vue } from 'vue-property-decorator';

@Component
export class IndexMixins extends Vue {
    @Prop() tabActive!: string;
    @Prop({ default: false }) isBatch!: boolean;
    @Prop() selectAllInstances!: any;
    @Prop() dataGroup!: any;
    @Emit('change')
    change(checked: boolean, id: any) {
    }
    @Emit('click')
    click(instance: any) {}
    created() {
    }
}
