import { Component, Emit, Vue } from 'vue-property-decorator';
import styles from './todo-center.module.less';
import { ToolBar } from './ToolBar';
import { todoCenterService } from './service';
import lodash from 'lodash';
import { ClassificationMode } from './ClassificationMode';
import { SourceMode } from './SourceMode';
import { MainMenu } from '@/MobileStandard/components';
import { authService } from '@/MobileStandard/services/auth';
import { dateHelper } from '@/MobileStandard/common/utils/date-helper';

@Component({
  components: {
    MainMenu,
    ToolBar,
    ClassificationMode,
    SourceMode
  },
})
export class IndexMixins extends Vue {
  tabActive = 'dbl';
  model = window.localStorage.getItem(authService.user.id || '') || 'urgent';
  searchStr = '';
  data: any = [];
  dataGroup: any = [];
  paging: any = {
    pageSize: 10,
    pageIndex: 1
  };
  refreshing = false;
  loading = false;
  finished = false;
  isBatch = false;
  selectAllInstances: any = [];
  badge = {
    carboncopy: null,
    draft: null,
    task: null,
    archivingTask: null
  };
  dblC: any = null;
  cgC: any = null;
  csC: any = null;
  dgdC: any = null;
  @Emit('tab-change')
  onTabChange(name: any) {
  }
  @Emit('badge-change')
  onBadgeChange(name: any) {
  }
  onTabsChange(name: any, title: any) {
    this.$router.push({ query: { tabActive: name } });
    if (this.tabActive === 'dbl') {
      this.model = window.localStorage.getItem(authService.user.id || '') || 'urgent';
    } else {
      this.model = 'urgent';
    }
    this.searchStr = '';
    this.isBatch = false;
    this.selectAllInstances = [];
    this.paging.pageIndex = 1;
    this.data = [];
    this.loading = true;
    this.finished = false;
    this.onLoadData();
    this.loadMenuNumber();
  }
  onModelChange(model: any) {
    if (authService.user.id) {
      window.localStorage.setItem(authService.user.id, model);
    }
    this.model = model;
    // this.searchStr = '';
    this.paging.pageIndex = 1;
    this.data = [];
    this.loading = true;
    this.finished = false;
    this.isBatch = false;
    this.selectAllInstances = [];
    this.onLoadData();
    this.loadMenuNumber();
  }
  onSearch(search: any, isBatch: boolean = false) {
    this.searchStr = search;
    this.paging.pageIndex = 1;
    this.data = [];
    this.loading = true;
    this.finished = false;
    this.isBatch = isBatch;
    this.selectAllInstances = [];
    this.onLoadData();
  }
  onLoadData() {
    const params: any = {
      PageSize: this.paging.pageSize,
      PageIndex: this.paging.pageIndex,
      keyword: lodash.trim(this.searchStr)
    };
    if (this.tabActive === 'dbl') {
      params['Param1'] = 1;
      if (this.model === 'urgent') {
        todoCenterService.getTodoTasks(params).subscribe(data => {
          this.setDataExtend(data);
        });
      } else if (this.model === 'class') {
        todoCenterService.getUseForGroupName(params).subscribe(data => {
          this.setDataGroupExtend(data);
        });
      } else if (this.model === 'source') {
        todoCenterService.getSubSystem(params).subscribe(data => {
          this.setDataGroupExtend(data);
        });
      }
    } else if (this.tabActive === 'csw') {
      params['StartTime'] = dateHelper.dateFormat(new Date(new Date().setFullYear((new Date()).getFullYear() - 1)));
      params['EndTime'] = dateHelper.dateFormat(new Date());
      todoCenterService.getMyccTasks(params).subscribe(data => {
        this.setDataExtend(data);
      });
    } else if (this.tabActive === 'ybl') {
      params['StartTime'] = dateHelper.dateFormat(new Date(new Date().setFullYear((new Date()).getFullYear() - 1)));
      params['EndTime'] = dateHelper.dateFormat(new Date());
      todoCenterService.getMydoneTasks(params).subscribe(data => {
        this.setDataExtend(data);
      });
    } else if (this.tabActive === 'brfq') {
      params['StartTime'] = dateHelper.dateFormat(new Date(new Date().setFullYear((new Date()).getFullYear() - 1)));
      params['EndTime'] = dateHelper.dateFormat(new Date());
      todoCenterService.getMystartTasks(params).subscribe(data => {
        this.setDataExtend(data);
      });
    } else if (this.tabActive === 'cg') {
      params['SubSystemCode'] = 'BPM';
      todoCenterService.getDraft(params).subscribe(data => {
        this.setDataExtend(data);
      });
    } else {
      this.onTabChange(this.tabActive);
    }
  }
  loadMenuNumber() {
    todoCenterService.getAllTotal({ Carboncopy: 1, Draft: 2, Task: 2, ArchivingTask: 2 }).subscribe(data => {
      this.badge.task = data.Task > 0 ? (data.Task > 999 ? '999+' : data.Task) : null;
      this.badge.draft = data.Draft > 0 ? (data.Draft > 999 ? '999+' : data.Draft) : null;
      this.badge.carboncopy = data.Carboncopy > 0 ? (data.Carboncopy > 999 ? '999+' : data.Carboncopy) : null;
      this.badge.archivingTask = data.ArchivingTask > 0 ? (data.ArchivingTask > 999 ? '999+' : data.ArchivingTask) : null;
      this.onBadgeChange(this.badge);
    });
  }
  setDataExtend(data: any) {
    if (this.refreshing) {
      this.data = [];
      this.refreshing = false;
    }
    this.loading = false;
    const ids: any = [];
    const dd = lodash.map((data.items || []), (m: any) => {
      if (m.startUserLoginId && !lodash.includes(ids, m.startUserLoginId)) {
        ids.push(m.startUserLoginId);
      }
      return {
        ...m,
        avatar: ''
      };
    });
    this.data.push(...dd);
    if (this.data.length === 0 || this.data.length >= data.total) {
      this.finished = true;
    }
    if (ids.length > 0) {
      todoCenterService.getUserAvatar({ keys: ids, cacheType: 2 }).subscribe(avatars => {
        lodash.forEach(avatars, (d: any) => {
          const eqealItems = lodash.filter(this.data, (f: any) => f.startUserLoginId === d.userLoginId);
          lodash.forEach(eqealItems, (e: any) => e.avatar = d.avatar);
        });
      });
    }
  }
  setDataGroupExtend(data: any) {
    if (this.refreshing) {
      this.dataGroup = [];
      this.refreshing = false;
    }
    this.loading = false;
    const ids: any = [];
    this.dataGroup = lodash.map(data, (d: any, di: number) => {
      const dd = lodash.map((d.items || []), (m: any) => {
        if (m.startUserLoginId && !lodash.includes(ids, m.startUserLoginId)) {
          ids.push(m.startUserLoginId);
        }
        return {
          ...m,
          avatar: ''
        };
      });
      d.items = dd;
      return { ...d, isOpen: di === 0 ? true : false, isLoading: false };
    });
    this.finished = true;
    if (ids.length > 0) {
      todoCenterService.getUserAvatar({ keys: ids, cacheType: 2 }).subscribe(avatars => {
        lodash.forEach(this.dataGroup, (dg: any) => {
          lodash.forEach(dg.items, (dgi: any) => {
            const avatar = lodash.find(avatars, (a: any) => a.userLoginId === dgi.startUserLoginId);
            if (avatar) {
              dgi.avatar = avatar.avatar;
            }
          });
        });
      });
    }
  }
  onRefresh() {
    this.paging.pageIndex = 1;
    this.loading = true;
    this.finished = false;
    this.onLoadData();
    this.loadMenuNumber();
  }
  onListLoading() {
    if (this.data.length > 0) {
      this.paging.pageIndex += 1;
    }
    this.onLoadData();
  }
  onBatchApproval(isBatch: boolean) {
    this.searchStr = '';
    this.isBatch = isBatch;
    this.selectAllInstances = [];
    // this.onSearch(this.searchStr);
  }
  onSelectAll(isSelect: boolean) {
    if (this.model === 'urgent') {
      this.selectAllInstances = lodash.map(lodash.filter(this.data, (d: any) => d.isBatchApprove), (m: any) => m.id);
    } else if (this.model === 'class') {
      this.selectAllInstances = [];
      lodash.forEach(this.dataGroup, (d: any) => {
        this.selectAllInstances.push(...lodash.map(lodash.filter(d.items, (dd: any) => dd.isBatchApprove), (m: any) => m.id));
      });
    } else if (this.model === 'source') {
      this.selectAllInstances = [];
      lodash.forEach(this.dataGroup, (d: any) => {
        this.selectAllInstances.push(...lodash.map(lodash.filter(d.items, (dd: any) => dd.isBatchApprove), (m: any) => m.id));
      });
    }
  }
  onUnSelectAll(isSelect: boolean) {
    this.selectAllInstances = [];
  }
  onInstanceChecked(checked: boolean, id: any) {
    if (checked && !lodash.includes(this.selectAllInstances, id)) {
      this.selectAllInstances.push(id);
    } else if (!checked && lodash.includes(this.selectAllInstances, id)) {
      const index = lodash.findIndex(this.selectAllInstances, (d: any) => d === id);
      this.selectAllInstances.splice(index, 1);
    }
  }
  onAgree() {
    if (this.selectAllInstances.length === 0) {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: `${this.$l.getLocale('fields.please')}${this.$l.getLocale('fields.select')}${this.$l.getLocale('todoCenterTabs.todo')}${this.$l.getLocale('fields.process')}`,
      });
      return;
    }
    this.$dialog.confirm({
      message: this.$l.getLocale('tips.agreeConfirm')
    }).then(() => {
      todoCenterService.batchDone({ items: this.selectAllInstances }).subscribe(() => {
        this.$toast({
          type: 'success',
          forbidClick: true,
          message: this.$l.getLocale('tips.batchAgreeSuccess'),
          onClose: () => {
            this.searchStr = '';
            this.onSearch(this.searchStr, true);
            this.loadMenuNumber();
          }
        });
      });
    }).catch(() => { });
  }
  onSetTop(subSystem: any) {
    if (subSystem.isTop) {
      todoCenterService.cancelTop({ subSystemCode: subSystem.code, subSystemName: subSystem.name }).subscribe(data => {
        this.$toast({
          type: 'success',
          forbidClick: true,
          message: this.$l.getLocale('tips.cancelTopSuccess'),
          onClose: () => { this.onSearch(this.searchStr); this.loadMenuNumber(); }
        });
      });
    } else {
      todoCenterService.setTop({ subSystemCode: subSystem.code, subSystemName: subSystem.name }).subscribe(data => {
        this.$toast({
          type: 'success',
          forbidClick: true,
          message: this.$l.getLocale('tips.setTopSuccess'),
          onClose: () => { this.onSearch(this.searchStr); this.loadMenuNumber(); }
        });
      });
    }
  }
  onInstanceClick(insatnce: any) {
    if (insatnce && insatnce.tripartiteViewMobileUrl) {
      window.location.href = insatnce.tripartiteViewMobileUrl;
    } else {
      this.$toast({
        type: 'fail',
        forbidClick: true,
        message: '流程无法在移动端打开，请至电脑端操作'
      });
    }
  }
  getTabContent() {
    return [<div class={styles.tool_bar + ' ' + styles.tool_bar_batch}>
      <tool-bar
        defaultModel={this.model}
        tabActive={this.tabActive}
        defaultBatch={this.isBatch}
        checkInstanceCount={this.selectAllInstances.length}
        on-model-change={(model: any) => this.onModelChange(model)}
        on-search={(search: any) => this.onSearch(search)}
        on-batch-approval={(isBatch: boolean) => this.onBatchApproval(isBatch)}
        on-select-all={(isSelect: boolean) => this.onSelectAll(isSelect)}
        on-un-select-all={(isSelect: boolean) => this.onUnSelectAll(isSelect)}
      ></tool-bar>
    </div>,
    <div class={styles.instance_content}>
      <van-pull-refresh
        v-model={this.refreshing}
        on-refresh={this.onRefresh}
      >
        {
          this.model === 'urgent' ? <van-list
            v-model={this.loading}
            finished={this.finished}
            finished-text={this.$l.getLocale('fields.noMore')}
            offset={0}
            immediate-check={false}
            on-load={this.onListLoading}
          >
            {
              lodash.map(this.data, (d: any) => {
                return <instance
                  tabActive={this.tabActive}
                  instance={d}
                  isBatch={this.isBatch}
                  defaultChecked={lodash.includes(this.selectAllInstances, d.id)}
                  on-change={(checked: boolean, id: any) => this.onInstanceChecked(checked, id)}
                  on-click={(instance: any) => this.onInstanceClick(instance)}
                />;
              })
            }
          </van-list> : ''
        }
        {
          this.model === 'class' ?
            <van-list
              v-model={this.loading}
              finished={this.finished}
              finished-text=''
              offset={0}
              immediate-check={false}
            >
              <classification-mode
                tabActive={this.tabActive}
                isBatch={this.isBatch}
                selectAllInstances={this.selectAllInstances}
                dataGroup={this.dataGroup}
                on-change={(checked: boolean, id: any) => this.onInstanceChecked(checked, id)}
                on-click={(instance: any) => this.onInstanceClick(instance)}
              /></van-list> : ''
        }
        {
          this.model === 'source' ?
            <van-list
              v-model={this.loading}
              finished={this.finished}
              finished-text=''
              offset={0}
              immediate-check={false}
            >
              <source-mode
                tabActive={this.tabActive}
                isBatch={this.isBatch}
                selectAllInstances={this.selectAllInstances}
                dataGroup={this.dataGroup}
                on-change={(checked: boolean, id: any) => this.onInstanceChecked(checked, id)}
                on-set-top={(instance: any) => this.onSetTop(instance)}
                on-click={(instance: any) => this.onInstanceClick(instance)}
              /></van-list> : ''
        }
      </van-pull-refresh>
    </div>];
  }
  created() {
    this.loading = true;
    this.tabActive = this.$route.query['tabActive'] as string || 'dbl';
    if (this.tabActive === 'dbl') {
      this.model = window.localStorage.getItem(authService.user.id || '') || 'urgent';
    } else {
      this.model = 'urgent';
    }
    this.onLoadData();
    this.loadMenuNumber();
  }
}
