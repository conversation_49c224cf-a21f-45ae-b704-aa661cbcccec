import { Component } from 'vue-property-decorator';
import styles from './source-mode.module.less';
import lodash from 'lodash';
import { IndexMixins } from './IndexMixins';
import { mixins } from 'vue-class-component';

@Component
export class SourceMode extends mixins(IndexMixins) {
    render() {
        return (<div>
            {
                lodash.map(this.dataGroup, (d: any) => {
                    return <div class={styles.instance_group_content}>
                        <div class={styles.title}
                            on-click={() => d.isOpen = !d.isOpen}>
                            <i class='iconfont icon-app_icon_fenlei'
                                style='color:#009a3e;'></i>
                            <span class={styles.name}>{d.name}</span>
                            <div class={styles.round}></div>
                            <span class={styles.count}>{d.count}</span>
                            <div class={styles.opts}
                            >
                                {
                                    !this.isBatch ? <img
                                        title={d.isTop ? this.$l.getLocale('fields.cancelTop') : this.$l.getLocale('fields.top')}
                                        on-click={(e: any) => { this.setTop(d); e.stopPropagation(); }}
                                        src={d.isTop ? require('../../../../assets/images/cancelTop.png') : require('../../../../assets/images/setTop.png')} /> : ''
                                }
                            </div>
                            <van-icon name={d.isOpen ? 'arrow-up' : 'arrow-down'}
                                color='#999999' />
                        </div>
                        {/* {
                            d.isLoading ?
                                <van-loading size='16'
                                    class={styles.lodaing}>加载中...</van-loading> : ''
                        } */}
                        {
                            d.isOpen ? <div class={styles.content}>
                                {
                                    lodash.map(d.items, (dd: any) => {
                                        return <instance
                                            tabActive={this.tabActive}
                                            instance={dd}
                                            isBatch={this.isBatch}
                                            defaultChecked={lodash.includes(this.selectAllInstances, dd.id)}
                                            on-change={(checked: boolean, id: any) => this.change(checked, id)}
                                            on-click={(instance: any) => this.click(instance)}
                                        />;
                                    })
                                }
                            </div> : ''
                        }
                    </div>;
                })
            }
        </div>
        );
    }
}
