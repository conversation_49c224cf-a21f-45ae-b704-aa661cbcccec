.instance_group_content {
    min-height: 45px;
    width: 100%;
    display: block;
    border-radius: 4px;
    overflow: hidden;

    .title {
        background: #ffffff;
        min-height: 45px;
        display: flex;
        align-items: center;
        padding: 0 12px;
        cursor: pointer;

        .name {
            font-size: 16px;
            font-weight: bold;
            margin-left: 10px;
            color: #333333;
            min-height: 23px;
            line-height: 23px;
        }

        .round {
            display: inline-block;
            height: 4px;
            width: 4px;
            border-radius: 50%;
            background: #333333;
            margin-left: 5px;
            margin-right: 5px;
            flex: 0 0 4px;
        }

        .count {
            font-size: 16px;
            font-weight: bold;
            color: #333333;
            height: 23px;
            line-height: 23px;
        }

        .opts {
            img {
                width: 20px;
                height: 20px;
            }
            margin-right: 5px;
            flex: 1 0 30px;
            text-align: right;
        }
    }

    .lodaing {
        height: 60px;
        width: 100%;
        text-align: center;
        line-height: 60px;
    }

    .content {
        background: #f8f8f8;
    }

    .content>div {
        background: none !important;
    }

    .content>div:not(:last-child) {
        border-bottom: 1px solid #e8e8e8;
    }
}

.instance_group_content:not(:first-child) {
    margin-top: 10px;
}