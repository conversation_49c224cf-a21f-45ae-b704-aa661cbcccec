import lodash from 'lodash';
import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator';

@Component
export class IndexMixins extends Vue {
    @Prop() defaultModel!: string;
    @Prop() checkInstanceCount!: number;
    @Prop() tabActive!: string;
    @Prop() defaultBatch!: boolean;
    modelOptions = [
        {
            label: this.$l.getLocale('fields.todoMode.urgent'),
            value: 'urgent'
        },
        {
            label: this.$l.getLocale('fields.todoMode.source'),
            value: 'source'
        },
        {
            label: this.$l.getLocale('fields.todoMode.class'),
            value: 'class'
        }
    ];
    model = 'urgent';
    modelVisible = false;
    searchStr = '';
    isBatch = false;
    @Watch('defaultBatch')
    defaultBatchChange(newVal: any) {
        this.isBatch = newVal ? true : false;
        this.modelVisible = false;
    }
    @Watch('tabActive')
    tabActiveChange(newVal: any) {
        this.searchStr = '';
        this.isBatch = false;
        this.modelVisible = false;
    }
    @Emit('model-change')
    modelChange(model: any) {
        this.modelVisible = false;
    }
    @Emit('search')
    search(search: any) {
    }
    @Emit('batch-approval')
    batchApproval(isBatch: boolean) {
    }
    @Emit('select-all')
    selectAll(isSelect: boolean) {
    }
    @Emit('un-select-all')
    unSelectAll() {
    }
    get modelLable() {
        const option = lodash.find(this.modelOptions, (m: any) => m.value === this.defaultModel);
        return option ? option.label : '';
    }
    onModelClick() {
        this.modelVisible = true;
    }
    onModelCancel() {
        this.modelVisible = false;
    }
    get defaultIndex() {
        const optionIndex = lodash.findIndex(this.modelOptions, (m: any) => m.value === this.defaultModel);
        return optionIndex > -1 ? optionIndex : 0;
    }
    created() {
    }
}
