import { mixins } from 'vue-class-component';
import { Component } from 'vue-property-decorator';
import { IndexMixins } from './IndexMixins';
import styles from './tool-bar.module.less';

@Component
export class ToolBar extends mixins(IndexMixins) {
    render() {
        return (
            <div class={styles.tool_bar}>
                {
                    this.isBatch ?
                        <div class={styles.batch}>
                            <span class={styles.checked}>
                                {`${this.$l.getLocale('fields.selected2')}${this.checkInstanceCount}${this.$l.getLocale('fields.items')}`}
                            </span>
                            <div>
                                <div class={styles.button + (this.checkInstanceCount === 0 ? ' ' + styles.no_cancel : '')}
                                    on-click={() => this.unSelectAll()}>{this.$l.getLocale('buttons.unselectAll')}</div>
                                <div class={styles.button}
                                    on-click={() => this.selectAll(true)}>{this.$l.getLocale('buttons.selectAll')}</div>
                                <div class={styles.button}
                                    on-click={() => this.batchApproval(false)}>{this.$l.getLocale('buttons.cancel')}</div>
                            </div>
                        </div>
                        :
                        <div class={styles.bz}>
                            {this.tabActive === 'dbl' ? <div class={styles.model}
                                on-click={() => this.onModelClick()}>
                                <span>
                                    {
                                        this.modelLable
                                    }
                                </span>
                                <i class='iconfont icon-icon_arrow_down'
                                    style='color:#333333;transform: scale(0.3);display: inline-block;'></i>
                            </div> : ''}
                            <form action='/'
                                class={styles.form}>
                                <van-search
                                    v-model={this.searchStr}
                                    placeholder={this.tabActive === 'brfq' || this.tabActive === 'cg' ?
                                        this.$l.getLocale('placeholders.todoCenterSearch2') :
                                        this.$l.getLocale('placeholders.todoCenterSearch')}
                                    background='#f0f2f5'
                                    shape='round'
                                    clearable
                                    on-search={() => this.search(this.searchStr)}
                                    on-clear={() => this.search(this.searchStr)}
                                >
                                    <template slot='left-icon'>
                                        <i class='iconfont icon-icon_search'
                                            style='color:#ababab;font-size:12px'></i>
                                    </template>
                                </van-search></form>
                            {/* {this.tabActive === 'dbl' ?
                                <div class={styles.batchApproval}
                                    on-click={() => this.batchApproval(true)}>
                                    <i class='iconfont icon-btn_piliangshenpi'
                                        style='color:#607384'></i>
                                </div> : ''} */}
                        </div>
                }
                <van-popup v-model={this.modelVisible}
                    position='bottom'
                    round>
                    <van-picker
                        title={this.$l.getLocale('fields.processMode')}
                        show-toolbar
                        value-key='label'
                        default-index={this.defaultIndex}
                        columns={this.modelOptions}
                        on-confirm={(value: any, index: number) => this.modelChange(value.value)}
                        on-cancel={this.onModelCancel} />
                </van-popup>
            </div>
        );
    }
}
