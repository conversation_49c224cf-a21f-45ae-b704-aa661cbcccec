.tool_bar {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    border-radius: 8px 8px 0px 0px;

    .batch {
        display: flex;
        align-items: center;
        width: 100%;

        span {
            flex: 1;
            font-size: 15;
            // font-weight: bold;
            color: #333333;
        }

        .button {
            display: inline-block;
            min-width: 40px;
            text-align: right;
            color: #009a3e;
            // font-weight: bold;
            cursor: pointer;
            margin-left: 10px;
        }

        .no_cancel {
            color: #999999;
        }
    }

    .bz {
        display: flex;
        align-items: center;
        width: 100%;

        .model {
            height: 32px;
            min-width: 88px;
            background-color: #ffffff;
            border-radius: 16px;
            text-align: center;
            margin-right: 5px;
            line-height: 32px;

            span {
                font-size: 12px;
                height: 12px;
                width: 48px;
                color: #333333;
                font-weight: bold;
            }

            i {
                width: 10px;
            }
        }

        .form {
            // min-width: 250px;
            flex: 1;

            :global(.van-search__content) {
                background: #ffffff;

                :global(.van-icon-search) {
                    font-size: 12px;
                }

                :global(.van-field__control) {
                    font-size: 12px;
                    color: #999999;
                    font-weight: 400px;
                }
            }
        }

        .batchApproval {
            background: #ffffff;
            border-radius: 16px 0px 0px 16px;
            height: 32px;
            margin-left: 5px;
            width: 50px;
            text-align: center;
            line-height: 32px;
        }
    }
}