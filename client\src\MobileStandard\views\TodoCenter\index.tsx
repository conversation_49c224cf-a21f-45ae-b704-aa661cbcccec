import { mixins } from 'vue-class-component';
import { IndexMixins } from './IndexMixins';
import styles from './todo-center.module.less';
import { Component } from 'vue-property-decorator';
import { getComponentFromProp } from '@/MobileStandard/common/utils';

@Component
export class TodoCenter extends mixins(IndexMixins) {
  render() {
    const moreTabDom = getComponentFromProp(this, 'moreTab');
    return (
      <main-menu tabbarActive='todo-center'
        isBatch={this.isBatch}
        on-agree={() => this.onAgree()}
      >
        <template slot='content'>
        {/* <span on-click={() => {
            this.$router.push({ path: '/test' });
          }}>测试0</span>||||||
          <span on-click={() => {
            this.$router.push({ path: '/fullScreen' });
          }}>测试1</span>||||||
          <span on-click={() => {
            this.$router.push({ path: '/test2' });
          }}>测试2</span> */}
          <van-tabs v-model={this.tabActive}
            animated
            swipeable
            background='#009a3e'
            color='#ffffff'
            line-height='3'
            line-width={this.tabActive.length * 15}
            title-active-color='#ffffff'
            title-inactive-color='#ffffff'
            swipe-threshold='4'
            class={styles.tabs + (moreTabDom ? ' ' + styles.tabs_move : '')}
            on-change={this.onTabsChange}>
            <van-tab title={this.$l.getLocale('todoCenterTabs.todo')} name='dbl' badge={this.badge.task}>
              {
                this.getTabContent()
              }
            </van-tab>
            <van-tab title={this.$l.getLocale('todoCenterTabs.myStart')} name='brfq'>
              {
                this.getTabContent()
              }
            </van-tab>
            <van-tab title={this.$l.getLocale('todoCenterTabs.done')} name='ybl'>
              {
                this.getTabContent()
              }
            </van-tab>
            <van-tab title={this.$l.getLocale('todoCenterTabs.ccToMe')} name='csw' badge={this.badge.carboncopy}>
              {
                this.getTabContent()
              }
            </van-tab>
            <van-tab title={this.$l.getLocale('todoCenterTabs.draft')} name='cg' badge={this.badge.draft}>
              {
                this.getTabContent()
              }
            </van-tab>
            {
              moreTabDom
            }
          </van-tabs>
          {
            moreTabDom ? [
              // <div class={styles.sjx_l} />,
              <div class={styles.sjx_r} />
            ]
              : ''
          }
        </template>
      </main-menu>
    );
  }
}
