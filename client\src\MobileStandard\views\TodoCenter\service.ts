import { Observable, zip } from 'rxjs';
import { httpHelper, toCasedStyleObject } from '@/MobileStandard/common/utils';
import { map } from 'rxjs/operators';
import { dateHelper, diffHours, diffSeconds } from '@/MobileStandard/common/utils/date-helper';
import { ApproveState } from '@/MobileStandard/services/common/common.types';
import lodash from 'lodash';

class TodoCenterService {
  completedStatuses = [ApproveState.approved, ApproveState.refused, ApproveState.canceled];
  getAllTotal(params: any): Observable<any> {
    const url = '/api/todo-centre/v1/global/allTotal';
    return httpHelper.get(url, { params: params });
  }
  getTodoTasks(params: any): Observable<any> {
    const url = '/api/todo-centre/v1/task';
    return httpHelper.get(url, { params: params });
    // const holidayOb = httpHelper.get('/api/process/v1/holiday',
    //   { params: toCasedStyleObject({ 'is-all': true, 'max-date': dateHelper.currentTime }) });
    // const todoTaskOb = httpHelper.get('/api/process/v1/my-workspace/todo-tasks', { params: params });
    // return zip(holidayOb, todoTaskOb).pipe(
    //   map(data => {
    //     const [holidays, todoTasks] = data;
    //     (todoTasks.items || []).forEach((item: any) => {
    //       // 过滤日期之间的假日
    //       if (holidays.items && holidays.items.length > 0) {
    //         const date = dateHelper.calculationEnddate(item.latestArriveTime as string, dateHelper.currentTime, holidays.items);
    //         item.retentionTime = diffSeconds(date.startDate, date.endDate);
    //       } else {
    //         item.retentionTime = diffSeconds(item.latestArriveTime as string);
    //       }
    //       item.id = `${item.instanceNumber}|${item.todoCenterId}`;
    //     });
    //     return todoTasks;
    //   }));

  }
  getMyccTasks(params: any): Observable<any> {
    // const url = '/api/process/v1/my-workspace/cc-tasks';
    // return httpHelper.get(url, { params }).pipe(
    //   map((data: any) => {
    //     (data.items || []).forEach((item: any) => {
    //       if (this.completedStatuses.includes(item.status as ApproveState)) {
    //         item.latestActivityName = '';
    //       }
    //       item.id = `${item.instanceNumber}|${item.todoCenterId}`;
    //     });
    //     return data;
    //   })
    // );
    const url = '/api/todo-centre/v1/carboncopy_Latest';
    return httpHelper.get(url, { params: params });
  }
  getMydoneTasks(params: any): Observable<any> {
    // const url = '/api/process/v1/my-workspace/done-tasks';
    // return httpHelper.get(url, { params }).pipe(
    //   map((data: any) => {
    //     (data.items || []).forEach((item: any) => {
    //       item.retentionTime = diffHours(item.approveTime as string, item.latestArriveTime as string);
    //       item.isRead = true;
    //       item.id = `${item.instanceNumber}|${item.todoCenterId}`;
    //     });
    //     return data;
    //   })
    // );
    const url = '/api/todo-centre/v1/archivedTask_Latest';
    return httpHelper.get(url, { params: params });
  }
  getMystartTasks(params: any): Observable<any> {
    // const url = '/api/process/v1/my-workspace/processes';
    // return httpHelper.get(url, { params }).pipe(
    //   map((data: any) => {
    //     (data.items || []).forEach((item: any) => {
    //       if (item.status === ApproveState.processing) {
    //         item.retentionTime = diffHours(item.latestArriveTime as string);
    //       }
    //       if (this.completedStatuses.includes(item.status as ApproveState)) {
    //         item.latestActivityName = '';
    //       }
    //       item.isRead = true;
    //       item.id = item.instanceNumber;
    //     });
    //     return data;
    //   })
    // );
    const url = '/api/todo-centre/v1/myStart_Latest';
    return httpHelper.get(url, { params: params });
  }
  getDraft(params: any): Observable<any> {
    const _url = '/api/todo-centre/v1/draft';
    return httpHelper.get(_url, { params: params });
  }
  getUseForGroupName(params: any): Observable<any[]> {
    const _url = '/api/todo-centre/v1/task/useForGroupName';
    return httpHelper.get(_url, { params: params });
  }
  getSubSystem(params: any): Observable<any[]> {
    const _url = '/api/todo-centre/v1/task/subSystem';
    return httpHelper.get(_url, { params: params });
  }
  batchDone(params: any): Observable<void> {
    const url = '/api/todo-centre/v1/task/batchApproval';
    return httpHelper.put(url, params, undefined, { loading: true });
  }
  setTop(params: any): Observable<void> {
    const url = '/api/todo-centre/v1/mySubSystem/setTop';
    return httpHelper.put(url, params, undefined, { loading: true });
  }
  cancelTop(params: any): Observable<void> {
    const url = '/api/todo-centre/v1/mySubSystem/cancelTop';
    return httpHelper.put(url, params, undefined, { loading: true });
  }
  getUserAvatar(params: any): Observable<any> {
    const _url = '/api/platform/v1/user-avatar';
    return httpHelper.post(_url, params);
  }
}

export const todoCenterService = new TodoCenterService();
