.tabs {
    height: 100%;

    :global(.van-tab__text) {
        font-size: 15px;
    }

    :global(.van-tab--active) {
        font-weight: bold;
    }

    :global(.van-tabs__content) {
        height: calc(~'100% - 44px');
        background-color: #f0f2f5;
    }

    :global(.van-tab__pane) {
        height: 100%;
    }

    :global(.van-info) {
        transform: translate(90%, -50%)
    }

    .tool_bar {
        padding-left: 12px;
    }

    .tool_bar_batch {
        padding-right: 12px;
    }

    .instance_content {
        padding: 0 12px 10px 12px;
        height: calc(~'100% - 60px');
        overflow: auto;
    }
}

.tabs_move {
    :global(.van-tabs__nav) {
        padding-left: 0px !important;
        padding-right: 0px !important;
    }

    :global(.van-tabs__wrap) {
        padding-left: 12px;
        padding-right: 12px;
        background-color: #0e9266;
    }
}

.sjx_l {
    position: absolute !important;
    z-index: 1;
    top: 14px;
    left: -7px;
    width: 0;
    height: 0;
    border-width: 8px;
    border-style: solid;
    border-color: transparent #ffffff transparent transparent;
    transform: rotateX(50deg);
}

.sjx_r {
    position: absolute !important;
    z-index: 1;
    top: 14px;
    right: -7px;
    width: 0;
    height: 0;
    border-width: 8px;
    border-style: solid;
    border-color: transparent transparent transparent #ffffff;
    transform: rotateX(50deg);
}