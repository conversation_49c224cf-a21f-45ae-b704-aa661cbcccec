import Vue from 'vue';
import VueI18n, { LocaleMessages } from 'vue-i18n';
import { Settings } from './MobileStandard/common/defines';
import { httpHelper } from './MobileStandard/common/utils';

Vue.use(VueI18n);

/* function loadLocaleMessages(): LocaleMessages {
  const locales = require.context('./locales', true, /[A-Za-z0-9-_,\s]+\.json$/i);
  const messages: LocaleMessages = {};
  locales.keys().forEach(key => {
    const matched = key.match(/([A-Za-z0-9-_]+)\./i);
    if (matched && matched.length > 1) {
      const locale = matched[1];
      messages[locale] = locales(key);
    }
  });
  return messages;
}

export default new VueI18n({
  locale: process.env.VUE_APP_I18N_LOCALE || 'zh',
  fallbackLocale: process.env.VUE_APP_I18N_FALLBACK_LOCALE || 'zh',
  messages: loadLocaleMessages()
}); */

const i18n = new VueI18n({
  locale: process.env.VUE_APP_I18N_LOCALE || 'zh',
  fallbackLocale: process.env.VUE_APP_I18N_FALLBACK_LOCALE || 'zh',
  messages: {},
  silentTranslationWarn: true,
});

// 异步加载语言包
function loadLanguageAsync() {
  const i18LanguagePackages = localStorage.getItem(Settings.I18nLanguageCacheKey);
  if (i18LanguagePackages) {
    const msgs = JSON.parse(i18LanguagePackages);
    Object.keys(msgs).forEach((key: string) => {
      i18n.setLocaleMessage(key, msgs[key]);
    });
  } else {
    httpHelper.get('/auth/i18Languages', undefined, { loading: false }).subscribe(msgs => {
      localStorage.setItem(Settings.I18nLanguageCacheKey, JSON.stringify(msgs));
      Object.keys(msgs).forEach((key: string) => {
        i18n.setLocaleMessage(key, msgs[key]);
      });
    });
  }
}

Vue.prototype.$loadLanguageAsync = loadLanguageAsync;
export default i18n;
