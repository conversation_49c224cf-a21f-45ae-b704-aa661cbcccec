import Vue from 'vue';
import router from './router';
import App from './App';
import i18n from './i18n';
import '@babel/polyfill';
import '@/plugins/ant-design-vue';
import Vant from 'vant';
import 'vant/lib/index.css';
import './assets/iconfont/iconfont.css';
import '@vant/touch-emulator';
import './styles.less';
import { i18nService } from './MobileStandard/services/i18n';
import { httpHelper, i18nHelper } from './MobileStandard/common/utils';
import { Settings, vueContext } from './MobileStandard/common/defines';

Vue.prototype.$l = i18nHelper;

refreshLanguage();
Vue.config.productionTip = false;
i18nService.init(i18n);
Vue.use(Vant);

/* 路由发生变化修改页面title */
router.afterEach((to: any, from) => {
  if (to.meta.title) {
    document.title = vueContext.$t(`router.${to.meta.title}`).toString();
  }
});

new Vue({
  router,
  i18n,
  render: h => h(App)
}).$mount('#app').$loadLanguageAsync();

// 刷新多语言缓存
function refreshLanguage() {
  httpHelper.get('/auth/i18Languages', undefined, { loading: false }).subscribe(msgs => {
    localStorage.setItem(Settings.I18nLanguageCacheKey, JSON.stringify(msgs));
    Object.keys(msgs).forEach((key: string) => {
      i18n.setLocaleMessage(key, msgs[key]);
    });
  });
}
