import {
    ApprovalComment,
    ApprovalRecord,
    BaseInfo, FormInfo, Instance, InstanceCollapseItem, PageBottom,
    ProcessDeduction, ProcessDeductionParallel, RelationInstance,
    RelevantAttachment, RelevantProcess, ApprovalRecordParallel,
    Document, WebOffice
} from '@/MobileStandard/components';
import Vue from 'vue';
Vue.component('base-info', BaseInfo);
Vue.component('form-info', FormInfo);
Vue.component('relevant-process', RelevantProcess);
Vue.component('relevant-attachment', RelevantAttachment);
Vue.component('process-deduction', ProcessDeduction);
Vue.component('process-deduction-parallel', ProcessDeductionParallel);
Vue.component('approval-comment', ApprovalComment);
Vue.component('page-bottom', PageBottom);
Vue.component('approval-record', ApprovalRecord);
Vue.component('approval-record-parallel', ApprovalRecordParallel);
Vue.component('instance-collapse-item', InstanceCollapseItem);
Vue.component('instance', Instance);
Vue.component('relation-instance', RelationInstance);
Vue.component('document', Document);
Vue.component('web-office', WebOffice);
