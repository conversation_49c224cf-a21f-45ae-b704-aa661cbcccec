import { mobileStandardRoutes } from '@/MobileStandard/router/index';
import Vue from 'vue';
import Router from 'vue-router';

Vue.use(Router);

// 获取原型对象上的push函数
const originalPush = Router.prototype.push;
// 修改原型对象中的push方法
Router.prototype.push = function push(location: any) {
  return originalPush.call(this, location, undefined, (err: any) =>  err);
};

export default new Router({
  mode: 'history',
  base: process.env.BASE_URL,
  routes: [ ...mobileStandardRoutes],
});
