#app {
  background-color: #FFFFFF;
  height: 100vh;
  font-size: 15px;

  ::-webkit-scrollbar {
    width: 0 !important;
  }

  ::-webkit-scrollbar {
    width: 0 !important;
    height: 0;
  }
}

.structure_up_down {
  display: block;
}

.van-search {
  padding: 0;
}

.van-info {
  background-color: #be4848;
}

.van-checkbox__icon--checked .van-icon {
  background-color: #009a3e !important;
  border-color: #009a3e !important;
}

.van-radio__icon--checked .van-icon {
  background-color: #009a3e !important;
  border-color: #009a3e !important;
}

#custom-form {
  a {
    color: #009a3e;
  }

  .wangeditor_tsd {
    :global(.van-field__value) {
      overflow: auto !important;
    }
  }

  .custom_table_content {
    position: relative;

    .custom_table {
      overflow: auto;

      table {
        margin: 5px 0;
        border-top: 1px solid #e8e8e8;
        border-left: 1px solid #e8e8e8;
        border-spacing: 0;

        th {
          border-bottom: 1px solid #e8e8e8;
          border-right: 1px solid #e8e8e8;
          font-size: 12px;
          background-color: #F6F7F9;
          padding: 5px;
          height: 25px;
        }

        td {
          border-bottom: 1px solid #e8e8e8;
          border-right: 1px solid #e8e8e8;
          font-size: 12px;
          padding: 5px;
          height: 25px;
        }

        .custom_table_serial_td {
          background-color: #F6F7F9;
        }
      }
    }

    .custom_table_hengping {
      opacity: 0.6;
      position: absolute;
      top: 5px;
      right: 0px;
      background: #333333;
      border-radius: 0px 0px 0px 100px;
    }
  }

  .custom_table_button {
    display: block;
    text-align: center;
    color: #009a3e;
    padding-bottom: 5px;

    span {
      margin-left: 5px;
      font-weight: bold;
    }
  }

  .custom_title {
    text-align: center;
    height: 44px;
    line-height: 44px;

    .custom_title_close {
      position: absolute;
      left: 20px;
      top: 10px;
    }

    .custom_title_buttons {
      position: absolute;
      right: 0;
      line-height: 30px;

      .custom_title_button {
        margin-right: 5px;
      }

      .van-button--info {
        background-color: #009a3e;
        border: 1px solid #009a3e;
      }
    }
  }

  .custom_title_tip {
    line-height: 40px;
    padding-left: 15px;
    background-color: #f8f8f9;
  }

  .red-preview {
    color: #ff0000;

    input {
      color: #ff0000;
    }
  }

  .blue-preview {
    color: #0000ff;

    input {
      color: #0000ff;
    }
  }

  .black-preview {
    color: #000000;

    input {
      color: #000000;
    }
  }

  .yellow-preview {
    color: #ffff00;

    input {
      color: #ffff00;
    }
  }

  .grey-preview {
    color: #e8e8e8;

    input {
      color: #f7b9b9;
    }
  }

  .van-calendar__popup {

    .van-calendar__selected-day,
    .van-calendar__day--start,
    .van-calendar__day--end {
      background-color: #009a3e;
    }

    .van-button--danger {
      background-color: #009a3e;
      border: 1px solid #009a3e;
    }
  }

  .van-tab__pane {
    padding-bottom: 0px;
  }
}

.table_wrap {
  width: 100%;
  z-index: 10000;
  background: #fff !important;

  table {
    table-layout: fixed;
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    border: 0;
  }

  td,
  th {
    width: 150px;
    box-sizing: border-box;
    // border-right:1px solid red;
    // border-bottom:1px solid red;
    /*超出长度...*/
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  thead tr th {
    position: sticky;
    top: 0;
    // background:#61dafb;
    // background:pink;
    border-bottom: 1px solid #e8e8e8;
    border-right: 1px solid #e8e8e8;
    font-size: 12px;
    background-color: #F6F7F9;
    padding: 5px;
    height: 25px;
  }

  th:first-child,
  td:first-child {
    position: sticky;
    left: 0;
    // background:#61dafb;
    border-bottom: 1px solid #e8e8e8;
    border-right: 1px solid #e8e8e8;
    font-size: 12px;
    background-color: #F6F7F9;
    padding: 5px;
    height: 25px;
  }

  th:first-child {
    z-index: 1;
    /*左上角单元格z-index，切记要设置，不然表格纵向横向滚动时会被该单元格右方或者下方的单元格遮挡*/
    // background:pink;
  }

  tbody tr td {
    border-bottom: 1px solid #e8e8e8;
    border-right: 1px solid #e8e8e8;
    font-size: 12px;
    padding: 5px;
    height: 25px;
  }
}