import Vue from 'vue';

declare module 'vue/types/vue' {
  interface I18nHelper {
    getLocale: (key: string | string[]) => string;
  }

  interface Vue {
    $l: I18nHelper;
    $loadLanguageAsync: () => void;

    // 实例相关挂载
    isIndependent: boolean;
    pageType: string;
    number: string;
    processId: string;
    draftId: string;
    taskId: string;
    bsid: string;
    btid: string;
    boid: string;
    authId: string;
    listType: string;
    backUrl: string;
    canRecall: string;
    simulate: boolean;
  }
}
