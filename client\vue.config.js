module.exports = {
  outputDir: '../public/mobile',
  publicPath: 'mobile',
  runtimeCompiler: true,

  devServer: {
    disableHostCheck: true,
    proxy: {
      '/mobile/api/': {
        target: 'http://localhost:4300',
        changeOrigin: true
      },
      '/mobile/auth/': {
        target: 'http://localhost:4300',
        changeOrigin: true
      },
      '/mobile/version/': {
        target: 'http://localhost:4300',
        changeOrigin: true
      },
      '/mobile/static-form/': {
        target: 'http://localhost:4300',
        changeOrigin: true
      },
      '/mobile/socket.io/': {
        target: 'http://localhost:4300',
        changeOrigin: true,
        ws: true
      }
    }
  },

  pluginOptions: {
    i18n: {
      locale: 'zh',
      fallbackLocale: 'zh',
      localeDir: 'locales',
      enableInSFC: false
    }
  }
};
