{"urlKey": "mobile", "nacos": {"serverAddr": "*************:8848", "namespace": "shui<PERSON>", "languagegroupid": "i18n", "languageids": ["en.mobile.json", "zh.mobile.json"]}, "port": 4100, "uploadLimit": "100mb", "apiGateway": {"uri": "http://*************:32000", "appKey": "799e6e124ad95e09b055ae8c8cc53d7f", "appSecret": "a1116bf74e17adeb6b11615377b627e874484243129c5941c19058fd3c54bcf0"}, "baiduak": "QRqq3vxdZq4G1D6s9cncMIvRkKL0Uxfz", "localServices": [{"enable": false, "name": "form", "uri": "http://localhost:5053"}, {"enable": false, "name": "platform", "uri": "http://localhost:5051"}, {"enable": false, "name": "process", "uri": "http://localhost:5052"}], "adminUri": "", "sso": {"type": "", "movitech": {"pc": {"redirectUri": "http://***************:32014", "loginUri": "/saiwuaccount/mobile/login", "logoutUri": "/saiwuaccount/logout", "userStateUri": "/connect/userinfo", "callbackUri": "http://***************:32003/auth/sso-callback", "localNetwork": "http://***************:32014"}, "h5": {"redirectUri": "http://***************:32014", "loginUri": "/saiwuaccount/mobile/login", "logoutUri": "/saiwuaccount/logout", "userStateUri": "/connect/userinfo", "callbackUri": "http://***************:32003/auth/sso-callback", "localNetwork": "http://***************:32014"}, "wechat": {"redirectUri": "http://***************:32014", "loginUri": "/saiwuaccount/mobile/login", "logoutUri": "/saiwuaccount/logout", "userStateUri": "/connect/userinfo", "callbackUri": "http://***************:32003/auth/sso-callback", "localNetwork": "http://***************:32014"}}}, "impersonatePath": "/auth/impersonate", "saiwuSetting": {"home": "http://sweop.movitech.cn/h5/eop-flow-app/#/eoptodo"}, "noNeedAuthPaths": {"state": "/platform/v1/userExts", "captcha": "/platform/v1/captcha"}, "session": {"maxAge": 3600, "redis": {"host": "**************", "port": 6379, "db": 0, "password": "architecture"}}, "rabbitMQ": {"username": "admin", "password": "admin", "host": "**************", "exchange": "boards.refresh"}, "idocv": {"view": "http://************/view/url?url="}, "IsParalle": true, "formDesginConfig": {"baiduak": "QRqq3vxdZq4G1D6s9cncMIvRkKL0Uxfz"}}