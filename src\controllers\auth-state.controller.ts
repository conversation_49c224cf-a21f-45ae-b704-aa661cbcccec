import Koa from 'koa';
import bodyParser from 'koa-body';
import agent from 'superagent';
import { environment, i18Languages } from '../environment';
import { removeUserState } from '../utils/user-state';
import { sso } from '../sso/service';
import { getSsoConfig } from '../utils/sso-config';

export async function LoginAsync(ctx: Koa.ParameterizedContext, next: Koa.Next) { }

export async function LogoutAsync(ctx: Koa.ParameterizedContext, next: Koa.Next) {
  sso.deleteToken(ctx);
  removeUserState(ctx);
  ctx.status = 204;
}

export async function GetMobileUserStateAsync(ctx: Koa.ParameterizedContext, next: Koa.Next) {
  const uid = ctx.request.body.uid;
  const token = ctx.request.body.token;
  const timestamp = ctx.request.body.timestamp;
  const ssoConfig = getSsoConfig(ctx);
  const gtLoginUri = `${ssoConfig.redirectUri}${ssoConfig.loginUri}` + encodeURI(`?uid=${uid}&token=${token}&timestamp=${timestamp}`);
  await agent
    .get(gtLoginUri)
    .then((res: agent.Response) => {
      const { accessToken: accessToken, expiresIn: expiresIn } = res.body;
      ctx.session.cookie.maxAge = expiresIn * 1000;
      ctx.session.accessToken = accessToken;
      ctx.session.user = { language: 'zh' };
      ctx.headers['lang'] = 'zh';
      ctx.redirect('/');
    });
}

export async function GetUserStateAsync(ctx: Koa.ParameterizedContext, next: Koa.Next) {
  if (ctx.session && ctx.session.user) {
    const ssoConfig = getSsoConfig(ctx);
    const logoutUri = ssoConfig ? ssoConfig.logoutUri : '/logout';
    const adminUri = environment.adminUri;

    await agent
      .get(`${environment.apiGateway.uri}/platform/v1/mobile/menus`)
      .set(ctx.header)
      .then((res: agent.Response) => {
        ctx.status = 200;
        ctx.body = { ...ctx.session.user, menus: res.body, logoutUri, adminUri };
      })
      .catch((err: any) => {
        if (err.status === 404) {
          ctx.status = 200;
          ctx.body = { ...ctx.session.user, menus: [], logoutUri, adminUri };
        } else {
          const message = err.response ? err.response.text : '';
          ctx.throw(err.status, message);
        }
      });
  } else {
    ctx.throw(404, 'No Authentication!');
  }
}

export async function ModifyUserLanguage(ctx: Koa.ParameterizedContext, next: Koa.Next) {
  await bodyParser().call(null, ctx, next);

  if (ctx.session && ctx.session.user) {
    ctx.session.user.language = ctx.request.body.lang;
  }
  ctx.status = 204;
}

export async function Geti18Languages(ctx: Koa.ParameterizedContext, next: Koa.Next) {
  await i18Languages().then(res => {
    ctx.status = 200;
    ctx.body = res;
  });
}
