import Koa from 'koa';
import Router from 'koa-router';
import path from 'path';
import fs from 'fs';
import { environment } from '../environment';
import { apiProxy } from '../middlewares/api-proxy';
import { noNeedAuthProxy } from '../middlewares/no-need-auth-proxy';
import {
  GetUserStateAsync,
  ModifyUserLanguage,
  LogoutAsync,
  Geti18Languages,
  GetMobileUserStateAsync
} from '../controllers/auth-state.controller';
import { ImpersonateLoginAsync, ImpersonateLogoutAsync } from '../controllers/impersonate.controller';
import bodyParser from 'koa-body';

export function ApiRouter() {
  const router = new Router();

  /**
   * 验证是否登录，存在 session 即认为已登录
   * 如果是 SSO 登录的，则 session 会每次会由服务端用户信息赋值
   */
  router.get(['/auth/state', `/${environment.urlKey}/auth/state`], GetUserStateAsync);

  /**
   * 修改当前用户的语言
   */
  router.put(['/auth/state', `/${environment.urlKey}/auth/state`], ModifyUserLanguage);

  /**
   * 获取移动端用户信息
   */
  router.post(['/auth/mobile-state', `/${environment.urlKey}/auth/mobile-state`], bodyParser(),
    async (ctx: Koa.ParameterizedContext, next: Koa.Next) => GetMobileUserStateAsync(ctx, next));

  /**
   * 获取i8n语言配置文件
   */
  router.get(['/auth/i18Languages', `/${environment.urlKey}/auth/i18Languages`], Geti18Languages);

  /**
   * 获取服务器时间
   */
  router.get(['/auth/server-time', `/${environment.urlKey}/auth/server-time`], async (ctx: Koa.ParameterizedContext, next: Koa.Next) => {
    ctx.body = new Date();
  });

  /**
   * 登出
   */
  router.delete(['/auth/state', `/${environment.urlKey}/auth/state`], LogoutAsync);

  /**
   * 模拟登录
   */
  router.get(['/auth/impersonate', `/${environment.urlKey}/auth/impersonate`], ImpersonateLoginAsync);

  /**
   * 结束模拟
   */
  router.delete(['/auth/impersonate', `/${environment.urlKey}/auth/impersonate`], ImpersonateLogoutAsync);

  /**
   * SSO Return Url
   */
  router.post(['/auth/sso-callback', `/${environment.urlKey}/auth/sso-callback`],
    bodyParser(), async (ctx: Koa.ParameterizedContext, next: Koa.Next) => {
      console.log('↓↓↓↓↓↓↓↓↓↓↓↓↓↓ callback');
      const { access_token: accessToken, expires_in: expiresIn, lang } = ctx.request.body;
      ctx.session.cookie.maxAge = expiresIn * 1000;
      ctx.session.accessToken = accessToken;
      ctx.session.user = { language: lang };
      ctx.headers['lang'] = lang;
      const redirectUrl = ctx.originalUrl.indexOf('referer=') >= 0 ?
        ctx.originalUrl.substring(ctx.originalUrl.indexOf('referer=') + 8) : '/platform/';
      console.log('redirectUrl', redirectUrl);
      ctx.redirect(redirectUrl);
    });

  /**
   * Proxy
   * 对无需身份的 URL 验证
   */
  router.all(['/auth/*', `/${environment.urlKey}/auth/*`], noNeedAuthProxy(), async (ctx: Koa.ParameterizedContext, next: Koa.Next) => { });

  /**
   * 监听获取config
   */
  router.get(['/api/config', `/${environment.urlKey}/api/config`], async (ctx: Koa.ParameterizedContext, next: Koa.Next) => {
    ctx.body = environment;
  });

  /**
   * 监听获取formDesginConfig
   */
  router.get(['/api/formDesginConfig', `/${environment.urlKey}/api/formDesginConfig`],
    async (ctx: Koa.ParameterizedContext, next: Koa.Next) => {
      ctx.body = environment.formDesginConfig;
  });

  /**
   * Proxy
   * 监听获取idcview地址
   */
  router.get(['/api/getIdocView', `/${environment.urlKey}/api/getIdocView`], async (ctx: Koa.ParameterizedContext, next: Koa.Next) => {
    ctx.body = environment.idocv.view;
  });

  /**
   * Proxy
   * 加上一个空回调以阻止 proxy 中间件调用 next
   */
  router.all(['/api/*', `/${environment.urlKey}/api/*`], apiProxy(), async (ctx: Koa.ParameterizedContext, next: Koa.Next) => {
    if (ctx.session && ctx.session.user) {
      // 刷新登录状态
      ctx.session.cookie.tick = new Date().getTime();
    }
  });

  /**
   *  用于配合 低代码渲染子系统 二级目录路由
   */
  router.all(['/business-for-m/*', `/${environment.urlKey}/business-for-m/*`], async (ctx, next) => {
    ctx.type = 'html';
    if (environment.isDevelopment) {
      ctx.body = 'Hello World! If you see this page, your environment might unhealthy.';
    } else {
      ctx.body = fs.createReadStream(path.resolve(__dirname, '../../public/business-for-m/index.html'));
    }
  });

  /**
   *  用于配合 todo-center 二级目录路由
   */
  router.all(['/mobile/*', `/${environment.urlKey}/mobile/*`], async (ctx, next) => {
    ctx.type = 'html';
    if (environment.isDevelopment) {
      ctx.body = 'Hello World! If you see this page, your environment might unhealthy.';
    } else {
      ctx.body = fs.createReadStream(path.resolve(__dirname, '../../public/mobile/index.html'));
    }
  });

  /**
   * 其他所有请求均指向首页，用于配合 angular 路由
   */
  router.all('/*', async (ctx: Koa.ParameterizedContext, next: Koa.Next) => {
    ctx.type = 'html';
    if (environment.isDevelopment) {
      ctx.body = 'Hello World! If you see this page, your environment might unhealthy.';
    } else {
      ctx.body = fs.createReadStream(path.resolve(__dirname, '../../public/mobile/index.html'));
    }
  });

  return router.routes();
}
