import Koa from 'koa';
import agent from 'superagent';
import querystring from 'querystring';

export class HongYang {
  public logoutUrl(ctx: Koa.ParameterizedContext, config: any): string {
    const params = config.params || {};
    const alias = config.alias || {};

    if (alias.callbackUri) {
      params[alias.callbackUri] = ctx.origin + config.callbackUri;
    }
    if (alias.returnUri) {
      params[alias.returnUri] = ctx.href;
    }

    return `${config.redirectUri}?${querystring.stringify(params)}`;
  }

  public deleteToken(ctx: Koa.ParameterizedContext, config: any) {
    try {
      agent
        .delete(`${config.logoutUri}`)
        .set({ Authorization: `Bearer ${ctx.session.accessToken}` })
        .then(res => {})
        .catch(c => {
          console.log('delete-token-error', c);
        });
    } catch (error) {
      console.log('catch', error);
    }
  }
}
