import Koa from 'koa';
import agent from 'superagent';
import querystring from 'querystring';

export class HuaYangNian {
  public logoutUrl(ctx: Koa.ParameterizedContext, config: any): string {
    const params = config.params || {};
    const alias = config.alias || {};

    if (alias.callbackUri) {
      params[alias.callbackUri] = ctx.origin + config.callbackUri;
    }
    if (alias.returnUri) {
      params[alias.returnUri] = ctx.href;
    }

    return `${config.redirectUri}?${querystring.stringify(params)}`;
  }

  public async deleteToken(ctx: Koa.ParameterizedContext, config: any) {
    await agent
      .delete(`${config.logoutUri}/${config.params['appId']}`)
      .set('Authorization', `Bearer ${ctx.session.accessToken}`)
      .then(res => {});
  }
}
