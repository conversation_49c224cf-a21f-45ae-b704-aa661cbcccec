import Koa from 'koa';
import { HuaYangNian } from './implements/huayangnian';
import { Nippon } from './implements/nippon';
import { Default } from './implements/default';
import { environment } from '../environment';
import { getSsoConfig } from '../utils/sso-config';
import { HongYang } from './implements/hongyang';

class SSOService {
  get switchServcie(): any {
    let service: any = new Default();
    switch (environment.sso.type) {
      case 'nippon':
        service = new Nippon();
        break;
      case 'huayangnian':
        service = new HuaYangNian();
        break;
      case 'hongyang':
        service = new HongYang();
        break;
    }

    return service;
  }

  deleteToken(ctx: Koa.ParameterizedContext) {
    this.switchServcie.deleteToken(ctx, getSsoConfig(ctx));
  }

  getLogoutUrl(ctx: Koa.ParameterizedContext) {
    return this.switchServcie.logoutUrl(ctx, getSsoConfig(ctx));
  }
}

export const sso = new SSOService();
