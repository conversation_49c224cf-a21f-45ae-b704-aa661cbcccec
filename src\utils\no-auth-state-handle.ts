import Koa from 'koa';
import querystring from 'querystring';
import { environment } from '../environment';
import { getSsoConfig } from './sso-config';

export function noStateHandle(ctx: Koa.Context): boolean {
  if (!ctx.session || !ctx.session.user) {
    const refererUrl = ctx.header['referer'] && ctx.header['referer'] !== '/' ? new URL(ctx.header['referer'] + '') : undefined;
    const params = refererUrl ? { referer: refererUrl.pathname + refererUrl.search } : undefined;
    const ssoConfig = getSsoConfig(ctx);

    console.log('ssoConfig', JSON.stringify(ssoConfig));
    let ssoUrl = '';
    if (ssoConfig) {
      if (refererUrl) {
        ssoUrl = `${ssoConfig.redirectUri}${ssoConfig.loginUri}` + encodeURI(`?callBackUrl=${ssoConfig.callbackUri}`);
      } else {
        ssoUrl = `${ssoConfig.redirectUri}${ssoConfig.loginUri}`;
      }
    }
    let uri = `${ssoConfig ? ssoUrl : '/login'}`;
    if (params) {
      uri = `${uri}?${querystring.stringify(params)}`;
    }

    if (ssoConfig) {
      ctx.status = (ctx.path === `/${environment.urlKey}/auth/state` || ctx.path === '/auth/state') ? 308 : 440;
      ctx.body = uri;
    } else {
      ctx.status = (ctx.path === `/${environment.urlKey}/auth/state` || ctx.path === '/auth/state') ? 401 : 440;
      ctx.body = uri;
    }
    console.log('uir========>', uri);
    return true;
  }

  return false;
}
