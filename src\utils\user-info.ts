import Koa from 'koa';
import agent from 'superagent';
import { environment } from '../environment';
import { setUserState } from './user-state';

export async function getUserInfo(ctx: Koa.ParameterizedContext, account: string) {
  await agent
    .post(`${environment.apiGateway.uri}${environment.noNeedAuthPaths['state']}/${account}`)
    .set(ctx.header)
    .then((res: agent.Response) => {
      setUserState(ctx, res.body);
    });
}
