import Koa from 'koa';
import { getSsoConfig } from './sso-config';

export function setUserState(ctx: Koa.ParameterizedContext, user: any) {
  if (ctx.session) {
    if (user.accessToken) {
      ctx.session.accessToken = user.accessToken;
      delete user.accessToken;
    }

    const ssoConfig = getSsoConfig(ctx);
    if (ssoConfig && ssoConfig.refreshTokenKey) {
      if (user[ssoConfig.refreshTokenKey]) {
        ctx.session[ssoConfig.refreshTokenKey] = user[ssoConfig.refreshTokenKey];
        delete user[ssoConfig.refreshTokenKey];
      }
    }

    ctx.session.user = Object.assign({ language: ctx.headers['lang'] || 'zh' }, user);
  }
}

export function removeUserState(ctx: Koa.ParameterizedContext) {
  if (ctx.session) {
    ctx.session.user = undefined;
  }
}
